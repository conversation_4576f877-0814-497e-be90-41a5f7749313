/* 会员查询 */
import request from '@/utils/request';

export default {
    data() {
        return {
            memberShip: null, // 会员信息
            isMember: false, // 是不是会员(到期也不是会员)
            memberEnd: '', // 会员到期时间
        }
    },
    mounted() {
        this.getInfo();
    },
    methods: {
        getInfo() {
            request({
                url: 'getInfo',
                method: 'get',
            }).then((userInfo) => {
                console.log('会员信息', userInfo);
                let membership = userInfo['membership'];
                if (membership) {
                    this.memberShip = membership
                    this.memberEnd = membership.endDate;
                    this.isMemberEnd();
                } else {
                    this.isMember = false;
                    this.memberShip = null;
                    this.memberEnd = ''
                }
            });
        },
        isMemberEnd() {
            // 会员是否过期
            const today = new Date();
            const year = today.getFullYear();
            const month = (today.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的
            const day = today.getDate().toString().padStart(2, '0');
            const currentDate = `${year}-${month}-${day}`;

            // 判断会员是否过期
            const date1 = new Date(currentDate);
            const date2 = new Date(this.memberEnd);
            this.isMember = date1 > date2 ? false : true;
        }
    }
}
