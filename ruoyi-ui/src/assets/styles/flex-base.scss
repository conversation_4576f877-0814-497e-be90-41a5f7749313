/* flex布局 */
@each $direction in column, row {
    .flex-#{$direction} {
        display: flex;
        flex-direction: #{$direction};
        flex-wrap: wrap;
    }
}

/* flex布局 -- 对齐方式 */
@each $justifycontent in center, space-between, flex-start, flex-end {
    .jc-#{$justifycontent} {
        justify-content: #{$justifycontent};
    }
}

/* flex布局 -- 内容对齐方式 */
@each $alignitems in flex-start, flex-end, center, baseline, stretch {
    .ai-#{$alignitems} {
        align-items: #{$alignitems};
    }
}

/* 溢出省略号 */
.text-over {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@each $clamp in 2, 3, 4, 5 {
    .text-over#{$clamp} {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: #{$clamp};
        -webkit-box-orient: vertical;
    }
}

@each $clamp in left, center, right {
    .text-#{$clamp} {
        text-align: #{$clamp};
    }
}

/* 生成不同的外边距和内边距 */
$values: 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44;
@each $value in $values {
    .mt-#{$value} {
        margin-top: $value + px;
    }
}

@each $value in $values {
    .mb-#{$value} {
        margin-bottom: $value + px;
    }
}

@each $value in $values {
    .ml-#{$value} {
        margin-left: $value + px;
    }
}

@each $value in $values {
    .mr-#{$value} {
        margin-right: $value + px;
    }
}

@each $value in $values {
    .pt-#{$value} {
        padding-top: $value + px;
    }
}

@each $value in $values {
    .pb-#{$value} {
        padding-bottom: $value + px;
    }
}

@each $value in $values {
    .pl-#{$value} {
        padding-left: $value + px;
    }
}

@each $value in $values {
    .pr-#{$value} {
        padding-right: $value + px;
    }
}

@each $value in $values {
    .plr-#{$value} {
        padding-left: $value + px;
        padding-right: $value + px;
    }
}

@each $value in $values {
    .ptb-#{$value} {
        padding-top: $value + px;
        padding-bottom: $value + px;
    }
}

@each $value in $values {
    .mlr-#{$value} {
        margin-left: $value + px;
        margin-right: $value + px;
    }
}

@each $value in $values {
    .mtb-#{$value} {
        margin-top: $value + px;
        margin-bottom: $value + px;
    }
}

@each $value in $values {
    .m-#{$value} {
        margin: $value + px;
    }
}

@each $value in $values {
    .p-#{$value} {
        padding: $value + px;
    }
}

.my-container {
    width: 1500px;
}

// 不同分类的颜色
@for $i from 1 through 10 {
    .categories#{$i} {
        background-color: hsl(210 + ($i * 30), 70%, 50%) !important; // 从橙色色调开始，逐渐变化
        color: white !important; // 白色字体
    }
}
