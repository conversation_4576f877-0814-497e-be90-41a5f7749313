import request from "@/utils/request";

const developerModel = {
    namespaced: true,
    state: {
        list: [],
        info: {},
    },
    mutations: {
        SET_LIST: (state, list) => {
            state.list = list
        },
        // 如果需要存储搜索参数
        SET_INFO: (state, info) => {
            state.info = info
        }
    },
    actions: {
        // 修改后的loadList action
        async loadList({commit, state}, params) {
            try {
                const response = await request({
                    url: '/dev/verification/getPage',
                    method: 'get',
                    params: params || state.searchParams // 使用传入参数或state中的参数
                })
                console.info('获取开发者认证列表:', response)
                commit('SET_LIST', response.rows || [])

                if (response.rows.length === 0) {
                    commit('SET_INFO', {})
                } else {
                    commit('SET_INFO', response.rows[0])
                }
                return response // 可以返回响应数据供组件使用
            } catch (error) {
                console.error('获取开发者认证列表失败:', error)
                throw error // 抛出错误以便组件可以捕获处理
            }
        }
    }
}

export default developerModel
