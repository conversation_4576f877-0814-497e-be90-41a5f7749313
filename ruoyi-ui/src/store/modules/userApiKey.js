import {get<PERSON>y<PERSON><PERSON>} from "@/api/web/index.js";
import {
    getApi<PERSON><PERSON>,
    setApi<PERSON>ey
} from "@/utils/auth";

const userApiKey = {
    state: {
        apiKey: getApiKey()
    },

    mutations: {
        SET_API_KEY: (state, key) => {
            state.apiKey = key;
        }
    },

    actions: {
        // 登录
        GetApiKey({commit}) {
            return new Promise((resolve, reject) => {
                getMyKey()
                    .then((res) => {
                        let key = (res.data || [])[0]?.secretKey
                        setApiKey(key);
                        commit("SET_API_KEY", key);
                        return resolve(key);
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },


    },
};

export default userApiKey;
