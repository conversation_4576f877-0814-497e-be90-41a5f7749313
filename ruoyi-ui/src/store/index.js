import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import dict from './modules/dict'
import user from './modules/user'
import userApiKey from './modules/userApiKey'
import userModel from './modules/userModel'
import tagsView from './modules/tagsView'
import permission from './modules/permission'
import settings from './modules/settings'
import getters from './getters'
import developerModel from "./modules/developer";

Vue.use(Vuex)

const store = new Vuex.Store({
    modules: {
        app,
        dict,
        user,
        userApiKey,
        userModel,
        developerModel,
        tagsView,
        permission,
        settings
    },
    getters
})

export default store
