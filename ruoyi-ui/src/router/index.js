import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
 noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
 title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
 icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
 breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
 activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
 }
 */

// 公共路由
export const constantRoutes = [
    {
        path: "/redirect",
        component: Layout,
        hidden: true,
        children: [
            {
                path: "/redirect/:path(.*)",
                component: () => import("@/views/redirect"),
            },
        ],
    },
    {
        path: "/login",
        component: () => import("@/views/login"),
        hidden: true,
    },
    {
        path: "/register",
        component: () => import("@/views/register"),
        hidden: true,
    },
    {
        path: "/password",
        component: () => import("@/views/password"),
        hidden: true,
    },
    {
        path: "/oauth",
        component: () => import("@/views/web/oauth"),
        hidden: true,
    },
    {
        path: "/404",
        component: () => import("@/views/error/404"),
        hidden: true,
    },
    {
        path: "/401",
        component: () => import("@/views/error/401"),
        hidden: true,
    },

    {
        path: "",
        redirect: "agent",
    },
    {
        path: "/models",
        component: () => import("@/views/layout"),
        meta: {title: "模型", icon: "dashboard"},
        hidden: true,
        children: [
            {
                path: "",
                redirect: "plaza",
            },
            {
                path: "plaza",
                component: () => import("@/views/web/models/plaza"),
                name: "plaza",
                meta: {title: "模型广场", icon: "dashboard"},
            }
        ],
    },
    {
        path: "/playground",
        component: () => import("@/views/layout"),
        meta: {title: "体验中心", icon: "dashboard"},
        hidden: true,
        children: [
            {
                path: "",
                redirect: "chat",
            },
            {
                path: "chat",
                component: () => import("@/views/web/playground/chat"),
                name: "chat",
                meta: {title: "文本对话", icon: "dashboard"},
            }
        ],
    },
    {
        path: "/account",
        component: () => import("@/views/layout"),
        meta: {title: "账户管理", icon: "dashboard"},
        hidden: true,
        children: [
            {
                path: "",
                redirect: "authentication",
            },
            {
                path: "authentication",
                component: () => import("@/views/web/account/authentication"),
                name: "authentication",
                meta: {title: "实名认证", icon: "dashboard"},
            },
            {
                path: "ak",
                component: () => import("@/views/web/account/ak"),
                name: "ak",
                meta: {title: "API密钥", icon: "dashboard"},
            },
            {
                path: "expensebill",
                component: () => import("@/views/web/account/expensebill"),
                name: "expensebill",
                meta: {title: "余额充值", icon: "dashboard"},
            },
            {
                path: "bills",
                component: () => import("@/views/web/account/bills"),
                name: "bills",
                meta: {title: "费用账单", icon: "dashboard"},
            },
            {
                path: "invoice",
                component: () => import("@/views/web/account/invoice"),
                name: "invoice",
                meta: {title: "发票开具", icon: "dashboard"},
            },
            {
                path: "authentication/personal",
                component: () => import("@/views/web/account/auth-personal"),
                name: "personal",
                meta: {title: "个人认证", icon: "dashboard"},
            },
            {
                path: "authentication/org",
                component: () => import("@/views/web/account/auth-org"),
                name: "org",
                meta: {title: "企业认证", icon: "dashboard"},
            },
            {
                path: "bank",
                component: () => import("@/views/web/account/bank-list"),
                name: "bank",
                meta: {title: "银行卡管理", icon: "dashboard"},
            }
        ],
    },
    {
        path: "/aiadmin",
        component: Layout,
        redirect: "/aiadmin/index",
        hidden: true,
        children: [
            {
                path: "index",
                component: () => import("@/views/admin/index"),
                name: "AdminIndex",
                meta: {title: "管理后台", icon: "dashboard", affix: true},
            },
        ],
    },
    {
        path: "/user",
        component: Layout,
        hidden: true,
        redirect: "noredirect",
        children: [
            {
                path: "profile",
                component: () => import("@/views/system/user/profile/index"),
                name: "Profile",
                meta: {title: "个人中心", icon: "user"},
            },
        ],
    },
    {
        path: "/agent",
        component: () => import("@/views/layout"),
        meta: {title: "智能体", icon: "dashboard"},
        hidden: true,
        children: [
            {
                path: "",
                redirect: "square",
            },
            {
                path: "square",
                component: () => import("@/views/web/agent/square"),
                name: "square",
                meta: {title: "智能体广场", icon: "dashboard"},
            }
        ],
    },
    {
        path: "/develop",
        component: () => import("@/views/layout"),
        meta: {title: "开发平台", icon: "dashboard"},
        hidden: true,
        children: [
            {
                path: "",
                redirect: "develop-center",
            },
            {
                path: "develop-center",
                component: () => import("@/views/web/develop-platform/developer-center"),
                name: "develop-center",
                meta: {title: "开发者中心", icon: "dashboard"},
            },
            {
                path: "my-agent",
                component: () => import("@/views/web/develop-platform/my-agent"),
                name: "my-agent",
                meta: {title: "我的智能体", icon: "dashboard"},
            },
            {
                path: "my-earnings",
                component: () => import("@/views/web/develop-platform/my-earnings"),
                name: "my-earnings",
                meta: {title: "我的收益", icon: "dashboard"},
            }
        ],
    },
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
    {
        path: "/system/user-auth",
        component: Layout,
        hidden: true,
        permissions: ["system:user:edit"],
        children: [
            {
                path: "role/:userId(\\d+)",
                component: () => import("@/views/system/user/authRole"),
                name: "AuthRole",
                meta: {title: "分配角色", activeMenu: "/system/user"},
            },
        ],
    },
    {
        path: "/system/role-auth",
        component: Layout,
        hidden: true,
        permissions: ["system:role:edit"],
        children: [
            {
                path: "user/:roleId(\\d+)",
                component: () => import("@/views/system/role/authUser"),
                name: "AuthUser",
                meta: {title: "分配用户", activeMenu: "/system/role"},
            },
        ],
    },
    {
        path: "/system/dict-data",
        component: Layout,
        hidden: true,
        permissions: ["system:dict:list"],
        children: [
            {
                path: "index/:dictId(\\d+)",
                component: () => import("@/views/system/dict/data"),
                name: "Data",
                meta: {title: "字典数据", activeMenu: "/system/dict"},
            },
        ],
    },
    {
        path: "/monitor/job-log",
        component: Layout,
        hidden: true,
        permissions: ["monitor:job:list"],
        children: [
            {
                path: "index/:jobId(\\d+)",
                component: () => import("@/views/monitor/job/log"),
                name: "JobLog",
                meta: {title: "调度日志", activeMenu: "/monitor/job"},
            },
        ],
    },
    {
        path: "/tool/gen-edit",
        component: Layout,
        hidden: true,
        permissions: ["tool:gen:edit"],
        children: [
            {
                path: "index/:tableId(\\d+)",
                component: () => import("@/views/tool/gen/editTable"),
                name: "GenEdit",
                meta: {title: "修改生成配置", activeMenu: "/tool/gen"},
            },
        ],
    },
];

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
let routerReplace = Router.prototype.replace;
// push
Router.prototype.push = function push(location) {
    return routerPush.call(this, location).catch((err) => err);
};
// replace
Router.prototype.replace = function push(location) {
    return routerReplace.call(this, location).catch((err) => err);
};

export default new Router({
    mode: "history", // 去掉url中的#
    scrollBehavior: () => ({y: 0}),
    routes: constantRoutes,
});
