<template>
    <div class="background-container">
        <div class="project-collaboration-form">
            <h2>项目合作孵化申请</h2>
            <div class="form-container">
                <el-form
                    label-width="80px"
                    :model="formData"
                    :rules="rules"
                    ref="formData"
                >
                    <el-row :gutter="20">
                        <el-col :span="12" :offset="0">
                            <el-form-item label="公司名称" prop="companyName">
                                <el-input
                                    v-model="formData.companyName"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" :offset="0" v-if="isShow">
                            <el-form-item label="产品名称" prop="productName">
                                <el-input
                                    v-model="formData.productName"
                                    readonly
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12" :offset="0">
                            <el-form-item
                                label="公司地址"
                                prop="companyAddress"
                            >
                                <el-cascader
                                    v-model="formData.companyAddress"
                                    :options="cities"
                                    :props="{
                                        expandTrigger: 'hover',
                                        value: 'label',
                                    }"
                                    filterable
                                    placeholder="请选择或搜索地区"
                                    clearable
                                ></el-cascader>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" :offset="0">
                            <el-input
                                v-model="companyDetailAddress"
                                placeholder="请输入详细地址"
                            ></el-input>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12" :offset="0">
                            <el-form-item label="姓名" prop="customerName">
                                <el-input
                                    v-model="formData.customerName"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" :offset="0">
                            <el-form-item label="邮箱" prop="email">
                                <el-input v-model="formData.email"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12" :offset="0">
                            <el-form-item label="电话号码" prop="phone">
                                <el-input v-model="formData.phone"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" :offset="0">
                            <el-form-item label="微信" prop="wechat">
                                <el-input v-model="formData.wechat"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12" :offset="0">
                            <el-form-item label="座机号码" prop="landline">
                                <el-input
                                    v-model="formData.landline"
                                    placeholder="例如：0400-8114012"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" :offset="0">
                            <el-form-item
                                label="信用代码"
                                prop="unifiedSocialCreditCode"
                            >
                                <el-input
                                    v-model="formData.unifiedSocialCreditCode"
                                    placeholder="请输入统一社会信用代码"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12" :offset="0">
                            <el-form-item label="项目名称" prop="projectName">
                                <el-input
                                    v-model="formData.projectName"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" :offset="0">
                            <el-form-item label="项目类型" prop="projectTypeId">
                                <el-select
                                    v-model="formData.projectTypeId"
                                    filterable
                                    allow-create
                                    default-first-option
                                    placeholder="请选择项目类型"
                                    clearable
                                >
                                    <el-option
                                        :label="item.projectType"
                                        :value="item.id"
                                        v-for="item in projectTypeList"
                                        :key="item.id"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-form-item label="项目描述" prop="projectDescription">
                        <el-input
                            v-model="formData.projectDescription"
                            type="textarea"
                            rows="5"
                            max-length="10"
                            placeholder="请输入项目描述"
                            show-word-limit
                        ></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="submitApplication('formData')"
                            >申请提交</el-button
                        >
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>
</template>

<script>
import { listProjectType } from "@/api/project/incubation";
import request from "@/utils/request";
import cities from "@/utils/cities.js";
export default {
    name: "Application",
    data() {
        return {
            // 表单数据
            formData: {
                companyName: "",
                companyAddress: null,
                landline: "",
                unifiedSocialCreditCode: "",
                phone: "",
                wechat: "",
                email: "",
                projectName: "",
                projectType: "",
                projectTypeId: "",
                projectDescription: "",
                type: "",
                productName: "",
            },
            // 表单验证
            rules: {
                companyName: [
                    {
                        required: true,
                        message: "请输入公司名称",
                        trigger: "blur",
                    },
                ],
                companyAddress: [
                    {
                        required: true,
                        message: "请输入公司地址",
                        trigger: "change",
                    },
                ],
                customerName: [
                    {
                        required: true,
                        message: "请输入您的姓名",
                        trigger: "blur",
                    },
                ],
                phone: [
                    {
                        required: true,
                        message: "请输入电话号码",
                        trigger: "blur",
                    },
                    {
                        pattern: /^1[3-9]\d{9}$/,
                        message: "手机号格式不正确",
                        trigger: "blur",
                    },
                ],
                // wechat: [{ required: true, message: "请输入微信号", trigger: "blur" }],
                email: [
                    {
                        required: false,
                        message: "请输入您的邮箱",
                        trigger: "blur",
                    },
                    {
                        pattern:
                            /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
                        message: "请输入正确的邮箱地址",
                        trigger: ["blur", "change"],
                    },
                ],
                // projectName: [
                //   { required: true, message: "请输入项目名称", trigger: "blur" },
                // ],
                projectTypeId: [
                    {
                        required: false,
                        message: "请选择项目类型",
                        trigger: "blur",
                    },
                ],
                projectDescription: [
                    {
                        required: false,
                        message: "请输入项目描述",
                        trigger: "blur",
                    },
                    {
                        min: 5,
                        max: 300,
                        message: "请输入5-300个字符",
                        trigger: ["blur", "change"],
                    },
                ],
                landline: [
                    {
                        required: false,
                        message: "请输入固话号码",
                        trigger: "blur",
                    },
                    {
                        pattern:
                            /^(?:(?:\d{3}-)?\d{8}|^(?:\d{4}-)?\d{7,8})(?:-\d+)?$/,
                        message: "请输入正确的座机号码",
                        trigger: ["blur", "change"],
                    },
                ],
                unifiedSocialCreditCode: [
                    {
                        required: false,
                        message: "请输入统一社会信用代码",
                        trigger: "blur",
                    },
                    {
                        pattern:
                            /^(([0-9A-Za-z]{15})|([0-9A-Za-z]{18})|([0-9A-Za-z]{20}))$/,
                        message: "请输入正确的统一社会信用代码",
                        trigger: ["blur", "change"],
                    },
                ],
                // type: [{ required: true, message: "请输入文件类型", trigger: "blur" }],
            },
            // 地区列表
            cities: cities,
            // 详细地址
            companyDetailAddress: "",
            // 项目类型
            projectTypeList: [],
            isShow: false,
        };
    },
    mounted() {
        this.getProjectType();
        if (this.$route.query.productName) {
            this.isShow = true;
            this.formData.productName = this.$route.query.productName;
            this.formData.spuId = this.$route.query.spuId;
            this.formData.skuId = this.$route.query.skuId;
        }
    },
    methods: {
        async submitApplication(formRef) {
            if (this.formData.companyAddress) {
                this.formData.companyAddress =
                    this.formData.companyAddress.join("/");
                this.formData.companyAddress += "-" + this.companyDetailAddress;
            } else {
                this.formData.companyAddress =
                    this.formData.companyDetailAddress;
            }
            this.$refs[formRef].validate((valid) => {
                if (valid) {
                    request({
                        url: "http://**********:80/prod-api/admin/projectApplicationReview/saveProject", //AI智选后台接口
                        // url: "http://**************:8080/admin/projectApplicationReview/saveProject",
                        method: "post",
                        data: this.formData,
                    }).then((response) => {
                        // 清空表单
                        this.$refs[formRef].resetFields();
                        this.$message({
                            type: "success",
                            message: "提交成功!",
                        });
                    });
                } else {
                    this.$message({
                        type: "error",
                        message: "提交失败!",
                    });
                }
            });
            // try {
            //   // 构建请求体;
            //   const form = new FormData();
            //   for (const key in this.formData) {
            //     form.append(key, this.formData[key]);
            //   }

            // 添加文件到FormData
            // if (this.files && this.files.length > 0) {
            //   this.files.forEach((file) => {
            //     formData.append("files", file);
            //   });
            // }
            // 发送数据到服务器
            // const response = await request({
            //   url: "/app/projectApplication/saveProject", // 替换为实际的API地址
            //   method: "post",
            //   data: form,
            // });
            // 成功后处理
            // console.log("Success:", response.data);
            //   alert("Application submitted successfully!");
            // } catch (error) {
            //   // 错误处理
            //   console.error("Error submitting application:", error);
            //   alert("Failed to submit the application. Please try again later.");
            // }
        },
        // 去掉地址逗号
        replaceAddress($event, v) {
            if (!$event) {
                this.formData.companyAddress =
                    this.formData.companyAddress.join("/");
                // 用斜杠拼接地址
                return this.formData.companyAddress;
            }
        },
        // 填写详细地址后拼接地址
        mergeAddress() {
            this.formData.companyAddress =
                this.formData.companyAddress.join("/");
            this.formData.companyAddress += "/" + this.companyDetailAddress;
        },
        //获取项目类型
        getProjectType() {
            listProjectType().then((response) => {
                this.projectTypeList = response.data;
            });
        },
    },
};
</script>

<style scoped>
.background-container {
    display: flex;
    justify-content: center;
    align-items: center;
    background-image: url("../assets/images/application_bc.png");
    background-size: cover;
    height: calc(100vh - 74px);
}
.project-collaboration-form {
    /* margin-top: 30px; */
    max-width: 680px;
    border-radius: 10px;
    background-color: rgb(255, 255, 255, 0.7);
    /* &:hover {
      box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.2);
    } */
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.2);
}
.project-collaboration-form h2 {
    text-align: center;
}
label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

input[type="text"],
input[type="tel"],
input[type="url"],
textare a {
    height: 35px;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
}
input[type="file"] {
    margin-top: 5px;
}
.el-form {
    padding: 10px 20px;
}
.el-form-item {
    margin-left: 20px;
}

.el-input {
    min-width: 200px;
    height: 20px;
}

button {
    margin-left: calc(50% - 100px);
    background-color: rgb(253, 84, 90);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
}
</style>
