<template>
    <div class="login-wrap flex-row">
        <div class="item left flex-column jc-center">
            <div class="_item desc-box flex-row ai-center">
                <el-image src="https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/_upload/84b6329d-91e4-4587-8e2c-dd1e2690d282.png"
                          style="width: 36px;height: 36px; border-radius: 6px;"></el-image>
            </div>
            <div class="_item _item2">彩翼智能体应用广场</div>
            <div class="_item _item2" style="margin-top: -15px">加速 AGI 普惠人类</div>
            <div class="_item">基于优秀的开源模型，提供高性价比的 GenAI 云服务</div>
            <div class="_item" style="margin-top: -15px;">文本对话 · 图像生成 · 视频生成 · 语音合成</div>
        </div>
        <div class="item right flex-row jc-center ai-center">
            <div class="login-right">
                <div class="login-title">注册账号</div>
                <el-form :model="registerForm" status-icon :rules="rules" ref="registerForm" class="demo-registerForm"
                         :status-icon="false">
                    <el-form-item prop="username">
                        <el-input v-model="registerForm.username" autocomplete="off"
                                  placeholder="请输入手机号">
                        </el-input>
                    </el-form-item>
                    <el-form-item prop="nickname">
                        <el-input v-model="registerForm.nickname" autocomplete="off"
                                  placeholder="请输入昵称">
                        </el-input>
                    </el-form-item>
                    <el-form-item prop="password">
                        <el-input v-model="registerForm.password" autocomplete="off"
                                  placeholder="请输入密码" type="password">
                        </el-input>
                    </el-form-item>
                    <el-form-item prop="code">
                        <div class="el-item-code">
                            <el-input v-model.trim="registerForm.code" placeholder="请输入验证码"></el-input>
                            <div class="_code" @click.stop="getCode">
                                {{ btnText }}
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item>
                        <div class="login-btn" @click="handleRegister()">注册</div>
                    </el-form-item>
                    <el-form-item>
                        <div class="to-other" @click="goPage('/login')">已有账号？<span>去登录</span></div>
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>
</template>
<script>
import {getCodeImg, register} from "@/api/login";
import request from '@/utils/request'

export default {
    name: "login",
    data() {
        let PhoneValidator = (rule, value, callback) => {
            if (!value) {
                return callback()
            } else {
                const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
                if (!reg.test(value)) {
                    callback(new Error('请输入正确手机号'))
                    return
                }
                callback()
            }
        }

        return {
            btnText: '获取验证码',
            canGetCode: true,
            seconds: 60,
            registerForm: {
                code: "",
                password: "",
                username: "",
                nickname: "",
            },
            rules: {
                username: [
                    {required: true, message: '请输入手机号', trigger: 'blur'},
                    {validator: PhoneValidator, trigger: 'blur'}
                ],
                nickname: [
                    {required: true, message: '请输入昵称', trigger: 'blur'},
                ],
                password: [
                    {required: true, message: '请输入登录密码', trigger: 'blur'},
                    {min: 6, max: 12, message: '长度在 6 到 12 个字符', trigger: 'blur'}
                ],
                code: [
                    {required: true, message: '请输入验证码', trigger: 'blur'},
                ],
            },
            loading: false,
            captchaEnabled: true
        }
    },
    methods: {
        handleRegister() {
            this.$refs.registerForm.validate(valid => {
                if (valid) {
                    this.loading = true;
                    register(this.registerForm).then(res => {
                        const username = this.registerForm.username;
                        this.$alert("<font color='red'>恭喜你，您的账号 " + username + " 注册成功！</font>", '系统提示', {
                            dangerouslyUseHTMLString: true,
                            type: 'success'
                        }).then(() => {
                            this.$router.push("/login");
                        }).catch(() => {
                        });
                    }).catch(() => {
                        this.loading = false;
                        if (this.captchaEnabled) {
                            // this.getCode();
                        }
                    })
                }
            });
        },
        goPage(path) {
            this.$router.push(path)
        },
        getCode() {
            if (this.registerForm.username) {
                if (this.canGetCode) {
                    this.canGetCode = false;
                    this.sendSms();

                    this.countdown(60, () => {
                        this.canGetCode = true;
                    })
                } else {
                    this.$message.error(`倒计时结束后再发送!`);
                }
            } else {
                this.$message.error(`请填写手机号!`);
            }
        },
        countdown(seconds, callback) {

            if (seconds <= 0) {
                callback();
                this.btnText = '重新获取';
                return;
            }
            this.btnText = seconds + '秒';
            setTimeout(() => this.countdown(seconds - 1, callback), 1000);
        },
        sendSms() {
            request({
                url: '/sms/send',
                method: 'get',
                params: {
                    phone: this.registerForm.username
                }
            }).then(response => {
                console.log('eeeee', response)
            })
        }
    }
}
</script>
<style lang="scss">
@import '@/assets/styles/flex-base.scss';

.login-wrap {
    width: 100vw;
    height: 100vh;
    min-width: 800px;
    min-height: 600px;
    background: url("https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/login_bg.jpg") no-repeat center;
    background-size: cover;
    box-sizing: border-box;

    .item {
        width: 50%;
        height: 100%;

        &.left {
            //background: url("https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/login.png") no-repeat center;
            background-size: cover;
            box-sizing: border-box;
            padding: 40px;
            gap: 25px;

            ._item {
                padding-left: 2%;
                font-weight: 550;
                color: #fff;
                font-size: 18px;
                letter-spacing: 2px;

                &.desc-box {
                    gap: 10px;
                    padding-bottom: 12px;

                    span {
                        font-weight: 550;
                        color: #fff;
                        font-size: 30px;
                    }
                }
            }

            ._item2 {
                font-size: 36px;
            }
        }

        &.right {
            .login-right {
                width: 310px;
                padding: 20px;
                background: #fff;
                border-radius: 5px 5px 0 0;

                .login-title {
                    font-size: 24px;
                    color: #333;
                    text-align: center;
                    margin-bottom: 32px;
                }

                .el-form-item {
                    .el-input__inner {
                        height: 40px !important;
                    }

                    .jzmm {
                        gap: 4px;
                        color: rgba(0, 0, 0, 0.80);
                    }

                    .login-btn {
                        width: 100%;
                        height: 40px;
                        line-height: 40px;
                        background: rgba(230, 162, 60, 1);
                        color: #fff;
                        border-radius: 8px;
                        font-weight: 500;
                        font-size: 16px;
                        letter-spacing: 2px;
                        text-align: center;
                        cursor: pointer;

                        &:hover {
                            background: rgba(230, 162, 60, 0.8);
                        }
                    }

                    .to-other {
                        font-size: 12px;
                        color: #939099;

                        span {
                            cursor: pointer;

                            &:hover {
                                color: rgba(230, 162, 60, 1);
                            }
                        }
                    }
                }

                .el-item-code {
                    position: relative;
                    box-sizing: border-box;

                    .el-input__inner {

                        padding-right: 200px;
                    }

                    ._code {
                        position: absolute;
                        right: 1px;
                        top: 12px;
                        bottom: 12px;
                        padding: 0 12px;
                        color: #FFA459;
                        font-size: 14px;
                        border-left: 1px solid #eee;
                        line-height: 16px;
                        cursor: pointer;
                        background: #FFFFFF;
                    }
                }
            }
        }
    }

    .el-input__inner {
        &:focus {
            border-color: #e6a23c !important;
        }
    }
}
</style>
