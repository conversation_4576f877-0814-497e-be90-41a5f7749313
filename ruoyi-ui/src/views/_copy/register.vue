<template>
    <div class="login-box flex-row ai-center jc-center">
        <el-card class="box-card">
            <div class="login-title">注册账号</div>

            <el-form :model="registerForm" status-icon :rules="rules" ref="registerForm" class="demo-registerForm"
                     :status-icon="false">
                <el-form-item prop="username">
                    <el-input v-model="registerForm.username" autocomplete="off"
                              placeholder="请输入手机号">
                    </el-input>
                </el-form-item>
                <el-form-item prop="password">
                    <el-input v-model="registerForm.password" autocomplete="off"
                              placeholder="请输入密码" type="password">
                    </el-input>
                </el-form-item>
                <el-form-item prop="code">
                    <div class="el-item-code">
                        <el-input v-model.trim="registerForm.code" placeholder="请输入验证码"></el-input>
                        <div class="_code" @click.stop="getCode">
                            {{ btnText }}
                        </div>
                    </div>
                </el-form-item>
                <el-form-item>
                    <div class="login-btn" @click="handleRegister()">注册</div>
                </el-form-item>
                <el-form-item>
                    <div class="to-other" @click="goPage('/login')">已有账号？<span>去登录</span></div>
                </el-form-item>
            </el-form>
        </el-card>
    </div>
</template>
<script>
import {getCodeImg, register} from "@/api/login";
import request from '@/utils/request'

export default {
    name: "login",
    data() {
        let PhoneValidator = (rule, value, callback) => {
            if (!value) {
                return callback()
            } else {
                const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
                if (!reg.test(value)) {
                    callback(new Error('请输入正确手机号'))
                    return
                }
                callback()
            }
        }

        return {
            btnText: '获取验证码',
            canGetCode: true,
            seconds: 60,
            registerForm: {
                code: "",
                password: "",
                username: "",
            },
            rules: {
                username: [
                    {required: true, message: '请输入手机号', trigger: 'blur'},
                    {validator: PhoneValidator, trigger: 'blur'}
                ],
                password: [
                    {required: true, message: '请输入登录密码', trigger: 'blur'},
                    {min: 6, max: 12, message: '长度在 6 到 12 个字符', trigger: 'blur'}
                ],
                code: [
                    {required: true, message: '请输入验证码', trigger: 'blur'},
                ],
            },
            loading: false,
            captchaEnabled: true
        }
    },
    methods: {
        handleRegister() {
            this.$refs.registerForm.validate(valid => {
                if (valid) {
                    this.loading = true;
                    register(this.registerForm).then(res => {
                        const username = this.registerForm.username;
                        this.$alert("<font color='red'>恭喜你，您的账号 " + username + " 注册成功！</font>", '系统提示', {
                            dangerouslyUseHTMLString: true,
                            type: 'success'
                        }).then(() => {
                            this.$router.push("/login");
                        }).catch(() => {
                        });
                    }).catch(() => {
                        this.loading = false;
                        if (this.captchaEnabled) {
                            // this.getCode();
                        }
                    })
                }
            });
        },
        goPage(path) {
            this.$router.push(path)
        },
        getCode() {
            if (this.registerForm.username) {
                if (this.canGetCode) {
                    this.canGetCode = false;
                    this.sendSms();

                    this.countdown(60, () => {
                        this.canGetCode = true;
                    })
                } else {
                    this.$message.error(`倒计时结束后再发送!`);
                }
            } else {
                this.$message.error(`请填写手机号!`);
            }
        },
        countdown(seconds, callback) {

            if (seconds <= 0) {
                callback();
                this.btnText = '重新获取';
                return;
            }
            this.btnText = seconds + '秒';
            setTimeout(() => this.countdown(seconds - 1, callback), 1000);
        },
        sendSms() {
            request({
                url: '/sms/send',
                method: 'get',
                params: {
                    phone: this.registerForm.username
                }
            }).then(response => {
                console.log('eeeee', response)
            })
        }
    }
}
</script>
<style lang="scss">
@import '@/assets/styles/flex-base.scss';

.login-box {
    width: 100vw;
    height: 100vh;
    min-width: 800px;
    min-height: 600px;
    //background: url("@/assets/images/web_login_bg.png") no-repeat center;
    background-size: 100% auto;
    margin: 0 auto;

    .box-card {
        width: 480px;
        //height: 506px;
        box-sizing: border-box;
        padding: 24px 42px;

        .login-title {
            font-weight: 550;
            font-size: 24px;
            color: #000000;
            line-height: 48px;
            height: 48px;
            text-align: center;
            margin-bottom: 32px;
        }

        .demo-registerForm {
            .el-input__inner {
                height: 46px !important;
            }

            .login-btn {
                width: 100%;
                height: 46px;
                text-align: center;
                line-height: 46px;
                font-size: 20px;
                color: #FFFFFF;
                background: #FFA459;
                border-radius: 5px;
                cursor: pointer;
                margin-top: 24px;
            }

            .to-other {
                text-align: center;
                font-size: 18px;
                color: #000;

                span {
                    cursor: pointer;
                    color: #FFA459;
                }
            }

            .el-item-code {
                position: relative;
                box-sizing: border-box;

                .el-input__inner {

                    padding-right: 200px;
                }

                ._code {
                    position: absolute;
                    right: 1px;
                    top: 12px;
                    bottom: 12px;
                    padding: 0 12px;
                    color: #FFA459;
                    font-size: 16px;
                    border-left: 1px solid #eee;
                    line-height: 22px;
                    cursor: pointer;
                    background: #FFFFFF;
                }
            }
        }
    }
}
</style>
