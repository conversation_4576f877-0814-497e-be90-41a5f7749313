<template>
    <div class="login-box flex-row ai-center jc-center">
        <el-card class="box-card">
            <div class="login-title">登录</div>

            <el-form
                :model="loginForm"
                status-icon
                :rules="loginRules"
                ref="loginForm"
                class="demo-loginForm"
            >
                <el-form-item prop="username">
                    <el-input
                        v-model="loginForm.username"
                        autocomplete="off"
                        placeholder="请输入手机号"
                    ></el-input>
                </el-form-item>
                <el-form-item prop="password">
                    <el-input
                        type="password"
                        v-model="loginForm.password"
                        autocomplete="off"
                        placeholder="请输入密码"
                        @keyup.enter.native="handleLogin"
                    ></el-input>
                </el-form-item>

                <el-form-item>
                    <div>
                        <input
                            type="checkbox"
                            id="rememberMe"
                            v-model="rememberMe"
                        />
                        <label for="rememberMe">记住密码</label>
                    </div>
                    <div
                        class="login-btn"
                        @click="handleLogin()"
                        :loading="loading"
                    >
                        登录
                    </div>
                </el-form-item>
                <el-form-item>
                    <div class="to-other flex-row jc-space-between">
                        <span @click="goPage('/register')">去注册</span>
                        <span @click="goPage('/password')">忘记密码</span>
                    </div>
                </el-form-item>
            </el-form>
        </el-card>
    </div>
</template>
<script>
import {getCodeImg} from "@/api/login";
import Cookies from "js-cookie";
import {encrypt, decrypt} from "@/utils/jsencrypt";

export default {
    name: "login",
    data() {
        return {
            rememberMe: false,
            loginForm: {
                code: "8888",
                password: "",
                username: "",
            },
            loginRules: {
                username: [
                    {
                        required: true,
                        trigger: "blur",
                        message: "请输入您的账号",
                    },
                ],
                password: [
                    {
                        required: true,
                        trigger: "blur",
                        message: "请输入您的密码",
                    },
                ],
                code: [
                    {
                        required: true,
                        trigger: "change",
                        message: "请输入验证码",
                    },
                ],
            },
            loading: false,
            // 验证码开关
            captchaEnabled: true,
            // 注册开关
            register: false,
            redirect: undefined,
        };
    },
    mounted() {
        // 初始化时从 localStorage 中读取用户名和密码
        const savedUsername = localStorage.getItem("username");
        const savedPassword = localStorage.getItem("password");

        if (savedUsername) {
            this.loginForm.username = savedUsername;
        }
        if (savedPassword) {
            this.loginForm.password = savedPassword;
        }
    },
    watch: {
        $route: {
            handler: function (route) {
                this.redirect = route.query && route.query.redirect;
                console.log(route, this.redirect);
            },
            immediate: true,
        },
    },
    methods: {
        getCookie() {
            const username = Cookies.get("username");
            const password = Cookies.get("password");
            const rememberMe = Cookies.get("rememberMe");
            this.loginForm = {
                username:
                    username === undefined ? this.loginForm.username : username,
                password:
                    password === undefined
                        ? this.loginForm.password
                        : decrypt(password),
                rememberMe:
                    rememberMe === undefined ? false : Boolean(rememberMe),
            };
        },
        handleLogin() {
            if (this.rememberMe) {
                // 记住用户名和密码
                localStorage.setItem("username", this.loginForm.username);
                localStorage.setItem("password", this.loginForm.password);
            } else {
                // 不记住密码时清空 localStorage
                localStorage.removeItem("username");
                localStorage.removeItem("password");
            }
            this.$refs.loginForm.validate((valid) => {
                if (valid) {
                    this.loading = true;
                    if (this.loginForm.rememberMe) {
                        Cookies.set("username", this.loginForm.username, {
                            expires: 30,
                        });
                        Cookies.set(
                            "password",
                            encrypt(this.loginForm.password),
                            {expires: 30}
                        );
                        Cookies.set("rememberMe", this.loginForm.rememberMe, {
                            expires: 30,
                        });
                    } else {
                        Cookies.remove("username");
                        Cookies.remove("password");
                        Cookies.remove("rememberMe");
                    }
                    this.$store
                        .dispatch("Login", this.loginForm)
                        .then(() => {
                            this.$router
                                .push({path: this.redirect || "/"})
                                .catch(() => {
                                });
                        })
                        .catch(() => {
                            this.loading = false;
                            if (this.captchaEnabled) {
                                // this.getCode();
                            }
                        });
                }
            });
        },
        goPage(path) {
            this.$router.push(path);
        },
    },
};
</script>
<style lang="scss">
@import "@/assets/styles/flex-base.scss";

.login-box {
    width: 100vw;
    height: 100vh;
    min-width: 800px;
    min-height: 600px;
    //background: url("@/assets/images/web_login_bg.png") no-repeat center;
    background-size: 100% auto;
    margin: 0 auto;

    .box-card {
        width: 480px;
        //height: 506px;
        box-sizing: border-box;
        padding: 24px 42px;

        .login-title {
            font-weight: 550;
            font-size: 24px;
            color: #000000;
            line-height: 48px;
            height: 48px;
            text-align: center;
            margin-bottom: 32px;
        }

        .demo-loginForm {
            .el-input__inner {
                height: 46px !important;
            }

            .login-btn {
                width: 100%;
                height: 46px;
                text-align: center;
                line-height: 46px;
                font-size: 20px;
                color: #ffffff;
                background: #ffa459;
                border-radius: 5px;
                cursor: pointer;
                margin-top: 24px;
            }

            .to-other {
                font-size: 16px;

                span {
                    cursor: pointer;
                    color: #ffa459;
                }
            }
        }
    }
}
</style>
