<template>
    <div class="login-wrap flex-row">
        <div class="item left flex-column jc-center">
            <div class="_item desc-box flex-row ai-center">
                <el-image src="https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/_upload/84b6329d-91e4-4587-8e2c-dd1e2690d282.png"
                          style="width: 36px;height: 36px; border-radius: 6px;"></el-image>
            </div>
            <div class="_item _item2">彩翼智能体应用广场</div>
            <div class="_item _item2" style="margin-top: -15px">加速 AGI 普惠人类</div>
            <div class="_item">基于优秀的开源模型，提供高性价比的 GenAI 云服务</div>
            <div class="_item" style="margin-top: -15px;">文本对话 · 图像生成 · 视频生成 · 语音合成</div>
        </div>
        <div class="item right flex-row jc-center ai-center">
            <div class="login-right">
                <div class="login-title">欢迎登录</div>
                <el-form
                    :model="loginForm"
                    status-icon
                    :rules="loginRules"
                    ref="loginForm"
                    class="demo-loginForm"
                >
                    <el-form-item prop="username">
                        <el-input
                            v-model="loginForm.username"
                            autocomplete="off"
                            placeholder="请输入手机号"
                        ></el-input>
                    </el-form-item>
                    <el-form-item prop="password">
                        <el-input
                            type="password"
                            v-model="loginForm.password"
                            autocomplete="off"
                            placeholder="请输入密码"
                            @keyup.enter.native="handleLogin"
                        ></el-input>
                    </el-form-item>

                    <el-form-item>
                        <div class="flex-row ai-center jzmm">
                            <input
                                type="checkbox"
                                id="rememberMe"
                                v-model="rememberMe"
                            />
                            <label for="rememberMe">记住密码</label>
                        </div>
                        <div class="login-btn" @click="handleLogin()" :loading="loading">
                            登录
                        </div>
                        <div class="to-other flex-row jc-space-between">
                            <span @click="goPage('/register')">去注册</span>
                            <span @click="goPage('/password')">忘记密码</span>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>
</template>
<script>
import {getCodeImg} from "@/api/login";
import Cookies from "js-cookie";
import {encrypt, decrypt} from "@/utils/jsencrypt";

export default {
    name: "login",
    data() {
        return {
            rememberMe: false,
            loginForm: {
                code: "8888",
                password: "",
                username: "",
            },
            loginRules: {
                username: [
                    {
                        required: true,
                        trigger: "blur",
                        message: "请输入您的账号",
                    },
                ],
                password: [
                    {
                        required: true,
                        trigger: "blur",
                        message: "请输入您的密码",
                    },
                ],
                code: [
                    {
                        required: true,
                        trigger: "change",
                        message: "请输入验证码",
                    },
                ],
            },
            loading: false,
            // 验证码开关
            captchaEnabled: true,
            // 注册开关
            register: false,
            redirect: undefined,
        };
    },
    mounted() {
        // 初始化时从 localStorage 中读取用户名和密码
        const savedUsername = localStorage.getItem("username");
        const savedPassword = localStorage.getItem("password");

        if (savedUsername) {
            this.loginForm.username = savedUsername;
        }
        if (savedPassword) {
            this.loginForm.password = savedPassword;
        }
    },
    watch: {
        $route: {
            handler: function (route) {
                this.redirect = route.query && route.query.redirect;
                console.log(route, this.redirect);
            },
            immediate: true,
        },
    },
    methods: {
        getCookie() {
            const username = Cookies.get("username");
            const password = Cookies.get("password");
            const rememberMe = Cookies.get("rememberMe");
            this.loginForm = {
                username:
                    username === undefined ? this.loginForm.username : username,
                password:
                    password === undefined
                        ? this.loginForm.password
                        : decrypt(password),
                rememberMe:
                    rememberMe === undefined ? false : Boolean(rememberMe),
            };
        },
        handleLogin() {
            if (this.rememberMe) {
                // 记住用户名和密码
                localStorage.setItem("username", this.loginForm.username);
                localStorage.setItem("password", this.loginForm.password);
            } else {
                // 不记住密码时清空 localStorage
                localStorage.removeItem("username");
                localStorage.removeItem("password");
            }
            this.$refs.loginForm.validate((valid) => {
                if (valid) {
                    this.loading = true;
                    if (this.loginForm.rememberMe) {
                        Cookies.set("username", this.loginForm.username, {
                            expires: 30,
                        });
                        Cookies.set(
                            "password",
                            encrypt(this.loginForm.password),
                            {expires: 30}
                        );
                        Cookies.set("rememberMe", this.loginForm.rememberMe, {
                            expires: 30,
                        });
                    } else {
                        Cookies.remove("username");
                        Cookies.remove("password");
                        Cookies.remove("rememberMe");
                    }
                    this.$store
                        .dispatch("Login", this.loginForm)
                        .then(() => {
                            this.$router
                                .push({path: this.redirect || "/"})
                                .catch(() => {
                                });
                        })
                        .catch(() => {
                            this.loading = false;
                            if (this.captchaEnabled) {
                                // this.getCode();
                            }
                        });
                }
            });
        },
        goPage(path) {
            this.$router.push(path);
        },
    },
};
</script>
<style lang="scss">
@import "@/assets/styles/flex-base.scss";

.login-wrap {
    width: 100vw;
    height: 100vh;
    min-width: 800px;
    min-height: 600px;
    background: url("https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/login_bg.jpg") no-repeat center;
    background-size: cover;
    box-sizing: border-box;

    .item {
        width: 50%;
        height: 100%;

        &.left {
            //background: url("https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/login.png") no-repeat center;
            background-size: cover;
            box-sizing: border-box;
            padding: 40px;
            gap: 25px;

            ._item {
                padding-left: 2%;
                font-weight: 550;
                color: #fff;
                font-size: 18px;
                letter-spacing: 2px;

                &.desc-box {
                    gap: 10px;
                    padding-bottom: 12px;

                    span {
                        font-weight: 550;
                        color: #fff;
                        font-size: 30px;
                    }
                }
            }

            ._item2 {
                font-size: 36px;
            }
        }

        &.right {
            .login-right {
                width: 310px;
                padding: 20px;
                background: #fff;
                border-radius: 5px 5px 0 0;

                .login-title {
                    font-size: 24px;
                    color: #333;
                    text-align: center;
                    margin-bottom: 32px;
                }

                .el-form-item {
                    .el-input__inner {
                        height: 40px !important;
                    }

                    .jzmm {
                        gap: 4px;
                        color: rgba(0, 0, 0, 0.80);
                    }

                    .login-btn {
                        width: 100%;
                        height: 40px;
                        line-height: 40px;
                        background: rgba(230, 162, 60, 1);
                        color: #fff;
                        border-radius: 8px;
                        font-weight: 500;
                        font-size: 16px;
                        letter-spacing: 2px;
                        text-align: center;
                        cursor: pointer;

                        &:hover {
                            background: rgba(230, 162, 60, 0.8);
                        }
                    }

                    .to-other {
                        font-size: 12px;
                        color: #939099;

                        span {
                            cursor: pointer;

                            &:hover {
                                color: rgba(230, 162, 60, 1);
                            }
                        }
                    }
                }
            }
        }
    }

    .el-input__inner {
        &:focus {
            border-color: #e6a23c !important;
        }
    }
}
</style>
