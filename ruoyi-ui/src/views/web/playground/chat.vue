<template>

    <div class="chat">
        <div class="chat-body">
            <el-row :gutter="5" class="chat-body-content">
                <el-col :span="6" class="chat-body-left">
                    <h1>编排</h1>
                    <el-card class="box-card orchestrate-body"
                             :body-style="{height:'calc(100% - 80px)',padding:'5px 5px 5px 15px'}">
                        <div slot="header" class="clearfix orchestrate-body-header">
                            <span>提示词</span>
                        </div>
                        <el-input class="prompt-input"
                                  type="textarea"
                                  v-model="prompt"
                                  rows="20"
                                  :autosize="{minRows: 20}"
                                  show-word-limit
                                  placeholder="在这里写你的提示词"
                                  maxlength="512"
                                  resize="vertical"
                        ></el-input>
                    </el-card>
                </el-col>
                <el-col :span="18" class="chat-body-right">
                    <el-card class="box-card use-model-body" :body-style="{height:'calc(100% - 80px)'}">
                        <div slot="header" class="clearfix" shadow="never">
                            <span>调试与预览</span>
                            <div class="use-model-header-menu">
                                <el-button class="add-use-btn" size="mini" type="text" icon="el-icon-plus"
                                           @click="addUse()">
                                    {{ "添加模型（" + useModelList.length + "/" + maxUseModel + "）" }}
                                </el-button>
                                <el-divider direction="vertical"></el-divider>
                                <el-button title="重新开始" class="refresh-use-btn-btn" size="mini" type="text"
                                           icon="el-icon-refresh-left" @click="refreshUse()"></el-button>

                            </div>


                        </div>
                        <el-row type="flex" class="use-model-content">
                            <el-col v-for="(useModel,index) in useModelList" :span="24/useModelList.length">
                                <el-card class="use-model" :body-style="{height:'calc(100% - 80px)'}">
                                    <div slot="header" class="use-model-header clearfix">
                                        <span class="use-model-number">#{{ index }}</span>
                                        <el-popover
                                            width="400"
                                            trigger="click">
                                            <el-form ref="form" label-position="top" label-width="80px" size="mini">
                                                <el-form-item label="模型" class="use-model-param-item">
                                                    <el-select class="use-model-select" v-model="useModel.model.info"
                                                               placeholder="请选择"
                                                               @change="((val)=>changeModel(val,useModel))"
                                                    >
                                                        <el-option-group
                                                            v-for="(group,type) in groupList"
                                                            :key="type"
                                                            :label="type">
                                                            <el-option
                                                                v-for="(item,index) in group"
                                                                :label="item.name"
                                                                :value="[type,index]">
                                                            </el-option>
                                                        </el-option-group>
                                                    </el-select>
                                                </el-form-item>
                                                <template v-for="(param,key) in useModel">
                                                    <el-form-item
                                                        v-if="param.type==='number'"
                                                        :label="param.title" class="use-model-param-item">
                                                        <el-slider :name="key"
                                                                   v-model="param.value"
                                                                   :precision="param.precision"
                                                                   :max="param.max"
                                                                   :min="param.min"
                                                                   :step="param.step"
                                                                   show-input
                                                                   :show-input-controls=false
                                                                   input-size="mini"
                                                        >
                                                        </el-slider>
                                                    </el-form-item>
                                                </template>
                                            </el-form>
                                            <el-link type="warning" :underline=false slot="reference">
                                                {{ useModel.model.name || "请选择模型" }}<i
                                                class="el-icon-arrow-down el-icon--right"></i>
                                            </el-link>
                                        </el-popover>
                                        <el-dropdown class="use-model-menu" size="mini"
                                                     @command="handleUseModelMenuCommand">
                                            <el-button type="text" icon="el-icon-menu">
                                            </el-button>
                                            <el-dropdown-menu slot="dropdown" class="use-model-menu-body">
                                                <el-dropdown-item v-if="useModelList.length<4"
                                                                  :command="{menu:'copy',index:index}">复制模型
                                                </el-dropdown-item>
                                                <el-dropdown-item v-if="useModelList.length>1"
                                                                  :command="{menu:'single',index:index}">单一模型
                                                </el-dropdown-item>
                                                <el-dropdown-item v-if="useModelList.length>1"
                                                                  :command="{menu:'remove',index:index}">移除
                                                </el-dropdown-item>
                                            </el-dropdown-menu>
                                        </el-dropdown>
                                    </div>
                                    <div class="use-model-window">
                                        <div class="use-model-chat" :id="'use-model-chat-'+useModel.id">
                                            <div v-for="(message) in useModel.historyMessages">
                                                <el-row class="chat-message user-message" v-if="message.role === 'user'"
                                                        type="flex"
                                                        justify="end">
                                                    <div class="content">
                                                        <div class="text" v-html="message.content"></div>
                                                    </div>
                                                    <el-image :src="userAvatar" class="avatar">
                                                    </el-image>
                                                </el-row>
                                                <el-row class="chat-message ai-message" type="flex" v-else>
                                                    <el-image :src="aiAvatar" class="avatar">
                                                    </el-image>
                                                    <div class="content">
                                                        <el-collapse v-if="message.reasoningState!==-1"
                                                                     v-model="reasoningActiveNames">
                                                            <el-collapse-item
                                                                :title="(message.reasoningState===1?'已深度思考':'深度思考中')+'('+message.reasoningTime+'s)'"
                                                                :name="message.reasoningState">
                                                                <div class="text"
                                                                     v-html="message.displayReasoningContent ||message.reasoningContent"></div>
                                                            </el-collapse-item>
                                                        </el-collapse>
                                                        <div class="text"
                                                             v-html="message.displayContent || message.content"></div>
                                                        <div v-if="message.loading" class="loading-indicator">
                                                            <i class="el-icon-loading"></i>
                                                        </div>
                                                    </div>
                                                </el-row>
                                            </div>
                                        </div>

                                    </div>
                                </el-card>
                            </el-col>
                        </el-row>
                        <el-input placeholder="请输入内容"
                                  v-model="userInput"
                                  @keyup.enter.native="sendMessage"
                        >
                            <el-button slot="append" title="发送" icon="el-icon-s-promotion"
                                       @click="sendMessage"></el-button>
                        </el-input>
                    </el-card>
                </el-col>
            </el-row>


        </div>
    </div>
</template>
<script>
import {marked} from "marked"
import DOMPurify from "dompurify";
import {getModelList, chatCompletions} from "@/api/web"

export default {
    name: "chat",
    data() {
        return {
            reasoningActiveNames: 0,
            chatData: {},
            groupList: {LLM: [], VLM: []},
            useModelList: [],
            maxUseModel: 4,
            sendClient: null,
            userInput: "",
            userAvatar: "https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/default_avatar.jpg",
            aiAvatar: "https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/ai_avatar.jpg",
            prompt: "",
            paramsList: {
                model: {
                    title: "模型",
                    type: 'model',
                    describe: "",
                    value: "",
                    name: "",
                    info: null
                },
                max_tokens: {
                    title: "最大标记",
                    type: 'number',
                    describe: null,
                    min: 1,
                    max: 8192,
                    step: 1,
                    precision: null,
                    value: 2048,
                }, temperature: {
                    title: "温度",
                    type: 'number',
                    describe: null,
                    min: 0,
                    max: 2,
                    step: 0.1,
                    precision: null,
                    value: 0.6,
                }, top_p: {
                    title: "Top P",
                    type: 'number',
                    describe: null,
                    min: 0,
                    max: 1,
                    step: 0.1,
                    precision: null,
                    value: 0.95,
                }, top_k: {
                    title: "Top K",
                    type: 'number',
                    describe: null,
                    min: 0,
                    max: 100,
                    step: 1,
                    precision: null,
                    value: 20,
                }, frequency_penalty: {
                    title: "频率惩罚",
                    type: 'number',
                    describe: null,
                    min: -2,
                    max: 2,
                    step: 0.1,
                    precision: null,
                    value: 0.0,
                }
            },
            searchParams: { // 查询的参数
                categoryIds: [1]
            },

        }
    },
    mounted() {
        this.getModelList();
    },
    methods: {
        handleUseModelMenuCommand(command) {

            switch (command.menu) {
                case  "copy":
                    this.copyUse(command.index);
                    break;
                case "single":
                    this.singleUse(command.index);
                    break;
                case "remove":
                    this.removeUse(command.index);
                    break;
                default:
                    break;
            }
        },
        getModelList() {
            getModelList(this.searchParams).then(response => {
                let modelList = response.data || [];
                this.groupList.VLM = modelList.filter(n => n.vision === '1');
                this.groupList.LLM = modelList.filter(n => n.vision !== '1');
                this.addUse();
            })
        },
        newUse(useModel) {
            let newModel = JSON.parse(JSON.stringify(useModel || this.paramsList));
            newModel.historyMessages = [];
            newModel.id = this.useModelList.length;
            return newModel
        },
        addUse() {
            if (this.useModelList.length < this.maxUseModel) {
                this.useModelList.push(this.newUse());
            }

        },
        refreshUse() {
            this.useModelList.forEach(useModel => {
                useModel.historyMessages = [];
            })
        },
        copyUse(index) {
            this.useModelList.push(this.newUse(this.useModelList[index]))
        },
        singleUse(index) {
            this.useModelList = [this.useModelList[index]]
        },
        removeUse(index) {
            this.useModelList.splice(index, 1)
        },
        changeModel(val, useModel) {
            useModel.model.info = val;
            let model = this.getSelectedModel(val);
            useModel.model.value = model.apiName;
            useModel.model.name = model.name;
        },
        getSelectedModel(val) {
            return this.groupList[val[0]][val[1]]
        },
        getChatParams(prompt, message, useModel) {
            let params = {
                stream: true,
                messages:
                    [{role: "system", content: prompt}]
            };

            useModel.historyMessages.filter(message => message.content).forEach(message => {
                params.messages.push({role: message.role, content: message.content})
            })
            for (let param in useModel) {
                let value = useModel[param].value
                if (value) {
                    params[param] = value;
                }
            }
            return params;
        },
        pushMessages(useModel, message) {
            useModel.historyMessages.push(message)
            this.scrollToBottom(useModel)
        },
        sendMessage() {
            this.useModelList.filter(n => n.model.value).forEach(useModel => {
                const userMessage = {
                    role: 'user',
                    content: this.userInput
                };

                this.pushMessages(useModel, userMessage);
                const aiMessage = {
                    role: 'assistant',
                    content: '',
                    displayContent: '',
                    reasoningStartTime: new Date(),
                    reasoningTime: 0.0,
                    reasoningEndTime: new Date(),
                    reasoningState: -1,
                    reasoningContent: '',
                    displayReasoningContent: '',
                    isThink: false,
                    loading: true
                };
                this.pushMessages(useModel, aiMessage);
                chatCompletions(this.getChatParams(this.prompt, this.userInput, useModel)).then(response => {
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let fullContent = '';

                    this.readStream(useModel, reader, decoder, aiMessage, fullContent);
                })
            })
            this.userInput = "";
        },
        async readStream(useModel, reader, decoder, message, fullContent) {
            while (true) {
                const {done, value} = await reader.read();
                if (done) {
                    break; // 流结束
                }

                // 解码数据
                const chunk = decoder.decode(value, {stream: true});
                if (chunk.startsWith('{"msg":')) {
                    this.$message({message: JSON.parse(chunk).msg, type: 'error'})
                    return
                }
                // 处理SSE格式数据
                const lines = chunk.split('\n').filter(line => line.trim() !== '');
                for (const line of lines) {
                    const data = line.startsWith('data:') ? line.substring(5) : line;
                    if (!data) {
                        continue;
                    } else if (data === '[DONE]') {
                        message.loading = false;
                        continue;
                    }
                    try {
                        const parsed = JSON.parse(data);
                        if (parsed.choices[0]?.delta?.hasOwnProperty("reasoning_content")) {
                            const reasoningContent = parsed.choices[0]?.delta?.reasoning_content || '';
                            fullContent = this.outputReasoningContent(message, reasoningContent, fullContent);
                        }
                        if (parsed.choices[0]?.delta?.hasOwnProperty("content")) {
                            const content = parsed.choices[0]?.delta?.content || '';
                            if (content === '<think>') {
                                message.isThink = true;
                            }
                            if (message.isThink) {
                                fullContent = this.outputReasoningContent(message, content, fullContent);
                            } else {
                                fullContent = this.outputContent(message, content, fullContent);
                            }
                            if (content === '</think>') {
                                message.isThink = false;
                            }
                        }
                        this.scrollToBottom(useModel);
                    } catch (error) {
                        console.error('解析响应失败:', error);
                        console.log('解析响应失败数据:', data);
                    }
                }
            }
        },
        outputReasoningContent(message, content, fullContent) {
            fullContent += content;
            if (message.reasoningState === -1) {
                message.reasoningState = 0;

            }
            message.reasoningEndTime = new Date();
            message.reasoningTime = ((message.reasoningEndTime.getTime() - message.reasoningStartTime.getTime()) / 1000).toFixed(1);
            message.reasoningContent = fullContent;
            message.displayReasoningContent = this.formatContent(fullContent);
            return fullContent;

        },
        outputContent(message, content, fullContent) {
            if (message.reasoningState === 0) {
                message.reasoningState = 1;
                fullContent = "";
            }
            fullContent += content;
            message.content = fullContent;
            message.displayContent = this.formatContent(fullContent);
            return fullContent;
        },
        formatContent(content) {
            if (content) {
                const html = marked(content);
                return DOMPurify.sanitize(html);
            } else
                return content;
        },
        scrollToBottom(useModel) {
            const box = document.getElementById("use-model-chat-" + useModel.id);
            box.scrollTo(0, box.scrollHeight - box.clientHeight);
        }
    }
}
</script>

<style lang="scss">
@import "@/assets/styles/flex-base";

.el-scrollbar__view {
    height: 100%;
}

.avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
}

.chat-message {
    margin-top: 10px;
    font-size: 12px;
}

.chat-message .content {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 4px;
    max-width: calc(100% - 66px);
}

.chat-message .content .el-collapse {
    border: none;
}

.chat-message .content .el-collapse-item__header {
    height: 24px;
    background: none;
}

.chat-message .content .el-collapse-item__content {
    padding: 0 10px;
    font-size: 12px;
}

.user-message .content {
    background-color: #e6f7ff;
}

.ai-message .content {
    background-color: #f5f5f5;
}

.use-model-select {
    width: 100%;
}

.use-model-param-item {
    margin-bottom: 0 !important;
    padding: 0 10px;
}

.use-model-menu {
    float: right;
}

.use-model-menu button {
    padding: 0 !important;
    color: black;
}

.use-model-menu button:hover, .use-model-menu button:focus {
    color: #ffba00 !important;

}
.use-model-menu-body {
    width: 150px;
}

.prompt-input textarea {
    border: none;
    padding: 0;
    font-size: 12px;
}

.chat {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
    height: 100%;

    .chat-body {
        gap: 20px;
        box-sizing: border-box;
        height: 100%;

        .chat-body-content {
            height: 100%;
        }

        .chat-body-left {
            height: 100%;

        }

        .chat-body-right {
            height: 100%;
        }

        .box-card {
            box-shadow: none;

        }

        .orchestrate-body {
            height: 400px;
        }

        .orchestrate-body-header {
            font-size: 14px;
        }

        .use-model-body {
            height: 100%;
        }

        .use-model-header-menu {
            float: right;
        }

        .use-model-header-menu .el-button--text {
            color: #ffba00;
        }

        .use-model-content {
            height: calc(100% - 40px);
            padding-bottom: 10px;
        }

        .use-model {
            margin-right: 3px;
            box-shadow: none;
            height: 100%;
        }

        .use-model-window {
            height: 100%;
        }

        .use-model-header {
            text-align: center;
        }

        .use-model-number {
            float: left;
        }


        .use-model-chat {
            height: 100%;
            flex: 1;
            overflow-y: auto;
        }


    }
}

@media (max-width: 1200px) {
    .chat {
        .chat-body {
            ._right {
                ._item {
                    width: calc((100% - 20px) / 2);

                    &._item-open {
                        width: 100%;
                    }
                }
            }
        }
    }
}

@media (min-width: 1250px) {
    .chat {
        .chat-body {
            ._right {
                ._item {
                    width: calc((100% - 40px) / 3);

                    &._item-open {
                        width: calc((100% - 20px) / 2);
                    }
                }
            }
        }
    }
}

@media (min-width: 1680px) {
    .chat {
        .chat-body {
            ._right {
                ._item {
                    width: calc((100% - 60px) / 4);

                    &._item-open {
                        width: calc((100% - 40px) / 3);
                    }
                }
            }
        }
    }
}

.el-input__inner {
    &:focus {
        border-color: #e6a23c !important;
    }
}
</style>
