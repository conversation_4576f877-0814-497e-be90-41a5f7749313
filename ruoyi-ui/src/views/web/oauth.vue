<template>
    <div class="oauth-box flex-row jc-center ai-center">
        <div class="login-right">
            <div class="login-title flex-column ai-center jc-center">
                <el-image :src="agentInfo.img"
                          style="width: 36px;height: 36px; border-radius: 6px;"></el-image>
                <div class="title">{{ agentInfo.name }}</div>
            </div>
            <div class="login-title login-title2 flex-row ai-center jc-center">
                申请使用您的头像和昵称 <b v-if="!userInfo.userId">请登录</b>
            </div>
            <el-form
                v-if="!userInfo.userId"
                :model="loginForm"
                status-icon
                :rules="loginRules"
                ref="loginForm"
                class="demo-loginForm"
            >
                <el-form-item prop="username">
                    <el-input
                        v-model="loginForm.username"
                        autocomplete="off"
                        placeholder="请输入手机号"
                    ></el-input>
                </el-form-item>
                <el-form-item prop="password">
                    <el-input
                        type="password"
                        v-model="loginForm.password"
                        autocomplete="off"
                        placeholder="请输入密码"
                        @keyup.enter.native="handleLogin"
                    ></el-input>
                </el-form-item>

                <el-form-item>
                    <div class="login-btn" @click="handleLogin()" :loading="loading">
                        登录
                    </div>
                </el-form-item>
            </el-form>

            <template v-else>
                <div class="flex-row user-info ai-center">
                    {{avatar}}
                    <el-image :src="avatar"
                              style="width: 36px;height: 36px; border-radius: 6px;"></el-image>
                    <div class="__label">{{ userInfo.nickName }}</div>
                </div>

                <div class="flex-row user-info ai-center jc-center">
                    <el-button size="small" type="info" @click="logout">拒绝</el-button>
                    <el-button size="small" type="warning" @click="agentOpen">授权</el-button>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
import request from "../../utils/request";
import {agentAuthorization} from "../../api/web";
import {mapGetters} from "vuex";

export default {
    name: "oauth",
    data() {
        return {
            loginForm: {
                code: "8888",
                password: "",
                username: "",
            },
            loginRules: {
                username: [
                    {
                        required: true,
                        trigger: "blur",
                        message: "请输入您的账号",
                    },
                ],
                password: [
                    {
                        required: true,
                        trigger: "blur",
                        message: "请输入您的密码",
                    },
                ],
            },
            loading: false,
            agentId: '',
            agentInfo: {},
            userInfo: {}
        }
    },
    computed: {
        ...mapGetters([
            'avatar',
        ])
    },
    mounted() {
        // 如果没有用户信息就去加载用户信息
        let userInfo = this.$store.getters.userInfo;
        console.log('avatar', this.avatar)

        try {
            this.userInfo = JSON.parse(userInfo);
        } catch (e) {
            this.userInfo = {};
        }

        console.log(' this.userInfo', this.userInfo)

        let query = this.$route.query;

        if (query.agentId) {
            this.agentId = query.agentId
            this.loadAgent()
        }
    },
    methods: {
        loadAgent() {
            request({
                url: '/agent/market/getByAgentId/' + this.agentId,
                method: 'get',
                params: {
                    id: this.agentId
                }
            }).then(response => {
                console.log('智能体详情', response)
                this.agentInfo = response.data
            })
        },
        handleLogin() {
            this.$refs.loginForm.validate((valid) => {
                if (valid) {
                    this.loading = true;
                    this.$store
                        .dispatch("Login", this.loginForm)
                        .then((response) => {
                            this.$store.dispatch("GetInfo")
                                .then(() => {
                                    // 如果没有用户信息就去加载用户信息
                                    let userInfo = this.$store.getters.userInfo;

                                    try {
                                        this.userInfo = JSON.parse(userInfo);
                                    } catch (e) {
                                        this.userInfo = {};
                                    }
                                })
                        })
                        .catch(() => {
                            this.loading = false;
                            if (this.captchaEnabled) {
                                // this.getCode();
                            }
                        });
                }
            });
        },
        logout() {
            if (!window.close()) {
                alert("浏览器阻止了自动关闭，请手动关闭窗口。");
            } else {
                window.close()
            }
        },
        agentOpen() {
            agentAuthorization({
                agentId: this.agentId
            }).then(response => {
                console.log('response', response)
                window.location.replace(response);
            })
        }
    }
}
</script>


<style scoped lang="scss">
@import "@/assets/styles/flex-base.scss";

.oauth-box {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    min-width: 1200px;
    min-height: 500px;

    .login-right {
        width: 310px;
        padding: 20px;
        background: #fff;
        border-radius: 5px 5px 0 0;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);

        .login-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 12px;
            gap: 10px;

            &.login-title2 {
                color: #939099;
            }
        }

        .demo-loginForm {
            margin-top: 32px;
        }

        .el-form-item {
            .el-input__inner {
                height: 40px !important;
            }

            .login-btn {
                width: 100%;
                height: 40px;
                line-height: 40px;
                background: rgba(230, 162, 60, 1);
                color: #fff;
                border-radius: 8px;
                font-weight: 500;
                font-size: 16px;
                letter-spacing: 2px;
                text-align: center;
                cursor: pointer;

                &:hover {
                    background: rgba(230, 162, 60, 0.8);
                }
            }
        }

        .user-info {
            width: 100%;
            height: 88px;
            border-top: 1px solid #eee;
            gap: 12px;
        }
    }
}
</style>
