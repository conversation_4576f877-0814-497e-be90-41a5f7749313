<template>
    <div class="authentication" v-if="loadEnd">
        <template v-if="authInfo.verificationId">
            <div class="auth-header" :class="'auth-header' + authInfo.verificationStatus">
                <div class="item" v-if="authInfo.verificationStatus == 1">已完成实名认证</div>
                <div class="item" v-else-if="authInfo.verificationStatus == 2">实名认证未通过</div>
                <div class="item" v-else-if="authInfo.verificationStatus == 0">实名认证待审核</div>
            </div>

            <div class="auth-info flex-column" v-if="authInfo.verificationType == '0'">
                <div class="_item flex-row jc-space-between" style="margin-bottom: 20px;">
                    <div class="_left" v-if="authInfo.verificationStatus == 1">您已完成实名认证</div>
                    <div class="_left" v-if="authInfo.verificationStatus == 2">您的实名认证未通过</div>
                    <div class="_left" v-if="authInfo.verificationStatus == 0">您的实名认证待审核</div>
                    <template v-if="authInfo.verificationStatus != 0">
                        <div class="_right flex-row ai-center" style="gap: 10px;">
                            <el-link style="color: #e6a23c;" @click="goPage('/account/authentication/personal')">
                                修改个人认证信息
                            </el-link>
                            <el-link style="color: #e6a23c;" @click="goPage('/account/authentication/org')">
                                变更为企业用户
                            </el-link>
                        </div>
                    </template>
                </div>

                <el-descriptions size="mini" labelClassName="custom-desc-label">
                    <el-descriptions-item label="认证状态">
                        {{ handleDict(3, authInfo.verificationStatus) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="认证类型">
                        {{ handleDict(2, authInfo.verificationType) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="真实姓名">
                        {{ authInfo.individualVerification.realName }}
                    </el-descriptions-item>
                    <el-descriptions-item label="证件类型">
                        {{ handleDict(1, authInfo.individualVerification.idType) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="证件号码">{{ authInfo.individualVerification.idNumber }}
                    </el-descriptions-item>
                    <el-descriptions-item label="认证时间">{{ authInfo.createTime }}
                    </el-descriptions-item>
                </el-descriptions>
            </div>

            <div class="auth-info flex-column" v-if="authInfo.verificationType == '1'">
                <div class="_item flex-row jc-space-between" style="margin-bottom: 20px;">
                    <div class="_left" v-if="authInfo.verificationStatus == 1">您已完成企业认证</div>
                    <div class="_left" v-if="authInfo.verificationStatus == 2">您的企业认证未通过</div>
                    <div class="_left" v-if="authInfo.verificationStatus == 0">您的企业认证待审核</div>
                    <template v-if="authInfo.verificationStatus != 0">
                        <div class="_right flex-row ai-center" style="gap: 10px;">
                            <el-link style="color: #e6a23c;" @click="goPage('/account/authentication/org')">
                                修改企业认证信息
                            </el-link>
                            <el-link style="color: #e6a23c;" @click="goPage('/account/authentication/personal')">
                                变更为个人用户
                            </el-link>
                        </div>
                    </template>
                </div>

                <el-descriptions size="mini" labelClassName="custom-desc-label">
                    <el-descriptions-item label="认证状态">
                        {{ handleDict(3, authInfo.verificationStatus) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="认证类型">
                        {{ handleDict(2, authInfo.verificationType) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="企业名称">
                        {{ authInfo.enterpriseVerification.companyName }}
                    </el-descriptions-item>
                    <el-descriptions-item label="统一社会信用代码">
                        {{ handleDict(1, authInfo.enterpriseVerification.companyCodeNum) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="法人姓名">{{ authInfo.enterpriseVerification.legalPersonName }}
                    </el-descriptions-item>
                    <el-descriptions-item label="法人身份证号">
                        {{ authInfo.enterpriseVerification.legalPersonNum }}
                    </el-descriptions-item>
                    <el-descriptions-item label="认证时间">
                        {{ authInfo.createTime }}
                    </el-descriptions-item>
                </el-descriptions>
            </div>
        </template>
        <template v-else>
            <div class="auth-header">
                <div class="item">为了您的账号及资金安全，请尽早完成实名认证</div>
                <div class="item">企业、机构用户请勿使用个人认证</div>
            </div>

            <div class="auth-body flex-row">
                <div class="item">
                    <div class="top">个人认证</div>
                    <div class="desc">适用于个人用户，账号归属个人</div>
                    <div class="flex-row ai-center go" @click="goPage('/account/authentication/personal')">
                        <span>立即认证</span>
                        <i class="el-icon-right"></i>
                    </div>
                </div>

                <div class="item">
                    <div class="top">企业认证</div>
                    <div class="desc">适用于企业、个体工商户、事业单位、学校等，账号归属于企业。</div>
                    <div class="flex-row ai-center go" @click="goPage('/account/authentication/org')">
                        <span>立即认证</span>
                        <i class="el-icon-right"></i>
                    </div>
                </div>
            </div>
        </template>

        <div class="auth-tip flex-row ai-center jc-center" v-if="GiftBalance>0">
            <span>完成认证后，立即获取</span>
            <span style="color: #e6a23c;font-size: 20px;">{{ GiftBalance }}</span>
            <span>元赠金！</span>
        </div>
    </div>
</template>
<script>
import {getGiftBalance, getVerificationInfo} from "@/api/web";

export default {
    dicts: ['certificate_type', 'user_verification_type', 'user_verification_status'],
    data() {
        return {
            GiftBalance: '', // 赠送的额度
            authInfo: {
                individualVerification: {}
            },
            loadEnd: false
        }
    },
    created() {
        this.loadEnd = false;
        this.loadData();
    },
    methods: {
        goPage(url) {
            this.$router.push(url)
        },
        loadData() {
            getGiftBalance().then(response => {
                console.log('赠送额度', response)
                this.GiftBalance = response.msg;
            })
            getVerificationInfo().then(response => {
                console.log('我的认证信息', response)
                this.loadEnd = true;
                this.authInfo = response.data || {}
            })
        },
        handleDict(type, value) { //user_verification_status
            let item = {};

            switch (type) {
                case 1: // 证件类型
                    item = this.dict.type.certificate_type.find(n => n.value == value)
                    break;
                case 2: // 用户认证类型
                    item = this.dict.type.user_verification_type.find(n => n.value == value)
                    break;
                case 3: // 用户认证状态
                    item = this.dict.type.user_verification_status.find(n => n.value == value)
                    break;
            }

            return item ? item.label : '';
        }
    }
}
</script>

<style lang="scss">
@import "@/assets/styles/flex-base";

.authentication {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;

    .auth-header {
        width: 900px;
        background: #fffbe6;
        border: 1px solid #ffe58f;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 14px;
        box-sizing: border-box;

        .item {
            position: relative;
            line-height: 24px;
            margin-left: 12px;
            font-size: 12px;
            color: #626066;

            &:before {
                position: absolute;
                content: '';
                width: 4px;
                height: 4px;
                border-radius: 4px;
                background: #000;
                left: -12px;
                top: 50%;
                margin-top: -2px;
            }
        }

        &.auth-header1 {
            background: rgb(225, 243, 216);
            border-color: #67C23A;
        }

        &.auth-header2 {
            background: rgb(254, 240, 240);
            border-color: #F56C6C;
        }
    }

    .auth-body {
        padding: 20px 0;
        box-sizing: border-box;
        gap: 20px;

        .item {
            width: 370px;
            border: 1px solid #ededed;
            border-radius: 8px;
            box-sizing: border-box;
            padding: 10px;

            .top {
                font-size: 1.125rem !important;
                line-height: 1.75rem !important;
                font-weight: 500 !important;
                margin-bottom: 8px;
            }

            .desc {
                color: #999;
                font-size: .75rem !important;
                line-height: 1rem !important;
            }

            .go {
                color: #e6a23c;
                font-size: .75rem !important;
                line-height: 1rem !important;
                padding-top: 20px;
                gap: 4px;
                cursor: pointer;
            }
        }
    }

    .auth-info {
        width: 900px;
        margin-top: 20px;
        padding: 20px;
        box-sizing: border-box;
        border: 1px solid #eeeeee;
        border-radius: 4px;
    }

    .custom-desc-label {
        color: #939099;
    }

    .auth-tip {
        margin-top: 24px;
        width: 900px;
        padding: 12px 0;
        height: 20px;
        background: rgb(253, 246, 236);
        border-radius: 5px;
        font-size: 12px;
        color: #313033;
    }
}
</style>
