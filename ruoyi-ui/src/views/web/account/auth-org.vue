<template>
    <div class="authentication-personal">
        <div class="auth-header">
            <div class="item">填写企业或组织名称与统一社会信用代码，法定代表人姓名与身份证号码。</div>
        </div>

        <div class="auth-body">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm"
                     label-position="top">
                <el-form-item label="企业名称" prop="companyName">
                    <el-input v-model="ruleForm.companyName" placeholder="请输入企业名称"></el-input>
                </el-form-item>
                <el-form-item label="统一社会信用代码" prop="companyCodeNum">
                    <el-input v-model="ruleForm.companyCodeNum" placeholder="请输入统一社会信用代码"></el-input>
                </el-form-item>
                <el-form-item label="法人姓名" prop="legalPersonName">
                    <el-input v-model="ruleForm.legalPersonName" placeholder="请输入法人姓名"></el-input>
                </el-form-item>
                <el-form-item label="法人身份证号" prop="legalPersonNum">
                    <el-input v-model="ruleForm.legalPersonNum" placeholder="请输入法人身份证号"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button style="width: 100%;" type="warning" @click="submitForm('ruleForm')">提交认证</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script>
import {certification, getGiftBalance, getVerificationInfo} from "@/api/web";

export default {
    name: "auth-personal",
    data() {
        return {
            ruleForm: {
                "companyCodeNum": "",
                "companyName": "",
                "enterpriseId": null,
                "legalPersonName": "",
                "legalPersonNum": "",
            },
            rules: {
                companyName: [
                    {required: true, message: '请输入企业名称', trigger: 'blur'},
                ],
                companyCodeNum: [
                    {required: true, message: '请输入统一社会信用代码', trigger: 'blur'}
                ],
                legalPersonName: [
                    {required: true, message: '请输入法人姓名', trigger: 'blur'},
                ],
                legalPersonNum: [
                    {required: true, message: '请输入法人身份证号', trigger: 'blur'}
                ],
            }
        }
    },
    created() {
        getVerificationInfo().then(response => {
            console.log('我的认证信息', response)
            let info = response.data || {};

            if (info.enterpriseVerification) {
                Object.keys(this.ruleForm).forEach(key => {
                    this.ruleForm[key] = info.enterpriseVerification[key]
                })
            }
        })
    },
    methods: {
        submitForm() {
            this.$refs.ruleForm.validate((valid) => {
                if (valid && !this.isDisabled) {
                    this.isDisabled = true;
                    certification({
                        enterpriseInfo: this.ruleForm,
                        verificationType: 1
                    }).then(response => {
                        if (response.code === 200) {
                            this.$message({message: response.msg, type: 'success'})

                            setTimeout(() => {
                                this.$router.go(-1);
                            }, 1500)
                        } else {
                            this.$message({message: response.msg, type: 'error'})
                            this.isDisabled = false;
                        }
                    })
                }
            })
        }
    }
}
</script>

<style lang="scss">
.authentication-personal {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;

    .el-input__inner {
        &:focus {
            border-color: #e6a23c !important;
        }
    }

    .auth-header {
        background: #fffbe6;
        border: 1px solid #ffe58f;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 14px;
        box-sizing: border-box;

        .item {
            position: relative;
            line-height: 24px;
            margin-left: 12px;
            font-size: 12px;
            color: #626066;

            &:before {
                position: absolute;
                content: '';
                width: 4px;
                height: 4px;
                border-radius: 4px;
                background: #000;
                left: -12px;
                top: 50%;
                margin-top: -2px;
            }
        }
    }

    .auth-body {
        padding: 20px 0;
        box-sizing: border-box;

        .demo-ruleForm {
            width: 600px;
        }
    }
}
</style>
