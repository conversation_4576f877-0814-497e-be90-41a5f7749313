<template>
    <div class="invoice-page">
        <!-- 页面标题 -->
        <div class="page-header">
            <el-button type="warning" size="small" @click="addInvoice" class="refresh-btn">
                <i class="el-icon-plus"></i>
                申请开票
            </el-button>
        </div>

        <!-- 说明信息 -->
        <div class="info-section">
            <div class="info-item">
                <span class="info-number">1.</span>
                <span class="info-text">可开票金额为您已消费但未开票的金额，系统将根据消费记录自动计算可开票金额；</span>
            </div>
            <div class="info-item">
                <span class="info-number">2.</span>
                <span class="info-text">请确保发票信息准确无误，提交后将进入审核流程；</span>
            </div>
            <div class="info-item">
                <span class="info-number">3.</span>
                <span
                    class="info-text">审核通过后，我们将在3-5个工作日内为您开具发票。电子发票将发送至您填写的邮箱，纸质发票将邮寄至指定地址。如有疑问，请联系客服。</span>
            </div>
        </div>

        <!-- 开票记录 -->
        <div class="records-section">
            <div class="section-header">
                <h3>开票记录</h3>
            </div>

            <!-- 记录列表 -->
            <div class="records-list">
                <el-table v-loading="loading" :data="invoiceList" class="records-table" border>
                    <el-table-column prop="amount" label="开票金额" width="200">
                        <template slot-scope="scope">
                            <span class="amount-text">¥{{ scope.row.amount }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="invoiceType" label="发票类型" width="200">
                        <template slot-scope="scope">
                            <span v-if="scope.row.invoiceType === '0'">增值税普通发票</span>
                            <span v-else-if="scope.row.invoiceType === '1'">增值税专用发票</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="invoiceTitle" label="发票抬头" show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column prop="amountType" label="费用项目">
                        <template slot-scope="scope">
                            <el-tag size="mini" v-if="scope.row.amountType === '0'">API调用</el-tag>
                            <el-tag size="mini" type="success" v-else-if="scope.row.amountType === '1'">
                                技术服务费
                            </el-tag>
                            <el-tag size="mini" type="warning" v-else-if="scope.row.amountType === '2'">云服务</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="invoiceStatus" label="状态" width="200">
                        <template slot-scope="scope">
                            <el-tag size="mini" v-if="scope.row.invoiceStatus === 0" type="warning">待审核</el-tag>
                            <el-tag size="mini" v-else-if="scope.row.invoiceStatus === 1" type="success">
                                审核通过
                            </el-tag>
                            <el-tag size="mini" v-else-if="scope.row.invoiceStatus === 2" type="warning">已拒绝</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="applyDate" label="申请时间">
                        <template slot-scope="scope">
                            {{ scope.row.applyDate }}
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <pagination
                    v-show="total > 0"
                    :total="total"
                    layout="prev, pager, next"
                    :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize"
                    @pagination="getInvoiceList"
                />
            </div>
        </div>

        <!-- 发票申请抽屉 -->
        <invoice-drawer ref="invoiceDrawer" :invoice-amount-info="invoiceAmountInfo"
                        @refresh="handleRefresh">
        </invoice-drawer>
    </div>
</template>

<script>
import {getInvoiceAmount, getMyInvoiceList} from "@/api/web/index";
import InvoiceDrawer from "./components/InvocieDrawer.vue";

export default {
    name: "invoice",
    components: {
        InvoiceDrawer
    },
    data() {
        return {
            // 可开票金额
            invoiceAmountInfo: {},
            // 发票记录相关
            loading: false,
            invoiceList: [],
            total: 0,
            queryParams: {
                pageNum: 1,
                pageSize: 10,
            },
        }
    },
    mounted() {
        this.getInvoiceAmountData();
        this.getInvoiceList();
    },
    methods: {
        // 获取可开票金额
        async getInvoiceAmountData() {
            try {
                const response = await getInvoiceAmount();
                console.log('获取可开票金额:', response);
                if (response.code === 200) {
                    this.invoiceAmountInfo = response.data || 0;
                }
            } catch (error) {
                console.error('获取可开票金额失败:', error);
                this.$message.error('获取可开票金额失败');
            }
        },

        // 打开发票申请抽屉
        openInvoiceDrawer() {
            this.$refs.invoiceDrawer.open();
        },

        // 申请开票（兼容旧方法名）
        addInvoice() {
            this.openInvoiceDrawer();
        },

        // 处理抽屉刷新事件
        handleRefresh() {
            this.getInvoiceAmountData();
            this.getInvoiceList();
        },

        // 获取发票记录列表
        async getInvoiceList() {
            this.loading = true;
            try {
                const response = await getMyInvoiceList(this.queryParams);
                if (response.code === 200) {
                    this.invoiceList = response.rows || [];
                    this.total = response.total || 0;
                }
            } catch (error) {
                console.error('获取发票记录失败:', error);
                this.$message.error('获取发票记录失败');
            } finally {
                this.loading = false;
            }
        },
    }
}
</script>

<style scoped lang="scss">
.invoice-page {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: calc(100vh - 100px);

    // 页面标题
    .page-header {
        margin-bottom: 12px;
    }

    // 说明信息
    .info-section {
        background: #fffbe6;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;

        .info-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            line-height: 1.6;

            &:last-child {
                margin-bottom: 0;
            }

            .info-number {
                color: #606266;
                font-weight: 600;
                margin-right: 8px;
                flex-shrink: 0;
            }

            .info-text {
                color: #606266;
                font-size: 14px;
            }
        }
    }

    // 功能卡片
    .function-card {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease, box-shadow 0.2s ease;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;

            h3 {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
                color: #303133;
            }
        }

        .card-content {
            .card-desc {
                color: #909399;
                font-size: 14px;
                margin-bottom: 20px;
                line-height: 1.5;
            }
        }
    }

    // 可开票金额卡片
    .amount-card {
        .amount-display {
            text-align: center;
            margin-bottom: 20px;

            .amount {
                font-size: 36px;
                font-weight: bold;
                color: #ffba00;
            }
        }

        .amount-details {
            .detail-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #f0f0f0;

                &:last-child {
                    border-bottom: none;
                }

                .label {
                    color: #909399;
                    font-size: 14px;
                }

                .value {
                    color: #303133;
                    font-weight: 600;
                }
            }
        }
    }

    // 申请卡片
    .apply-card {
        .quick-stats {
            display: flex;
            justify-content: space-around;
            gap: 16px;

            .stat-item {
                text-align: center;
                flex: 1;
                padding: 16px;
                background: #f8f9fa;
                border-radius: 8px;

                .stat-number {
                    display: block;
                    font-size: 24px;
                    font-weight: bold;
                    color: #ffba00;
                    margin-bottom: 4px;
                }

                .stat-label {
                    font-size: 12px;
                    color: #909399;
                }
            }
        }
    }

    // 记录区域
    .records-section {
        background: white;
        border-radius: 12px;
        padding: 24px;

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;

            h2 {
                margin: 0;
                font-size: 20px;
                font-weight: 600;
                color: #303133;
            }

            .header-actions {
                .el-form {
                    margin: 0;

                    .el-form-item {
                        margin-bottom: 0;
                        margin-right: 16px;

                        &:last-child {
                            margin-right: 0;
                        }
                    }
                }
            }
        }

        .records-list {
            .records-table {
                .amount-text {
                    color: #ffba00;
                    font-weight: 600;
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        padding: 10px;

        .page-header {
            padding: 16px;

            .header-content {
                flex-direction: column;
                gap: 12px;
                text-align: center;

                .title-section {
                    justify-content: center;
                }
            }
        }

        .info-section {
            padding: 16px;
        }

        .function-card {
            padding: 16px;

            .card-header {
                flex-direction: column;
                gap: 12px;
                text-align: center;
            }
        }

        .records-section {
            padding: 16px;

            .section-header {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;

                .header-actions {
                    .el-form {
                        .el-form-item {
                            display: block;
                            margin-bottom: 12px;
                            margin-right: 0;

                            .el-select,
                            .el-date-editor {
                                width: 100%;
                            }
                        }
                    }
                }
            }
        }
    }
}

// 弹窗样式优化
.el-dialog {
    .dialog-footer {
        text-align: right;
        padding-top: 20px;
        border-top: 1px solid #ebeef5;
    }
}
</style>
