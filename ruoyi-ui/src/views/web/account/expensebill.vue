<template>
    <div class="expensebill">
        <div class="left-panel">
            <div class="flex-row account-box">
                <div class="account-item flex-column jc-center ai-center">
                    <div class="_title">账户余额</div>
                    <div class="_amount flex-row ai-center">
                        <div>{{ accountInfo.balance || 0 }}</div>
                        <div>元</div>
                    </div>
                </div>
                <div class="account-item flex-column jc-center ai-center">
                    <div class="_title">套餐余额</div>
                    <div class="_amount flex-row ai-center">
                        <div class="_label">输入{{ formatNumberAbbreviation(totalInput) }} Tokens</div>
                        <div class="_label"> /</div>
                        <div class="_label">输出{{ formatNumberAbbreviation(totalOutput) }} Tokens</div>
                    </div>
                </div>
                <div class="account-item flex-column jc-center ai-center">
                    <div class="_title">赠送余额</div>
                    <div class="_amount flex-row ai-center">
                        <div>{{ accountInfo.giftBalance || '0' }}</div>
                        <div>元</div>
                    </div>
                </div>
            </div>
            <el-tabs v-model="tabActive" @tab-click="tabClick">
                <el-tab-pane label="余额充值" name="1">
                    <el-form style="width: 600px">
                        <el-form-item label="支付金额">
                            <div class="good-list flex-row">
                                <template v-for="(n,nIndex) in amounts">
                                    <div class="good-item" @click="changeAmount(n,nIndex)"
                                         :class="{active:nIndex == selBalanceIndex}">
                                        <span>{{ n }}</span>
                                        <span v-if="n !== '其他金额'">元</span>
                                    </div>
                                </template>
                                <el-input-number v-model="amount" v-if="showInput" :min="0.01" placeholder="0.00"
                                                 :max="100000">
                                </el-input-number>
                            </div>
                        </el-form-item>
                        <el-form-item label="支付方式">
                            <div class="flex-row ai-center pay-btn jc-center">
                                <img src="@/assets/web/order-wx.svg">
                                <span> 微信</span>
                            </div>
                        </el-form-item>
                        <el-form-item>
                            <el-button style="width: 100%" type="warning" @click="placeOrder">确认支付</el-button>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="套餐充值" name="2">
                    <el-form style="width: 900px" class="my-form">
                        <el-form-item label="选择套餐">
                            <div class="flex-row tc-list">
                                <template v-for="(tc,tcIndex) in tcList">
                                    <div class="tc-item flex-column jc-center ai-center" @click="selectTC(tc)"
                                         :class="{'_active':selTcInfo.productId === tc.productId}">
                                        <div class="_top">
                                            {{ tc.productName }} / {{ tc.validDate }}天
                                        </div>
                                        <div class="flex-row ai-center" style="gap: 5px;">
                                            <div class="_group">
                                                <div class="_price flex-row ai-flex-end">
                                                    <span> ￥ </span>
                                                    <span style="font-size: 36px">{{ tc.currentPrice }}</span>
                                                </div>
                                            </div>
                                            <div class="_group" STYLE="font-size: 12px">
                                                <div class="_tokens">
                                                    输入 / {{ formatNumberAbbreviation(tc.inputTokenQuota) }} tokens
                                                </div>
                                                <div class="_tokens">
                                                    输出 / {{ formatNumberAbbreviation(tc.outputTokenQuota) }} tokens
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </el-form-item>
                        <el-form-item label="支付方式">
                            <div class="flex-row ai-center pay-btn jc-center">
                                <img src="@/assets/web/order-wx.svg">
                                <span> 微信</span>
                            </div>
                        </el-form-item>
                        <el-form-item>
                            <el-button style="width: 100%" type="warning" @click="placeOrder">确认支付</el-button>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
            </el-tabs>
        </div>

        <div class="right-panel">
            <div class="record-title">充值记录</div>
            <el-scrollbar class="custom-scrollbar"
                          style="width:100%; overflow-x:hidden !important;height: calc(100vh - 200px);">
                <div class="record-list">
                    <div v-for="(record, index) in myOrderList" :key="index" class="record-item">
                        <div class="record-header">
                            <div class="payment-type flex-row ai-center">
                                <span class="span span1">微信支付</span>
                                <span class="span span2" :class="'status' + record.status">
                                    {{ handleState(record) }}
                                </span>
                            </div>
                            <div class="record-amount">¥ {{ record.totalAmount }}</div>
                        </div>
                        <div class="record-footer">
                            <div class="record-time">{{ record.createTime }}</div>
                            <div class="record-order">{{ record.orderNum }}</div>
                        </div>
                    </div>
                </div>
            </el-scrollbar>
        </div>

        <!--  充值弹出  -->
        <qr-code-dialog ref="qrCodeDialogRef" @refresh="refresh"></qr-code-dialog>
    </div>
</template>

<script>
import {listProduct} from "@/api/product/product";
import {createScanPayQrcode, paying, placeOrder, getMyOrderList} from "@/api/product/order";
import {getMyAccount, getMyTokenAccount} from "@/api/web";

import QrCodeDialog from "@/views/web/account/components/QrCodeDialog.vue";

export default {
    components: {QrCodeDialog},
    dicts: ['order_status'],
    data() {
        return {
            selectedAmount: null,
            selectedPayment: 'wechat',
            agreementChecked: false,
            amounts: ['10', '50', '100', '1000', '2000', '5000', '10000', '其他金额'],
            goodList: [],
            amountBalance: 0,
            amount: 20, // 输入的金额
            showInput: false,
            selBalanceIndex: -1,
            tabActive: '1',
            productId: '',
            accountInfo: {},
            myOrderList: [],
            totalInput: 0, // 套餐剩余流量
            totalOutput: 0, // 套餐剩余流量

            selTcInfo: {},
            isDisabled: false
        };
    },
    computed: {
        balanceInfo() { // 余额
            return this.goodList.find(n => n.type === 1)
        },
        tcList() {
            return this.goodList.filter(n => n.type === 0)
        },
    },
    watch: {
        amount: {
            deep: true,
            handler: function (value, oldValue) {
                this.amountBalance = Math.round(((value / this.balanceInfo.currentPrice) + Number.EPSILON) * 100) / 100;
                console.log('this.amountBalance', this.amountBalance)
            }
        }
    },
    mounted() {
        this.getMyAccount();
        this.getMyTokenAccount();
        this.getMyOrderList();
        this.loadGoods();
        console.log(111111, this.dict.type.order_status)
    },
    methods: {
        getMyAccount() { // 加载我的账户信息
            getMyAccount().then(res => {
                console.log('账户信息', res);
                this.accountInfo = res.data || {};
            })
        },
        getMyTokenAccount() { // 加载我的账户信息
            getMyTokenAccount().then(res => {
                console.log(13, res);
                let list = res.data || [];
                if (list.length > 0) {
                    // 计算 inputTokenQuota 总和
                    this.totalInput = list.reduce((sum, item) => sum + item.inputTokenQuota, 0);
                    // 计算 outputTokenQuota 总和
                    this.totalOutput = list.reduce((sum, item) => sum + item.outputTokenQuota, 0);
                }
            })
        },
        getMyOrderList() {
            getMyOrderList().then(res => {
                console.log('我的订单记录', res)
                this.myOrderList = res.data;
            })
        },
        loadGoods() {
            listProduct({
                pageNum: 1,
                pageSize: 1000
            }).then(res => {
                console.log(res)
                this.goodList = res.rows;
            })
        },
        tabClick() {
            console.log(this.tabActive)
            if (this.tabActive === '1') {
                this.getMyAccount()
            } else {
                this.getMyTokenAccount()
            }
        },
        changeAmount(item, nIndex) {
            this.selBalanceIndex = nIndex;
            if (item !== '其他金额') {
                this.showInput = false
                this.amountBalance = Math.round(((item / this.balanceInfo.currentPrice) + Number.EPSILON) * 100) / 100;
            } else {
                this.amount = 20; // 默认设置20
                this.showInput = true
                this.amountBalance = Math.round(((this.amount / this.balanceInfo.currentPrice) + Number.EPSILON) * 100) / 100;
            }

            console.log('this.amountBalance', this.amountBalance)
        },
        placeOrder() {
            let productId = '';
            let count = 1;
            if (this.isDisabled) {
                return;
            }

            if (this.tabActive === '1') {
                count = this.amountBalance;
                productId = this.balanceInfo.productId;
            } else {
                if (this.selTcInfo.productId) {
                    productId = this.selTcInfo.productId;
                }
            }

            let params = {
                "isPlace": true,
                "payType": 0,
                "count": this.tabActive === '1' ? this.amountBalance : 1,
                "productId": productId,
            }

            if (count === 0 || !count) {
                this.$message({message: '请选择充值金额', type: 'error'})
                return;
            }

            if (!productId) {
                this.$message({message: '请选择套餐', type: 'error'})
                return;
            }

            this.isDisabled = true;
            placeOrder(params).then(response => {
                console.log('支付结果AAA', response)
                paying({
                    orderNumber: response.data.orderNum,
                    payType: 1 // 扫码
                }).then(responseA => {
                    console.log('微信回调结果', responseA)

                    createScanPayQrcode({
                        codeUrl: responseA.data
                    }).then(responseB => {
                        console.log('生成二维码结果', responseB)

                        let qrcodeUrl = 'data:image/jpg;base64,' + responseB.data;
                        this.$refs.qrCodeDialogRef.init({
                            qrcodeurl: qrcodeUrl,
                            orderNumber: response.data.orderNum
                        });
                    })
                })
            })
        },
        refresh(bool) {
            if (bool) {
                this.getMyAccount();
                this.getMyTokenAccount();
                this.getMyOrderList();
                this.isDisabled = false;
            }
        },
        handleState(item) {
            let info = this.dict.type.order_status.find(n => n.value === (item.status + ''))

            return info.label;
        },
        formatNumberAbbreviation(num, maxFractionDigits = 2) {
            const units = [
                {value: 1e12, symbol: 'T'}, // 万亿
                {value: 1e9, symbol: 'B'},  // 十亿
                {value: 1e6, symbol: 'M'},  // 百万
                {value: 1e3, symbol: 'K'}   // 千
            ];

            // 处理负数
            const isNegative = num < 0;
            num = Math.abs(num);

            // 匹配单位
            const unit = units.find(unit => num >= unit.value);
            if (unit) {
                const dividedNum = num / unit.value;
                // 检查是否需要显示小数（如 8.0 → 8）
                const hasFraction = dividedNum % 1 !== 0;
                const formattedNum = dividedNum.toLocaleString(undefined, {
                    maximumFractionDigits: hasFraction ? maxFractionDigits : 0
                });
                return `${isNegative ? '-' : ''}${formattedNum}${unit.symbol}`;
            }

            // 不足千的单位直接返回原数字（无小数）
            return `${isNegative ? '-' : ''}${num.toLocaleString()}`;
        },
        selectTC(item) {
            this.selTcInfo = item;
        }
    }
}
</script>

<style lang="scss">
@import "@/assets/styles/flex-base";

.expensebill {
    display: flex;
    gap: 24px;
    width: 100%;
    padding: 20px;
    box-sizing: border-box;

    .left-panel {
        flex: 1;
        padding: 12px 24px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

        .account-box {
            background: #F3F5FA;
            box-sizing: border-box;
            padding: 20px;
            gap: 20px;

            .account-item {
                width: 240px;
                position: relative;
                gap: 12px;

                ._title {
                    font-size: 14px;
                    font-weight: 550;
                    color: #333;
                }


                ._amount {
                    gap: 4px;
                    color: #000;

                    ._label {
                        font-size: 12px;
                    }
                }

                &:not(:first-child) {
                    &:before {
                        position: absolute;
                        content: '';
                        width: 2px;
                        height: 50px;
                        left: -10px;
                        top: 0;
                        bottom: 0;
                        background: rgba(230, 162, 60, 0.3);
                    }
                }
            }
        }

        .my-form {
            .el-form-item--medium .el-form-item__content {
                line-height: 20px !important;
            }
        }

        .pay-btn {
            gap: 6px;
            width: 100px;
            height: 40px;
            border-radius: 5px;
            box-sizing: border-box;
            border: 1px solid #E6A23C;
            color: #E6A23C;
            cursor: pointer;
        }


        .token {
            height: 36px;
            font-size: 14px;
            font-weight: 450;
            color: #313033;
            gap: 24px;
        }

        .good-list {
            box-sizing: border-box;
            gap: 20px;

            .good-item {
                width: 110px;
                height: 40px;
                line-height: 40px;
                text-align: center;
                border: 1px solid #eeeeee;
                border-radius: 5px;

                &:hover, &.active {
                    border-color: #E6A23C;
                    color: #E6A23C;
                    cursor: pointer;
                }
            }
        }

        .tc-list {
            box-sizing: border-box;
            gap: 15px;

            .tc-item {
                width: 240px;
                height: 100px;
                border: 2px solid #eeeeee;
                border-radius: 10px;
                padding: 10px;
                gap: 8px;
                cursor: pointer;
                color: #313033;

                &._active {
                    border-color: #e6a23c;
                }

                &:hover {
                    border-color: #e6a23c;
                }

                ._top {
                    font-size: 14px;
                }

                ._price {
                    font-size: 12px;
                }
            }
        }
    }

    .right-panel {
        width: 400px;
        background: #ffffff;
        border-radius: 8px;
        padding: 24px 5px 0;
        //box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);


        .balance-amount span {
            font-size: 16px;
            margin-left: 4px;
        }

        .record-title {
            font-size: 16px;
            color: #333333;
            margin-bottom: 16px;
        }

        .record-item {
            padding: 16px 0;
            border: 1px solid #e5e7eb;
            padding: 15px;
            margin-bottom: 12px;
            border-radius: 5px;
        }

        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .payment-type {
            gap: 8px;
            color: #666666;

            .span2 {
                font-size: 10px;
            }

            .status0 {
                background: #e6a23c;
                color: #fff;
                border-radius: 3px;
                padding: 1px 5px;
            }

            .status1 {
                background: #67C23A;
                color: #fff;
                border-radius: 3px;
                padding: 1px 5px;
            }
        }

        .record-amount {
            font-size: 16px;
            font-weight: 500;
            color: #333333;
        }

        .record-footer {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .record-time, .record-order {
            font-size: 14px;
            color: #999999;
        }
    }

    .el-tabs__item {
        &:hover {
            color: #e6a23c;
        }

        &.is-active {
            color: #e6a23c;
        }
    }

    .el-tabs__active-bar {
        background: #e6a23c;
    }

    .el-input__inner {
        &:focus {
            border-color: #e6a23c !important;
        }
    }
}
</style>

