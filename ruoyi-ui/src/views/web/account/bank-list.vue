<template>
    <div class="bank-list">
        <el-button type="warning" size="small" @click="handleAddBank()">添加银行卡</el-button>
        <el-table border :data="tableData" style="width: 100%; margin-top: 12px" size="mini">
            <el-table-column prop="accountName" label="开户姓名" width="150"></el-table-column>
            <el-table-column prop="bank" label="银行名称"></el-table-column>
            <el-table-column prop="bankSub" label="支行名称"></el-table-column>
            <el-table-column prop="bankAccount" label="银行卡号"></el-table-column>
            <el-table-column prop="isDefault" label="是否默认">
                <template #default="scope">
                    <el-tag type="warning" v-if="scope.row.isDefault === 1">是</el-tag>
                    <el-tag type="info" v-else>否</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="100" fixed="right">
                <template #default="scope">
                    <el-button type="text" @click="handleAddBank(scope.row)" size="small">编辑</el-button>
                    <el-button type="text" @click="handleDeleteBank(scope.row)" size="small">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination
            v-show="total>0"
            :total="total"
            layout="prev, pager, next"
            :page.sync="pageNum"
            :limit.sync="pageSize"
            @pagination="loadList"
        />

        <edit-bank ref="editBankRef" @refresh="handleRefresh"></edit-bank>
    </div>
</template>

<script>
import request from "../../../utils/request";
import EditBank from "./components/EditBank.vue";

export default {
    name: "bank-list",
    components: {EditBank},
    data() {
        return {
            pageSize: 10,
            pageNum: 1,
            total: 0,
            tableData: []
        }
    },
    mounted() {
        this.loadList()
    },
    methods: {
        handleAddBank(item) {
            this.$refs.editBankRef.init(item)
        },
        handleDeleteBank(item) {
            this.$confirm('此操作将永久删除该银行卡, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                request({
                    url: '/user/bank/delete',
                    method: 'post',
                    data: {
                        ids: [item.id]
                    }
                }).then(response => {
                    this.$message({
                        type: 'success',
                        message: '删除成功'
                    });
                    this.handleRefresh()
                })
            }).catch(() => {
                // this.$message({
                //     type: 'error',
                //     message: '删除失败'
                // });
            });
        },
        loadList(url) {
            this.tableData = [];
            request({
                url: '/user/bank/page',
                method: 'get',
                params: {
                    pageNum: this.pageNum,
                    pageSize: this.pageSize
                }
            }).then(response => {
                console.log('提现记录', response)
                this.tableData = response.rows
                this.total = response.total
            })
        },
        handleRefresh() {
            this.pageNum = 1;
            this.loadList()
        }
    }
}
</script>

<style scoped lang="scss">
.bank-list {
    padding: 20px;
    gap: 24px;
}
</style>
