<template>
    <div class="bills">
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="月度账单" name="month">
                <div class="flex-row ai-center" style="gap: 10px;">
                    <el-date-picker v-model="currentYear" type="year" placeholder="选择日期" size="mini"
                                    value-format="yyyy" @change="loadData()">
                    </el-date-picker>
                    <el-select v-model="type" size="mini" @change="loadData()">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="智能体" value="1"></el-option>
                        <el-option label="模型" value="2"></el-option>
                    </el-select>
                </div>

                <el-table :data="tableList" style="width: 100%; margin-top: 12px;" border>
                    <el-table-column prop="month" label="账期">
                        <template #default="scope">
                            <span>{{ handleDate(scope.row.month) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="totalCost" label="消费总额(元)">
                        <template #default="scope">
                            {{ scope.row.totalCost || '-' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="totalTokens" label="Tokens总量">
                        <template #default="scope">
                            {{ scope.row.totalTokens || '-' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="150">
                        <template #default="scope">
                            <el-button type="warning" size="mini" @click="tapItem(scope.row)">查看详情</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="日度账单" name="day">
                <div class="flex-row ai-center" style="gap: 10px;">
                    <el-date-picker v-model="currentYearMonth" type="month" placeholder="选择日期" size="mini"
                                    value-format="yyyy-MM" @change="loadData()">
                    </el-date-picker>
                    <el-select v-model="type" size="mini" @change="loadData()">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="智能体" value="1"></el-option>
                        <el-option label="模型" value="2"></el-option>
                    </el-select>
                </div>

                <el-table :data="tableList" style="width: 100%; margin-top: 12px;" border>
                    <el-table-column prop="month" label="账期">
                        <template #default="scope">
                            <span>{{ handleDate(scope.row.month, scope.row.day) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="totalCost" label="消费总额(元)">
                        <template #default="scope">
                            {{ scope.row.totalCost || '-' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="totalTokens" label="Tokens总量">
                        <template #default="scope">
                            {{ scope.row.totalTokens || '-' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="150">
                        <template #default="scope">
                            <el-button type="warning" size="mini" @click="tapItem(scope.row)">查看详情</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="费用明细" name="second">
                <div class="flex-row" style="gap: 24px;">
                    <div class="_item">
                        <el-date-picker v-model="currentDate" placeholder="选择日期" size="mini"
                                        value-format="yyyy-MM-dd" :clearable="false" @change="loadData()">
                        </el-date-picker>
                    </div>
                    <div class="_item">
                        <el-select v-model="type" size="mini" @change="loadData()">
                            <el-option label="智能体" value="1"></el-option>
                            <el-option label="模型" value="2"></el-option>
                        </el-select>
                    </div>
                    <div class="_item flex-row ai-center" v-if="activeName === 'second' && type === '1'">
                        <span style="font-size: 12px; padding-right: 5px; color: #626066">智能体: </span>
                        <el-select v-model="agentId" size="mini" style="width: 340px" @change="loadData()">
                            <template v-for="(ai,aiIndex) in agentList">
                                <el-option :value="ai.agentId" :label="ai.agentName"></el-option>
                            </template>
                        </el-select>
                    </div>
                    <div class="_item flex-row ai-center" v-if="activeName === 'second' && type === '2'">
                        <span style="font-size: 12px; padding-right: 5px; color: #626066">API 密钥: </span>
                        <el-select v-model="aikey" size="mini" style="width: 340px" @change="loadData()">
                            <el-option value="" label="全部"></el-option>
                            <template v-for="(ai,aiIndex) in tableDataAkey">
                                <el-option :value="ai.keyId" :label="maskString(ai.secretKey)"></el-option>
                            </template>
                        </el-select>
                    </div>
                </div>

                <el-table :data="tableList" style="width: 100%; margin-top: 12px; margin-bottom: 12px;" border v-if="type === '2'">
                    <el-table-column prop="categoryName" label="模型类型"></el-table-column>
                    <el-table-column prop="modelName" label="模型名称"></el-table-column>
                    <el-table-column prop="totalCost" label="消费时间段">
                        <template #default="scope">
                            {{ sliceTime(scope.row.requestTime) }} ~ {{ sliceTime(scope.row.responseTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="totalTokens" label="Token总量(输入/输出)">
                        <template #default="scope">
                            <div class="flex-row ai-center">
                                <div class="_item">{{ scope.row.inputTokens || '-' }}</div>
                                /
                                <div class="_item">{{ scope.row.outputTokens || '-' }}</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="totalCost" label="余额抵扣总额(元)">
                        <template #default="scope">
                            {{ scope.row.totalCost || '-' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="totalTokens" label="套餐总量(输入/输出)">
                        <template #default="scope">
                            <div class="flex-row ai-center" style="color: #e6a23c;cursor: pointer;"
                                 @click="tapItemA(scope.row)"
                                 v-if="scope.row.packageInputTokens > 0 || scope.row.packageOutputTokens > 0">
                                <div class="_item">{{ scope.row.packageInputTokens || '-' }}</div>
                                /
                                <div class="_item">{{ scope.row.packageOutputTokens || '-' }}</div>
                            </div>
                            <div v-else>
                                -
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <el-table :data="tableList" style="width: 100%; margin-top: 12px; margin-bottom: 12px;" border v-if="type === '1'">
                    <el-table-column prop="categoryName" label="智能体类型"></el-table-column>
                    <el-table-column prop="agentName" label="智能体名称"></el-table-column>
                    <el-table-column prop="project" label="收费项目"></el-table-column>
                    <el-table-column prop="totalCost" label="消费时间段">
                        <template #default="scope">
                            {{ sliceTime(scope.row.requestTime) }} ~ {{ sliceTime(scope.row.responseTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="totalCost" label="余额抵扣总额(元)">
                        <template #default="scope">
                            {{ scope.row.totalCost || '-' }}
                        </template>
                    </el-table-column>
                </el-table>
                <pagination
                    layout="total, sizes, prev, pager, next"
                    v-show="total>0"
                    :total="total"
                    :background="false"
                    :page.sync="pageNum"
                    :limit.sync="pageSize"
                    @pagination="loadData"
                />
            </el-tab-pane>
        </el-tabs>

        <bill-detail ref="billDetailRef"></bill-detail>
        <bill-detail2 ref="billDetailRef2"></bill-detail2>
    </div>
</template>
<script>
import {getMonthlyStatistics, getDayStatistics, getMyBillList, getMyKey, getMyAgentBillList} from '@/api/web/index'
import {getFormattedDate} from "@/utils";
import BillDetail from "@/views/web/account/components/BillDetail.vue";
import BillDetail2 from "@/views/web/account/components/BillDetail2.vue";
import request from "../../../utils/request";

export default {
    components: {BillDetail, BillDetail2},
    data() {
        return {
            activeName: 'month',
            currentYear: '',
            type: '',
            currentMonth: '',
            currentYearMonth: '',
            currentDate: '',
            aikey: '',
            tableList: [],
            tableDataAkey: [],
            agentList: [],
            agentId: '',
            pageNum: 1,
            pageSize: 10,
            total: 0,
        }
    },
    mounted() {
        this.currentYear = getFormattedDate('year');
        this.currentMonth = getFormattedDate('month');
        this.currentYearMonth = getFormattedDate('yearMonth');
        this.currentDate = getFormattedDate('date');
        this.getMyKey();
        this.getAgentList();
        this.loadData();
    },
    methods: {
        handleClick() {
            if (this.activeName === 'second') {
                this.type = '2'
            } else {
                this.type = ''
            }
            this.loadData();
        },
        loadData() {
            let params = {
                type: this.type
            };

            this.tableList = [];

            if (this.activeName === 'month') {
                params.year = this.currentYear;

                getMonthlyStatistics(params).then(response => {
                    this.tableList = response.data;
                })
            } else if (this.activeName === 'day') {
                params.year = this.currentYear;
                params.month = this.currentMonth;

                getDayStatistics(params).then(response => {
                    this.tableList = response.data;
                })
            } else {
                // 日期是必填的~
                if (!this.currentDate) {
                    this.currentDate = getFormattedDate('date')
                }

                if (this.aikey) {
                    params.keyId = this.aikey
                }
                params.date = this.currentDate;
                params.pageNum = this.pageNum;
                params.pageSize = this.pageSize;

                if (Number(this.type) === 2) {
                    getMyBillList(params).then(response => {
                        console.log('bbb', response)
                        this.tableList = response.rows;
                        this.total = response.total;
                    })
                } else {
                    getMyAgentBillList(params).then(response => {
                        console.log('bbb', response)
                        this.tableList = response.rows;
                        this.total = response.total;
                    })
                }
            }
        },
        handleDate(month, day) {
            let date = this.currentYear;

            if (month < 10) {
                date += '-0' + month;
            } else {
                date += '-' + month;
            }
            if (day) {
                if (day < 10) {
                    date += '-0' + day;
                } else {
                    date += '-' + day;
                }
            }
            return date;
        },
        tapItem(item) {
            this.$nextTick(() => {
                let date = this.handleDate(item.month, item.day);
                this.$refs.billDetailRef.init(date);
            })
        },
        tapItemA(item) {
            console.log('item', item)
            this.$nextTick(() => {
                let billId = item.billId
                this.$refs.billDetailRef2.init(billId);
            })
        },
        getMyKey() {
            getMyKey().then(response => {
                console.log('我的密钥信息', response)
                this.tableDataAkey = response.data || [];
            })
        },
        maskString(str, visibleStart = 4, visibleEnd = 4) {
            return str.replace(
                new RegExp(`^(.{${visibleStart}})(.*)(.{${visibleEnd}})$`),
                (_, start, middle, end) => start + '*'.repeat(middle.length) + end
            );
        },
        sliceTime(datetime) {
            if (!datetime) {
                return '';
            }
            return datetime.slice(11); // 从索引11开始截取
        },
        getAgentList() {
            request({
                url: '/bill/record/getUseAgent',
                method: 'get',
            }).then(response => {
                console.log('智能体列表', response)
                this.agentList = response.data
            })
        }
    }
}
</script>

<style lang="scss">
.bills {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;

    .el-tabs__item {
        &:hover {
            color: #e6a23c;
        }

        &.is-active {
            color: #e6a23c;
        }
    }

    .el-tabs__active-bar {
        background: #e6a23c;
    }

    .el-input__inner {
        &:focus {
            border-color: #e6a23c !important;
        }
    }

    .el-pager {
        .active {
            color: #e6a23c;
        }
    }
}

.el-select-dropdown__item.selected {
    color: #e6a23c;
}
</style>
