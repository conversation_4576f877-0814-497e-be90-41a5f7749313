<template>
    <div class="invoice-page">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="header-content">
                <div class="title-section">
                    <i class="el-icon-document"></i>
                    <h1>发票开具</h1>
                </div>
                <el-button type="primary" size="small" @click="addInvoice" class="refresh-btn">
                    <i class="el-icon-plus"></i>
                    申请开票
                </el-button>
            </div>
        </div>

        <!-- 说明信息 -->
        <div class="info-section">
            <div class="info-item">
                <span class="info-number">1.</span>
                <span class="info-text">可开票金额为您已消费但未开票的金额，系统将根据消费记录自动计算可开票金额；</span>
            </div>
            <div class="info-item">
                <span class="info-number">2.</span>
                <span class="info-text">请确保发票信息准确无误，提交后将进入审核流程；</span>
            </div>
            <div class="info-item">
                <span class="info-number">3.</span>
                <span
                    class="info-text">审核通过后，我们将在3-5个工作日内为您开具发票。电子发票将发送至您填写的邮箱，纸质发票将邮寄至指定地址。如有疑问，请联系客服。</span>
            </div>
        </div>

        <!-- 功能卡片区域 -->
        <div class="cards-container">
            <!-- 可开票金额卡片 -->
            <div class="function-card amount-card">
                <div class="card-header">
                    <h3>可开票金额</h3>
                    <div class="amount-display">
                        <span class="amount">¥{{ invoiceAmount.toFixed(2) }}</span>
                    </div>
                </div>
                <div class="amount-details">
                    <div class="detail-item">
                        <span class="label">已消费金额：</span>
                        <span class="value">¥{{ invoiceAmount.toFixed(2) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">已开票金额：</span>
                        <span class="value">¥0.00</span>
                    </div>
                </div>
            </div>

            <!-- 开票申请卡片 -->
            <div class="function-card apply-card">
                <div class="card-header">
                    <h3>申请开票</h3>
                    <el-button type="primary" size="small" @click="openInvoiceDrawer">
                        <i class="el-icon-plus"></i>
                        申请开票
                    </el-button>
                </div>
                <div class="card-content">
                    <p class="card-desc">提交开票申请，我们将在审核通过后为您开具发票</p>
                    <div class="quick-stats">
                        <div class="stat-item">
                            <span class="stat-number">{{ pendingCount }}</span>
                            <span class="stat-label">待审核</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ approvedCount }}</span>
                            <span class="stat-label">已通过</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ issuedCount }}</span>
                            <span class="stat-label">已开票</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 开票记录 -->
        <div class="records-section">
            <div class="section-header">
                <h2>开票记录</h2>
                <div class="header-actions">
                    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
                        <el-form-item>
                            <el-select v-model="queryParams.invoiceStatus" placeholder="发票状态" clearable
                                       style="width: 120px">
                                <el-option label="待审核" :value="0"></el-option>
                                <el-option label="审核通过" :value="1"></el-option>
                                <el-option label="已开票" :value="2"></el-option>
                                <el-option label="审核拒绝" :value="3"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-date-picker
                                v-model="dateRange"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                value-format="yyyy-MM-dd"
                                size="small"
                                @change="handleDateChange">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索
                            </el-button>
                            <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>

            <!-- 记录列表 -->
            <div class="records-list">
                <el-table v-loading="loading" :data="invoiceList" class="records-table">
                    <el-table-column prop="invoiceId" label="申请编号" width="140"></el-table-column>
                    <el-table-column prop="amount" label="开票金额" width="120">
                        <template slot-scope="scope">
                            <span class="amount-text">¥{{ scope.row.amount }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="invoiceType" label="发票类型" width="140">
                        <template slot-scope="scope">
                            <span v-if="scope.row.invoiceType === 0">增值税普通发票</span>
                            <span v-else-if="scope.row.invoiceType === 1">增值税专用发票</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="invoiceTitle" label="发票抬头" min-width="180"
                                     show-overflow-tooltip></el-table-column>
                    <el-table-column prop="amountType" label="费用项目" width="120">
                        <template slot-scope="scope">
                            <el-tag size="mini" v-if="scope.row.amountType === 0">API调用</el-tag>
                            <el-tag size="mini" type="success" v-else-if="scope.row.amountType === 1">技术服务费
                            </el-tag>
                            <el-tag size="mini" type="warning" v-else-if="scope.row.amountType === 2">云服务</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="invoiceStatus" label="状态" width="100">
                        <template slot-scope="scope">
                            <el-tag size="mini" v-if="scope.row.invoiceStatus === 0" type="warning">待审核</el-tag>
                            <el-tag size="mini" v-else-if="scope.row.invoiceStatus === 1" type="success">审核通过
                            </el-tag>
                            <el-tag size="mini" v-else-if="scope.row.invoiceStatus === 2" type="primary">已开票</el-tag>
                            <el-tag size="mini" v-else-if="scope.row.invoiceStatus === 3" type="danger">审核拒绝
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="applyDate" label="申请时间" width="160">
                        <template slot-scope="scope">
                            {{ formatDate(scope.row.applyDate) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" fixed="right">
                        <template slot-scope="scope">
                            <el-button size="mini" type="text" @click="viewDetail(scope.row)">详情</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <pagination
                    v-show="total > 0"
                    :total="total"
                    :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize"
                    @pagination="getInvoiceList"
                />
            </div>
        </div>

        <!-- 发票详情弹窗 -->
        <el-dialog title="发票详情" :visible.sync="detailVisible" width="600px">
            <div v-if="currentInvoice">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="申请编号">{{ currentInvoice.invoiceId }}</el-descriptions-item>
                    <el-descriptions-item label="开票金额">¥{{ currentInvoice.amount }}</el-descriptions-item>
                    <el-descriptions-item label="发票类型">
                        <span v-if="currentInvoice.invoiceType === 0">增值税普通发票</span>
                        <span v-else-if="currentInvoice.invoiceType === 1">增值税专用发票</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="费用项目">
                        <span v-if="currentInvoice.amountType === 0">API调用</span>
                        <span v-else-if="currentInvoice.amountType === 1">技术服务费</span>
                        <span v-else-if="currentInvoice.amountType === 2">云服务</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="发票抬头" :span="2">{{
                            currentInvoice.invoiceTitle
                        }}
                    </el-descriptions-item>
                    <el-descriptions-item label="纳税人识别号" :span="2">{{
                            currentInvoice.taxNumber || '-'
                        }}
                    </el-descriptions-item>
                    <el-descriptions-item label="配送方式">
                        <span v-if="currentInvoice.deliveryMethod === 0">电子发票</span>
                        <span v-else-if="currentInvoice.deliveryMethod === 1">邮寄</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="状态">
                        <el-tag v-if="currentInvoice.invoiceStatus === 0" type="warning">待审核</el-tag>
                        <el-tag v-else-if="currentInvoice.invoiceStatus === 1" type="success">审核通过</el-tag>
                        <el-tag v-else-if="currentInvoice.invoiceStatus === 2" type="primary">已开票</el-tag>
                        <el-tag v-else-if="currentInvoice.invoiceStatus === 3" type="danger">审核拒绝</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="配送信息" :span="2">{{
                            currentInvoice.deliveryInfo || '-'
                        }}
                    </el-descriptions-item>
                    <el-descriptions-item label="申请时间" :span="2">{{
                            formatDate(currentInvoice.applyDate)
                        }}
                    </el-descriptions-item>
                    <el-descriptions-item label="开票时间" :span="2">
                        {{ currentInvoice.issueDate ? formatDate(currentInvoice.issueDate) : '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="备注" :span="2">{{
                            currentInvoice.remark || '-'
                        }}
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="detailVisible = false">关闭</el-button>
            </div>
        </el-dialog>

        <!-- 发票申请抽屉 -->
        <invoice-drawer ref="invoiceDrawer" :invoice-amount="invoiceAmount" @refresh="handleRefresh"></invoice-drawer>
    </div>
</template>

<script>
import {getInvoiceAmount, invoiceSubmit, getMyInvoiceList} from "@/api/web/index";
import InvoiceDrawer from "./components/InvocieDrawer.vue";

export default {
    name: "invoice",
    components: {
        InvoiceDrawer
    },
    data() {
        return {
            // 可开票金额
            invoiceAmount: 0,
            // 发票记录相关
            loading: false,
            invoiceList: [],
            total: 0,
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                invoiceStatus: null,
                applyDateStart: null,
                applyDateEnd: null
            },
            dateRange: [],
            // 详情弹窗
            detailVisible: false,
            currentInvoice: null,
            // 统计数据
            pendingCount: 0,
            approvedCount: 0,
            issuedCount: 0
        }
    },
    mounted() {
        this.getInvoiceAmountData();
        this.getInvoiceList();
        this.getStatistics();
    },
    methods: {
        // 获取可开票金额
        async getInvoiceAmountData() {
            try {
                const response = await getInvoiceAmount();
                if (response.code === 200) {
                    this.invoiceAmount = response.data || 0;
                }
            } catch (error) {
                console.error('获取可开票金额失败:', error);
                this.$message.error('获取可开票金额失败');
            }
        },

        // 打开发票申请抽屉
        openInvoiceDrawer() {
            this.$refs.invoiceDrawer.open();
        },

        // 申请开票（兼容旧方法名）
        addInvoice() {
            this.openInvoiceDrawer();
        },

        // 处理抽屉刷新事件
        handleRefresh() {
            this.getInvoiceAmountData();
            this.getInvoiceList();
        },

        // 查看详情
        viewDetail(row) {
            this.currentInvoice = row;
            this.detailVisible = true;
        },

        // 获取发票记录列表
        async getInvoiceList() {
            this.loading = true;
            try {
                const response = await getMyInvoiceList(this.queryParams);
                if (response.code === 200) {
                    this.invoiceList = response.rows || [];
                    this.total = response.total || 0;
                }
            } catch (error) {
                console.error('获取发票记录失败:', error);
                this.$message.error('获取发票记录失败');
            } finally {
                this.loading = false;
            }
        },

        // 获取统计数据
        getStatistics() {
            // 从发票列表中统计各状态数量
            this.pendingCount = this.invoiceList.filter(item => item.invoiceStatus === 0).length;
            this.approvedCount = this.invoiceList.filter(item => item.invoiceStatus === 1).length;
            this.issuedCount = this.invoiceList.filter(item => item.invoiceStatus === 2).length;
        },

        // 搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getInvoiceList();
        },

        // 重置搜索
        resetQuery() {
            this.$refs.queryForm.resetFields();
            this.dateRange = [];
            this.queryParams = {
                pageNum: 1,
                pageSize: 10,
                invoiceStatus: null,
                applyDateStart: null,
                applyDateEnd: null
            };
            this.getInvoiceList();
        },

        // 日期范围变化
        handleDateChange(dates) {
            if (dates && dates.length === 2) {
                this.queryParams.applyDateStart = dates[0];
                this.queryParams.applyDateEnd = dates[1];
            } else {
                this.queryParams.applyDateStart = null;
                this.queryParams.applyDateEnd = null;
            }
        },

        // 格式化日期
        formatDate(date) {
            if (!date) return '-';
            return new Date(date).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    },
    watch: {
        invoiceList: {
            handler() {
                this.getStatistics();
            },
            immediate: true
        }
    }
}
</script>

<style scoped lang="scss">
.invoice-page {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: calc(100vh - 100px);

    // 页面标题
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        padding: 20px 24px;
        margin-bottom: 20px;
        color: white;

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .title-section {
                display: flex;
                align-items: center;
                gap: 12px;

                i {
                    font-size: 24px;
                }

                h1 {
                    margin: 0;
                    font-size: 24px;
                    font-weight: 600;
                }
            }

            .refresh-btn {
                background: rgba(255, 255, 255, 0.2);
                border: 1px solid rgba(255, 255, 255, 0.3);
                color: white;

                &:hover {
                    background: rgba(255, 255, 255, 0.3);
                }
            }
        }
    }

    // 功能卡片容器
    .cards-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;

        @media (max-width: 768px) {
            grid-template-columns: 1fr;
        }
    }

    // 说明信息
    .info-section {
        background: #fffbe6;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;

        .info-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            line-height: 1.6;

            &:last-child {
                margin-bottom: 0;
            }

            .info-number {
                color: #606266;
                font-weight: 600;
                margin-right: 8px;
                flex-shrink: 0;
            }

            .info-text {
                color: #606266;
                font-size: 14px;
            }
        }
    }

    // 功能卡片
    .function-card {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease, box-shadow 0.2s ease;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;

            h3 {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
                color: #303133;
            }
        }

        .card-content {
            .card-desc {
                color: #909399;
                font-size: 14px;
                margin-bottom: 20px;
                line-height: 1.5;
            }
        }
    }

    // 可开票金额卡片
    .amount-card {
        .amount-display {
            text-align: center;
            margin-bottom: 20px;

            .amount {
                font-size: 36px;
                font-weight: bold;
                color: #409eff;
            }
        }

        .amount-details {
            .detail-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #f0f0f0;

                &:last-child {
                    border-bottom: none;
                }

                .label {
                    color: #909399;
                    font-size: 14px;
                }

                .value {
                    color: #303133;
                    font-weight: 600;
                }
            }
        }
    }

    // 申请卡片
    .apply-card {
        .quick-stats {
            display: flex;
            justify-content: space-around;
            gap: 16px;

            .stat-item {
                text-align: center;
                flex: 1;
                padding: 16px;
                background: #f8f9fa;
                border-radius: 8px;

                .stat-number {
                    display: block;
                    font-size: 24px;
                    font-weight: bold;
                    color: #409eff;
                    margin-bottom: 4px;
                }

                .stat-label {
                    font-size: 12px;
                    color: #909399;
                }
            }
        }
    }

    // 记录区域
    .records-section {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #ebeef5;

            h2 {
                margin: 0;
                font-size: 20px;
                font-weight: 600;
                color: #303133;
            }

            .header-actions {
                .el-form {
                    margin: 0;

                    .el-form-item {
                        margin-bottom: 0;
                        margin-right: 16px;

                        &:last-child {
                            margin-right: 0;
                        }
                    }
                }
            }
        }

        .records-list {
            .records-table {
                .amount-text {
                    color: #409eff;
                    font-weight: 600;
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        padding: 10px;

        .page-header {
            padding: 16px;

            .header-content {
                flex-direction: column;
                gap: 12px;
                text-align: center;

                .title-section {
                    justify-content: center;
                }
            }
        }

        .info-section {
            padding: 16px;
        }

        .function-card {
            padding: 16px;

            .card-header {
                flex-direction: column;
                gap: 12px;
                text-align: center;
            }
        }

        .records-section {
            padding: 16px;

            .section-header {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;

                .header-actions {
                    .el-form {
                        .el-form-item {
                            display: block;
                            margin-bottom: 12px;
                            margin-right: 0;

                            .el-select,
                            .el-date-editor {
                                width: 100%;
                            }
                        }
                    }
                }
            }
        }
    }
}

// 弹窗样式优化
.el-dialog {
    .dialog-footer {
        text-align: right;
        padding-top: 20px;
        border-top: 1px solid #ebeef5;
    }
}
</style>
