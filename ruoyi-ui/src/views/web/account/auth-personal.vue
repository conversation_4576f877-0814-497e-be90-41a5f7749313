<template>
    <div class="authentication-personal">
        <div class="auth-header">
            <div class="item">根据相关法律法规，我们不对未满14周岁的个人提供在线实名认证服务。</div>
        </div>

        <div class="auth-body">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm"
                     label-position="top">
                <el-form-item label="真实姓名" prop="realName">
                    <el-input v-model="ruleForm.realName" placeholder="请输入真实姓名"></el-input>
                </el-form-item>
                <el-form-item label="证件类型" prop="idType">
                    <el-select v-model="ruleForm.idType" placeholder="请选择证件类型" style="width: 100%">
                        <template v-for="(dictA,dictAIndex) in dict.type.certificate_type">
                            <el-option :label="dictA.label" :value="dictA.value"></el-option>
                        </template>
                    </el-select>
                </el-form-item>
                <el-form-item label="证件号码" prop="idNumber">
                    <el-input v-model="ruleForm.idNumber" placeholder="请输入证件号码"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button style="width: 100%;" type="warning" @click="submitForm('ruleForm')">提交认证</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script>
import {certification, getVerificationInfo} from "@/api/web";

export default {
    name: "auth-personal",
    dicts: ['certificate_type'],
    data() {
        return {
            ruleForm: {
                "idNumber": "",
                "idType": "0",
                "individualId": null,
                "realName": "",
            },
            rules: {
                realName: [
                    {required: true, message: '请输入正确的姓名', trigger: 'blur'},
                ],
                idType: [
                    {required: true, message: '请选择证件类型', trigger: 'change'}
                ],
                idNumber: [
                    {required: true, message: '请输入正确的身份证号码', trigger: 'blur'},
                    {pattern: /^[a-zA-Z0-9]+$/, message: '只能输入字母或数字', trigger: 'blur'}
                ],
            },
            isDisabled: false
        }
    },
    created() {
        console.log('证件类型', this.dict.type.certificate_type)

        getVerificationInfo().then(response => {
            console.log('我的认证信息', response)
            let info = response.data || {};

            if (info.individualVerification) {
                Object.keys(this.ruleForm).forEach(key => {
                    this.ruleForm[key] = info.individualVerification[key]
                })
            }
        })
    },
    methods: {
        submitForm() {
            this.$refs.ruleForm.validate((valid) => {
                if (valid && !this.isDisabled) {
                    this.isDisabled = true;
                    certification({
                        individualInfo: this.ruleForm,
                        verificationType: 0
                    }).then(response => {
                        if (response.code === 200) {
                            this.$message({message: response.msg, type: 'success'})

                            setTimeout(() => {
                                this.$router.go(-1);
                            }, 1500)
                        } else {
                            this.$message({message: response.msg, type: 'error'})
                            this.isDisabled = false;
                        }
                    })
                }
            })
        }
    }
}
</script>

<style lang="scss">
.authentication-personal {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;

    .el-input__inner {
        &:focus {
            border-color: #e6a23c !important;
        }
    }

    .auth-header {
        background: #fffbe6;
        border: 1px solid #ffe58f;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 14px;
        box-sizing: border-box;

        .item {
            position: relative;
            line-height: 24px;
            margin-left: 12px;
            font-size: 12px;
            color: #626066;

            &:before {
                position: absolute;
                content: '';
                width: 4px;
                height: 4px;
                border-radius: 4px;
                background: #000;
                left: -12px;
                top: 50%;
                margin-top: -2px;
            }
        }
    }

    .auth-body {
        padding: 20px 0;
        box-sizing: border-box;

        .demo-ruleForm {
            width: 600px;
        }
    }
}
</style>
