<template>
    <el-drawer :title="title" :visible.sync="drawer" size="75%">
        <div class="bill-drawer">
            <div class="bill-title">按大模型统计</div>
            <el-table :data="tableDataA" border>
                <el-table-column prop="categoryName" label="模型类型"></el-table-column>
                <el-table-column prop="modelName" label="模型名称"></el-table-column>
                <el-table-column prop="totalCost" label="消费总额(元)">
                    <template #default="scope">
                        {{ scope.row.totalCost || '-' }}
                    </template>
                </el-table-column>
                <el-table-column prop="totalTokens" label="Tokens总量"></el-table-column>
            </el-table>

            <div class="bill-title" style="padding-top: 24px">按 API 密钥统计</div>
            <el-table :data="tableDataB" border>
                <el-table-column prop="apiKey" label="apiKey">
                    <template #default="scope">
                        {{ maskString(scope.row.apiKey) }}
                    </template>
                </el-table-column>
                <el-table-column prop="apiKeyName" label="apiKeyName"></el-table-column>
                <el-table-column prop="totalCost" label="消费总额(元)">
                    <template #default="scope">
                        {{ scope.row.totalCost || '-' }}
                    </template>
                </el-table-column>
                <el-table-column prop="totalTokens" label="Tokens总量"></el-table-column>
            </el-table>

            <div class="bill-title" style="padding-top: 24px">按智能体统计</div>
            <el-table :data="tableDataC" border>
                <el-table-column prop="agentName" label="智能体名称"></el-table-column>
                <el-table-column prop="categoryName" label="智能体分类"></el-table-column>
                <el-table-column prop="totalCost" label="消费总额(元)">
                    <template #default="scope">
                        {{ scope.row.totalCost || '-' }}
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </el-drawer>
</template>
<script>
import {getApiKeyUsageStatistics, getModelUsageStatistics} from "@/api/web";
import {getAgentUsageStatistics} from "../../../../api/web";

export default {
    name: "BillDetail",
    data() {
        return {
            drawer: false,
            date: '',
            title: '',
            tableDataA: [],
            tableDataB: [],
            tableDataC: [],
        }
    },
    methods: {
        init(date) {
            this.date = date;
            this.title = this.date + ' 账单详情'
            this.drawer = true;
            this.loadData();
        },
        loadData() {
            let params = {
                moth: this.date
            }
            getApiKeyUsageStatistics(params).then(response => {
                console.log('账单详情APIKEY', response)
                this.tableDataB = response.data;
            })

            getModelUsageStatistics(params).then(response => {
                console.log('账单详情', response)
                this.tableDataA = response.data;
            })

            getAgentUsageStatistics(params).then(response => {
                console.log('账单详情智能体', response)
                this.tableDataC = response.data;
            })
        },
        maskString(str, visibleStart = 4, visibleEnd = 4) {
            return str.replace(
                new RegExp(`^(.{${visibleStart}})(.*)(.{${visibleEnd}})$`),
                (_, start, middle, end) => start + '*'.repeat(middle.length) + end
            );
        },
    }
}
</script>
<style lang="scss">
.bill-drawer {
    box-sizing: border-box;
    width: 100%;
    height: calc(100vh - 100px);
    background: #F3F5FA;
    padding: 0 20px;

    .bill-title {
        font-size: 16px;
        font-weight: 550;
        color: #000;
        line-height: 42px;
    }
}
</style>
