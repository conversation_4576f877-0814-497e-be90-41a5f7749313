<template>
    <el-drawer
        title="申请发票"
        :visible.sync="drawer"
        :before-close="handleClose"
        size="600px"
        class="invoice-drawer">

        <div class="drawer-content">
            <!-- 可开票金额显示 -->
            <div class="amount-section" v-if="invoiceAmountInfo.totalAmount > 0">
                <div class="amount-label">可开票金额</div>
                <div class="amount-value">¥ {{ invoiceAmount.toFixed(2) }}</div>
                <div class="amount-detail">
                    累计已消费金额 ¥ {{ invoiceAmountInfo.totalAmount.toFixed(2) }} - 累计可开票金额 ¥
                    {{ invoiceAmountInfo.invoiceAmount ? invoiceAmountInfo.invoiceAmount.toFixed(2) : 0 }} - 欠款 ¥
                    {{ invoiceAmountInfo.debtAmount.toFixed(2) }} = 累计可开票金额 ¥
                    {{ invoiceAmount.toFixed(2) }}
                </div>
            </div>

            <!-- 申请表单 -->
            <el-form :model="invoiceForm" :rules="invoiceRules" ref="invoiceForm" class="invoice-form">
                <!-- 申请开票金额 -->
                <div class="form-section">
                    <div class="section-title">
                        <span>申请开票金额</span>
                        <span class="required-mark">*</span>
                    </div>
                    <el-form-item prop="amount">
                        <el-input
                            v-model.number="invoiceForm.amount"
                            placeholder="金额"
                            style="width: 100%"
                            size="large">
                            <template slot="prepend">￥</template>
                        </el-input>
                    </el-form-item>
                </div>

                <!-- 费用项目 -->
                <div class="form-section">
                    <div class="section-title">
                        <span>费用项目</span>
                        <span class="required-mark">*</span>
                    </div>
                    <el-form-item prop="amountType">
                        <el-select v-model="invoiceForm.amountType" placeholder="请选择费用项目" style="width: 100%"
                                   size="large">
                            <el-option label="API 调用" :value="0"></el-option>
                            <el-option label="技术服务费" :value="1"></el-option>
                            <el-option label="云服务" :value="2"></el-option>
                        </el-select>
                    </el-form-item>
                </div>

                <!-- 开票信息 -->
                <div class="form-section">
                    <div class="info-note">
                        请填写您的开票信息，开票信息一旦提交将无法修改，请仔细核对后再提交。
                    </div>
                </div>

                <!-- 发票类型 -->
                <div class="form-section">
                    <div class="section-title">
                        <span>发票类型</span>
                        <span class="required-mark">*</span>
                    </div>
                    <el-form-item prop="invoiceType">
                        <el-select v-model="invoiceForm.invoiceType" placeholder="请选择费用项目" style="width: 100%"
                                   size="large">
                            <el-option label="增值税普通发票" :value="0"></el-option>
                        </el-select>
                    </el-form-item>
                </div>

                <!-- 发票抬头 -->
                <div class="form-section">
                    <div class="section-title">
                        <span>发票抬头</span>
                        <span class="required-mark">*</span>
                    </div>
                    <el-form-item prop="invoiceTitle">
                        <el-input
                            v-model="invoiceForm.invoiceTitle"
                            placeholder="请输入发票抬头"
                            size="large">
                        </el-input>
                    </el-form-item>
                </div>

                <!-- 纳税人识别号 -->
                <div class="form-section">
                    <div class="section-title">纳税人识别号</div>
                    <el-form-item prop="taxNum">
                        <el-input
                            v-model="invoiceForm.taxNum"
                            placeholder="请输入纳税人识别号"
                            size="large">
                        </el-input>
                    </el-form-item>
                    <div class="input-note">
                        纳税人识别号是税务登记证上的号码，通常为15位、17位或18位
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-title">
                        <span>接收方式</span>
                        <span class="required-mark">*</span>
                    </div>
                    <el-form-item prop="deliveryMethod">
                        <el-select v-model="invoiceForm.deliveryMethod" placeholder="请选择费用项目" style="width: 100%"
                                   size="large">
                            <el-option label="邮箱" value="0"></el-option>
                            <el-option label="短信" value="1"></el-option>
                        </el-select>
                    </el-form-item>
                </div>

                <!-- 配送信息 -->
                <div class="form-section" v-if="invoiceForm.deliveryMethod === '0'">
                    <div class="section-title">
                        <span>邮箱地址</span>
                        <span class="required-mark">*</span>
                    </div>
                    <el-form-item prop="deliveryInfo">
                        <el-input
                            v-model="invoiceForm.deliveryInfo"
                            placeholder="请输入接收发票的邮箱地址"
                            size="large">
                        </el-input>
                    </el-form-item>
                </div>

                <!-- 手机号码 -->
                <div class="form-section" v-if="invoiceForm.deliveryMethod === '1'">
                    <div class="section-title">
                        <span>手机号码</span>
                        <span class="required-mark">*</span>
                    </div>
                    <el-form-item prop="deliveryInfo">
                        <el-input v-model="invoiceForm.deliveryInfo" placeholder="请输入要接收的手机号"
                                  resize="none">
                        </el-input>
                    </el-form-item>
                </div>
            </el-form>

            <!-- 提交按钮 -->
            <div class="submit-section">
                <el-button
                    type="warning"
                    @click="submitInvoice"
                    :loading="submitting"
                    class="submit-btn">
                    <i class="el-icon-document"></i>
                    申请发票
                </el-button>
            </div>
        </div>
    </el-drawer>
</template>

<script>
import {invoiceSubmit} from "@/api/web/index";

export default {
    emits: ["refresh"],
    props: {
        invoiceAmountInfo: {
            type: Object,
            default: {}
        }
    },
    computed: {
        invoiceAmount() {
            return (this.invoiceAmountInfo.totalAmount || 0) - (this.invoiceAmountInfo.invoicedAmount || 0) - (this.invoiceAmountInfo.debtAmount || 0);
        }
    },
    data() {
        return {
            drawer: false,
            submitting: false,
            // 开票申请表单
            invoiceForm: {
                "amount": null, // 开票金额
                "amountType": 0, // 费用项名称【0:API调用:1:技术服务费;2:云服务
                "taxNum": "", // 税号
                "deliveryInfo": "", // 交付信息
                "deliveryMethod": "", // 交付方式 0 邮箱 1 短信
                "invoiceTitle": "", // 抬头名称和税号
                "invoiceType": 0 // 发票类型【0:增值税普通发票】
            },
            // 表单验证规则
            invoiceRules: {
                amount: [
                    {required: true, message: '请输入开票金额', trigger: 'blur'},
                    {type: 'number', min: 0.01, message: '开票金额必须大于0.01元', trigger: 'blur'}
                ],
                invoiceType: [
                    {required: true, message: '请选择发票类型', trigger: 'change'}
                ],
                invoiceTitle: [
                    {required: true, message: '请输入发票抬头', trigger: 'blur'}
                ],
                // taxNum: [
                //     {required: true, message: '请输入纳税人识别号', trigger: 'blur'},
                //     {pattern: /^[A-Z0-9]{15,20}$/, message: '纳税人识别号格式不正确', trigger: 'blur'}
                // ],
                amountType: [
                    {required: true, message: '请选择费用项目', trigger: 'change'}
                ],
                deliveryMethod: [
                    {required: true, message: '请选择接收方式', trigger: 'change'}
                ],
                deliveryInfo: [
                    {required: true, message: '请输入接收信息', trigger: 'blur'}
                ]
            }
        };
    },
    methods: {
        // 打开抽屉
        open() {
            this.drawer = true;
            this.resetForm();
        },

        // 关闭抽屉
        handleClose() {
            this.drawer = false;
            this.resetForm();
        },

        // 重置表单
        resetForm() {
            if (this.$refs.invoiceForm) {
                this.$refs.invoiceForm.resetFields();
            }
            this.invoiceForm = {
                "amount": null, // 开票金额
                "amountType": "", // 费用项名称【0:API调用:1:技术服务费;2:云服务
                "taxNum": "", // 税号
                "deliveryInfo": "", // 交付信息
                "deliveryMethod": "", // 交付方式
                "invoiceTitle": "", // 抬头名称和税号
                "invoiceType": 0 // 发票类型【0:增值税普通发票】
            };
        },

        // 提交开票申请
        submitInvoice() {
            this.$refs.invoiceForm.validate(async (valid) => {
                if (valid) {
                    // if (this.invoiceForm.amount > this.invoiceAmount) {
                    //     this.$message.error('开票金额不能超过可开票金额');
                    //     return;
                    // }

                    this.submitting = true;
                    try {
                        const response = await invoiceSubmit(this.invoiceForm);
                        if (response.code === 200) {
                            this.$message.success('开票申请提交成功');
                            this.handleClose();
                            this.$emit('refresh', true);
                        } else {
                            this.$message.error(response.msg || '提交失败');
                        }
                    } catch (error) {
                        console.error('提交开票申请失败:', error);
                        this.$message.error('提交失败，请稍后重试');
                    } finally {
                        this.submitting = false;
                    }
                }
            });
        }
    }
};
</script>

<style scoped lang="scss">
.invoice-drawer {
    ::v-deep .el-drawer__header {
        padding: 20px 24px;
        border-bottom: 1px solid #ebeef5;
        margin-bottom: 0;

        .el-drawer__title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
        }
    }

    ::v-deep .el-drawer__body {
        padding: 0;
        overflow-y: auto;
    }

    .drawer-content {
        padding: 24px;
        background-color: #fafafa;

        // 可开票金额区域
        .amount-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
            text-align: center;

            .amount-label {
                font-size: 14px;
                color: #909399;
                margin-bottom: 8px;
            }

            .amount-value {
                font-size: 32px;
                font-weight: bold;
                color: #ffba00;
                margin-bottom: 12px;
            }

            .amount-detail {
                font-size: 12px;
                color: #909399;
                line-height: 1.5;
            }
        }

        // 表单区域
        .invoice-form {
            .form-section {
                background: #fff;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 16px;

                .section-title {
                    font-size: 14px;
                    font-weight: 600;
                    color: #303133;
                    margin-bottom: 16px;
                    display: flex;
                    align-items: center;

                    .required-mark {
                        color: #f56c6c;
                        margin-left: 4px;
                    }
                }

                .info-note {
                    font-size: 13px;
                    color: #ffba00;
                    line-height: 1.6;
                    //background: #f8f9fa;
                    border-radius: 6px;
                }

                .input-note {
                    font-size: 12px;
                    color: #909399;
                    margin-top: 8px;
                    line-height: 1.4;
                }

                // 表单项样式
                ::v-deep .el-form-item {
                    margin-bottom: 0;

                    .el-form-item__error {
                        position: static;
                        margin-top: 8px;
                    }
                }

                // 输入框样式
                ::v-deep .el-input__inner,
                ::v-deep .el-textarea__inner {
                    &:focus {
                        border-color: #ffba00;
                    }
                }
            }
        }

        // 提交按钮区域
        .submit-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;

            .submit-btn {
                width: 100%;
                height: 48px;
                font-size: 16px;
                font-weight: 600;
                border-radius: 8px;

                i {
                    margin-right: 8px;
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .invoice-drawer {
        ::v-deep .el-drawer {
            width: 100% !important;
        }

        .drawer-content {
            padding: 16px;

            .amount-section {
                padding: 16px;
                margin-bottom: 16px;

                .amount-value {
                    font-size: 28px;
                }
            }

            .invoice-form {
                .form-section {
                    padding: 16px;
                    margin-bottom: 12px;

                    .section-title {
                        font-size: 14px;
                        margin-bottom: 12px;
                    }
                }
            }

            .submit-section {
                padding: 16px;

                .submit-btn {
                    height: 44px;
                    font-size: 15px;
                }
            }
        }
    }
}
</style>
