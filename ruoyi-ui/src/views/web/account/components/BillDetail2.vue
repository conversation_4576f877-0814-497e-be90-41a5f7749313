<template>
    <el-drawer :title="title" :visible.sync="drawer" size="750px">
        <div class="bill-drawer">
            <el-table :data="tableData" border>
                <el-table-column prop="inputTokenQuota" label="输入Tokens数量"></el-table-column>
                <el-table-column prop="outputTokenQuota" label="输出Tokens数量"></el-table-column>
            </el-table>
        </div>
    </el-drawer>
</template>
<script>
import {getBillDetailList} from "@/api/web";

export default {
    name: "BillDetail",
    data() {
        return {
            drawer: false,
            billId: '',
            title: '',
            tableData: [],
        }
    },
    methods: {
        init(billId) {
            this.billId = billId;
            this.title = '费用明细详情'
            this.drawer = true;
            this.loadData();
        },
        loadData() {
            let params = {
                billId: this.billId
            }

            getBillDetailList(params).then(response => {
                console.log('账单详情', response)
                this.tableData = response.data;
            })
        },
    }
}
</script>
<style lang="scss">
.bill-drawer {
    box-sizing: border-box;
    width: 100%;
    height: calc(100vh - 100px);
    background: #F3F5FA;
    padding: 0 20px;

    .bill-title {
        font-size: 16px;
        font-weight: 550;
        color: #000;
        line-height: 42px;
    }
}
</style>
