<template>
    <el-dialog title="微信扫码支付" :visible.sync="dialogVisible" width="450px"
               :close-on-click-modal="false"
               :close-on-press-escape="false" @close="closeDialog">
        <div class="flex-column flex-columnA ai-center jc-center">
            <el-image :src="qrcodeurl" style="width: 250px;height: 250px"></el-image>
            <div class="tips">请使用手机打开微信扫描二维码完成支付</div>
            <div class="custom-btn" @click="queryOrder">已完成支付</div>
        </div>
    </el-dialog>
</template>
<script>
import {queryOrder} from "@/api/product/order";

export default {
    name: "QrCodeDialog",
    emits: ['refresh'],
    data() {
        return {
            dialogVisible: false,
            qrcodeurl: '',
            orderNumber: ''
        }
    },
    methods: {
        init(item) {
            // console.log('item', item)
            this.dialogVisible = true;
            this.qrcodeurl = item.qrcodeurl;
            this.orderNumber = item.orderNumber;
        },
        queryOrder(orderNumber) {
            // 查询支付状态
            queryOrder({
                outTradeNo: this.orderNumber
            }).then(response => {
                // console.log('支付结果状态', response)
                if (response.data.tradeState === 'SUCCESS') {
                    this.$message({message: response.data.tradeStateDesc, type: 'success'})
                } else {
                    this.$message({message: response.data.tradeStateDesc, type: 'error'})
                }

                this.$emit('refresh', true)
                this.dialogVisible = false;
            })
        },
        closeDialog() {
            this.$emit('refresh', true);
            this.dialogVisible = false;
        }
    }
}
</script>
<style scoped lang="scss">
.flex-columnA {
    gap: 10px;

    .tips {
        font-size: 14px;
        color: #626066;
    }

    .custom-btn {
        width: 80px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background: rgba(230, 162, 60, 1);
        color: #FFFFFF;
        border-radius: 5PX;
        font-size: 12px;
        cursor: pointer;

        &:hover {
            background: rgba(230, 162, 60, 0.8);
        }
    }
}
</style>
