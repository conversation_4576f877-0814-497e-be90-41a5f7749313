<template>
    <div class="edit-bank">
        <el-dialog title="编辑银行卡" :visible.sync="dialogVisible" width="800px" :before-close="handleClose"
                   destroy-on-close :close-on-click-modal="false">
            <div class="edit-bank custom-scrollbar">
                <el-scrollbar class="custom-scrollbar">
                    <el-form ref="ruleForm" :model="form" :rules="rules" label-width="100px">
                        <el-form-item label="开户名称" prop="accountName">
                            <el-input v-model="form.accountName" placeholder="请输入开户名称"></el-input>
                        </el-form-item>
                        <el-form-item label="银行名称" prop="bank">
                            <el-input v-model="form.bank" placeholder="请输入银行名称"></el-input>
                        </el-form-item>
                        <el-form-item label="支行名称" prop="bankSub">
                            <el-input v-model="form.bankSub" placeholder="请输入支行名称"></el-input>
                        </el-form-item>
                        <el-form-item label="银行卡号" prop="bankAccount">
                            <el-input v-model="form.bankAccount" placeholder="请输入银行卡号"></el-input>
                        </el-form-item>
                        <el-form-item label="备注" prop="reason">
                            <el-input v-model="form.reason" placeholder="请输入备注" type="textarea"></el-input>
                        </el-form-item>
                        <el-form-item label="是否默认" prop="isDefault">
                            <el-switch v-model="isDefault"></el-switch>
                        </el-form-item>
                    </el-form>
                </el-scrollbar>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" @click="onSubmit" :loading="isDisabled">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import request from "../../../../utils/request";

export default {
    name: "EditBank",
    emits: ["refresh"],
    data() {
        return {
            dialogVisible: false,
            isDefault: false,
            isDisabled: false, // 禁止重复提交
            form: {
                "accountName": "", // accountName
                "bank": "", // 银行
                "bankAccount": "", // 银行卡号
                "bankSub": "", // 支行
                "id": null,
                "isDefault": false,
                "reason": "" // 备注
            },
            rules: {
                bankAccount: [
                    {required: true, message: '请输入银行卡号', trigger: 'blur'},
                    // {pattern: /^[0-9]{15,19}$/, message: '请输入正确的银行卡号', trigger: 'blur'}
                ],
                accountName: [
                    {required: true, message: '请输入开户名称', trigger: 'blur'},
                ],
                bank: [
                    {required: true, message: '请输入银行名称', trigger: 'blur'},
                ],
                bankSub: [
                    {required: true, message: '请输入支行名称', trigger: 'blur'},
                ]
            },
        }
    },
    watch: {
        isDefault: {
            deep: true,
            immediate: true,
            handler(newVal) {
                this.form.isDefault = newVal ? 1 : 0;
            }
        }
    },
    methods: {
        init(item) {
            this.isDisabled = false;
            this.dialogVisible = true;
            if (item) {
                this.form = Object.assign({}, item);
                this.isDefault = this.form.isDefault === 1;
            }
        },
        handleClose() {
            this.$refs.ruleForm.resetFields()
            this.dialogVisible = false

            this.form = {
                "accountName": "", // accountName
                "bank": "", // 银行
                "bankAccount": "", // 银行卡号
                "bankSub": "", // 支行
                "id": null,
                "isDefault": false,
                "reason": "" // 备注
            }
        },
        onSubmit() {
            if (this.isDisabled) {
                return;
            }
            this.$refs['ruleForm'].validate((valid) => {
                if (valid) {
                    this.isDisabled = true
                    request({
                        url: '/user/bank/addOrUpdate',
                        method: 'post',
                        data: this.form
                    }).then(response => {
                        console.log('编辑银行卡的结果', response)
                        this.$message.success('操作成功')
                        this.$emit('refresh')
                        this.handleClose()
                    }).finally(() => {
                        this.isDisabled = false
                    })

                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        }
    }
}
</script>

<style scoped lang="scss">

</style>
