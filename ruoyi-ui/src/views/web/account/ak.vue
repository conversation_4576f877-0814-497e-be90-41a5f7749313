<template>
    <div class="authentication">
        <el-row>
            <el-col :span="24">
                <el-button type="warning" @click="editAPI(1)">新建API密钥</el-button>
            </el-col>
        </el-row>

        <div class="auth-header flex-column">
            <div class="item">API密钥 是您访问 彩翼智能体应用 接口的凭证，具有该账户的完整权限，请您妥善保管。</div>
        </div>

        <div class="auth-body">
            <el-table
                :data="tableData"
                style="width: 100%">
                <el-table-column prop="secretKey" label="密钥(点击复制)">
                    <template #default="scope">
                        <span @click="copyText(scope.row)" style="cursor: pointer;">{{
                                maskString(scope.row.secretKey)
                            }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="keyName" label="描述"></el-table-column>
                <el-table-column prop="createTime" label="创建日期"></el-table-column>
                <el-table-column label="操作" width="120">
                    <template #default="scope">
                        <el-button @click.native.prevent="deleteRow(scope.row)" type="text" size="small"
                                   style="color: #e6a23c">
                            删除
                        </el-button>
                        <el-button @click.native.prevent="editAPI(2, scope.row)" type="text" size="small"
                                   style="color: #e6a23c">
                            编辑
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>
<script>
import {generateKey, deleteApiKey, getMyKey, redirectKey} from '@/api/web/index'
import {copyToClipboard} from '@/utils/index'

export default {
    data() {
        return {
            editTitle: '',
            tableData: []
        }
    },
    mounted() {
        this.getMyKey();
    },
    methods: {
        getMyKey() {
            getMyKey().then(response => {
                console.log('我的密钥信息', response)
                this.tableData = response.data || [];
            })
        },
        editAPI(type, item) {
            if (type === 1) {
                this.editTitle = '新建密钥'
            } else {
                this.editTitle = '编辑密钥'
            }

            this.$prompt('关于密钥用途等的补充说明', this.editTitle, {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputPlaceholder: '请输入描述信息',
                cancelButtonClass: "cancelButtonClass",
                confirmButtonClass: "confirmButtonClass",
            }).then(({value}) => {
                let params = {
                    description: value,
                    keyName: value,
                }

                if (item && item.keyId) {
                    params.keyId = item.keyId
                }

                if (type === 1) {
                    generateKey(params).then(response => {
                        console.log('创建结果', response)
                        this.$message({
                            type: 'success',
                            message: '操作成功'
                        });
                        this.getMyKey();
                    })
                } else {
                    redirectKey(params).then(response => {
                        this.$message({
                            type: 'success',
                            message: '操作成功'
                        });
                        this.getMyKey();
                    })
                }

            }).catch(() => {

            });
        },
        deleteRow(item) {
            console.log(item)
            this.$confirm('此操作将永久删除该密钥, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                deleteApiKey(item.keyId).then(response => {
                    this.$message({
                        type: 'success',
                        message: '删除成功'
                    });
                    this.getMyKey();
                })
            }).catch(() => {
                // this.$message({
                //     type: 'error',
                //     message: '删除失败'
                // });
            });
        },
        copyText(item) {
            copyToClipboard(item.secretKey)
            this.$message({
                type: 'success',
                message: '复制成功'
            });
        },
        maskString(str, visibleStart = 4, visibleEnd = 4) {
            return str.replace(
                new RegExp(`^(.{${visibleStart}})(.*)(.{${visibleEnd}})$`),
                (_, start, middle, end) => start + '*'.repeat(middle.length) + end
            );
        }
    }
}
</script>

<style lang="scss">
@import "@/assets/styles/flex-base";

.authentication {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;

    .auth-header {
        background: #fffbe6;
        border: 1px solid #ffe58f;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 14px;
        box-sizing: border-box;
        margin-top: 12px;
        margin-bottom: 12px;

        .item {
            line-height: 24px;
            margin-left: 12px;
        }
    }
}

.confirmButtonClass {
    background: #e6a23c !important;
    color: #FFF !important;
    border-color: #e6a23c !important;
}

.el-message-box__input {
    .el-input__inner {
        &:focus {
            border-color: #e6a23c !important;
        }
    }
}
</style>
