<template>
    <div class="markdown-content" v-html="compiledMarkdown"></div>
</template>

<script>
import {marked} from "marked"
import DOMPurify from 'dompurify'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css' // 选择你喜欢的样式主题

export default {
    name: 'MarkdownRenderer',
    props: {
        content: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            renderer: new marked.Renderer()
        }
    },
    created() {
        // 配置 marked
        marked.setOptions({
            renderer: this.renderer,
            highlight: async (code, lang) => {
                if (!lang) return hljs.highlightAuto(code).value

                try {
                    // 动态加载语言
                    const languageModule = await import(`highlight.js/lib/languages/${lang}`)
                    if (!hljs.getLanguage(lang)) {
                        hljs.registerLanguage(lang, languageModule.default)
                    }
                    return hljs.highlight(lang, code).value
                } catch (e) {
                    console.warn(`Failed to load language ${lang}`, e)
                    return hljs.highlightAuto(code).value
                }
            },
            pedantic: false,
            gfm: true,
            breaks: false,
            sanitize: false, // 我们使用 DOMPurify 代替
            smartLists: true,
            smartypants: false,
            xhtml: false
        })
    },
    computed: {
        compiledMarkdown() {
            if (this.content) {
                // 使用 marked 渲染 Markdown
                const rendered = marked(this.content)
                // 使用 DOMPurify 清理 HTML
                return DOMPurify.sanitize(rendered)
            }

            return ''
        }
    },
    watch: {
        content: {
            deep: true,
            immediate: true,
            handler(newVal) {
                if (newVal) {
                    // 当内容变化时，确保高亮生效
                    this.$nextTick(() => {
                        this.highlightCodeBlocks()
                    })
                }
            }
        }
    },
    mounted() {
        this.highlightCodeBlocks()
    },
    methods: {
        highlightCodeBlocks() {
            // 手动高亮所有代码块
            document.querySelectorAll('.markdown-content pre code').forEach((block) => {
                hljs.highlightBlock(block)
            })
        }
    }
}
</script>

<style scoped>
.markdown-content {
    /* 添加一些基本样式 */
    line-height: 1.6;
}

.markdown-content >>> pre {
    /* 覆盖 highlight.js 样式 */
    border-radius: 4px;
    padding: 16px;
    overflow: auto;
}

.markdown-content >>> code {
    /* 行内代码样式 */
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    background-color: rgba(27, 31, 35, 0.05);
    border-radius: 3px;
}

.markdown-content >>> a {
    color: #4493f8;
    text-decoration: underline;
}

/* 确保 Markdown 渲染的表格有边框 */
.markdown-content >>> table {
    border-collapse: collapse;
    margin: 1em 0;
}

.markdown-content >>> th,
.markdown-content >>> td {
    border: 1px solid #ddd;
    padding: 8px;
}

.markdown-content >>> th {
    background-color: #f2f2f2;
    text-align: left;
}
</style>
