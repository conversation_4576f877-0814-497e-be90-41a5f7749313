<template>
    <el-drawer :visible.sync="drawer" size="70%">
        <el-scrollbar class="hide-scrollbar">
            <iframe class="_iframe-box" ref="myIframe" sandbox="allow-same-origin allow-scripts allow-forms allow-downloads allow-popups" :src="link"
                    frameborder="0" @load="onIframeLoad"></iframe>
        </el-scrollbar>
    </el-drawer>
</template>

<script>
export default {
    name: "AuthIframe",
    props: {
        iframeLink: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            drawer: false,
            link: ''
        }
    },
    methods: {
        onIframeLoad() {
            console.log('iframe loaded');
        },
        init(url) {
            this.drawer = true
            this.link = url
        }
    }
}
</script>

<style scoped lang="scss">
.iframe-container {
    position: relative;
    overflow: hidden;
    padding-top: 56.25%; /* 16:9 宽高比 */
}

.iframe-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
}

.hide-scrollbar {
    width: 100%;
    height: calc(100vh - 100px);

    ._iframe-box {
        width: 100%;
        height: calc(100vh - 100px);
        box-sizing: border-box;
    }
}
</style>
