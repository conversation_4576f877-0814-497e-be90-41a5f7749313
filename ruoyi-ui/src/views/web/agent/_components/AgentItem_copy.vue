<template>
    <div class="agents-item flex-column" @click="handleAgentClick">
        <div class="_status" :class="'_status' + agent.status" v-if="showDetail">
            {{ statusList[agent.status] }}
        </div>

        <div class="agents-item-header flex-row">
            <div class="img-box">
                <el-image :src="agent.img || defaultImg">
                    <template #error>
                        <div class="default-icon">
                            <i class="el-icon-cpu"></i>
                        </div>
                    </template>
                </el-image>
            </div>
            <div class="flex-column info-box">
                <div class="name text-over">{{ agent.name }}</div>
                <div class="userName">By {{ agent.userName }} | {{ agent.chargeable === 0 ? '免费' : '收费' }}</div>
            </div>
        </div>

        <div class="agents-item-body text-over2">
            {{ agent.description }}
        </div>

        <div class="agents-item-footer flex-row ai-center jc-space-between">
            <div class="flex-row">
                <div class="_tag">
                    {{ agent.categoryName }}
                </div>

                <div class="_tag" v-if="agent.api === 1">
                    API
                </div>

                <div class="_tag" v-if="agent.mcp === 1">
                    MCP
                </div>

                <div class="_tag" v-if="agent.onLine === 1">
                    在线运行
                </div>
            </div>

            <div class="_time">{{ getDatePart(agent.createTime) }}</div>
        </div>

        <div class="agents-item-buttons flex-row ai-center" v-if="!showDetail">
            <template v-if="agent.platformBaseUrl && agent.onLine === 1">
                <div class="_button" @click.stop="agentOpen(agent)">运行</div>
            </template>

            <template v-if="showDetail">
                <div class="_button" @click.stop="handleItem(agent,1)">查看</div>
                <template v-if="agent.status === 2">
                    <div class="_button" @click.stop="handleItem(agent,2)">上架</div>
                </template>
                <template v-if="agent.status === 1">
                    <div class="_button" @click.stop="handleItem(agent,3)">下架</div>
                </template>
                <template v-if="agent.status === 0">
                    <div class="_button" @click.stop="handleItem(agent,4)">提交审核</div>
                </template>
            </template>

            <template v-if="!showDetail">
                <div class="_button">
                    <i class="iconfont icon-yunhang"></i>
                    {{ agent.useNum }}
                </div>
                <div class="_button">
                    <i class="el-icon-view"></i>
                    {{ agent.viewNum }}
                </div>
                <div class="_button" @click.stop="handleCollection(agent,1)" v-if="!agent.collection">
                    <i class="iconfont icon-shoucang"></i>
                    {{ agent.collectionNum || 0}}
                </div>
                <div class="_button" @click.stop="handleCollection(agent,2)" v-else>
                    <i class="iconfont icon-shoucang1"></i>
                    {{ agent.collectionNum || 0}}
                </div>
            </template>
        </div>

        <div class="agents-item-buttons flex-row ai-center"  v-if="showDetail">
            <template v-if="agent.platformBaseUrl && agent.onLine === 1">
                <div class="_button" @click.stop="agentOpen(agent)">运行</div>
            </template>

            <template v-if="showDetail">
                <div class="_button" @click.stop="handleItem(agent,1)">查看</div>
                <template v-if="agent.status === 2">
                    <div class="_button" @click.stop="handleItem(agent,2)">上架</div>
                </template>
                <template v-if="agent.status === 1">
                    <div class="_button" @click.stop="handleItem(agent,3)">下架</div>
                </template>
                <template v-if="agent.status === 0">
                    <div class="_button" @click.stop="handleItem(agent,4)">提交审核</div>
                </template>
            </template>

            <template v-if="!showDetail">
                <div class="_button">
                    <i class="iconfont icon-yunhang"></i>
                    {{ agent.useNum }}
                </div>
                <div class="_button">
                    <i class="el-icon-view"></i>
                    {{ agent.viewNum }}
                </div>
                <div class="_button" @click.stop="handleCollection(agent,1)" v-if="!agent.collection">
                    <i class="iconfont icon-shoucang"></i>
                    {{ agent.collectionNum || 0}}
                </div>
                <div class="_button" @click.stop="handleCollection(agent,2)" v-else>
                    <i class="iconfont icon-shoucang1"></i>
                    {{ agent.collectionNum || 0}}
                </div>
            </template>
        </div>
    </div>
</template>

<script>
import request from "../../../../utils/request";

export default {
    name: "AgentItem",
    emits: ["agent-click", "agent-open", "agent-detail"],
    props: {
        agent: {
            type: Object,
            required: true
        },
        showDetail: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            defaultImg: 'http://shuziyunxie-1313466856.cos.ap-guangzhou.myqcloud.com/picture_url/30675706-f429-4ae1-94a0-4334559bd693.jpg',
            statusList: {
                0: '草稿',
                1: '上架',
                2: '下架',
                3: '待审核',
                4: '未通过',
                5: '已禁用',
            },
        }
    },
    methods: {
        handleAgentClick(agent) {
            this.$emit('agent-click', agent)
        },
        agentOpen(agent) {
            this.$emit('agent-open', agent)
        },
        getDatePart(dateTimeStr) {
            if (typeof dateTimeStr !== 'string') return '';

            const trimmedStr = dateTimeStr.trim();
            if (!trimmedStr) return '';

            // 检查格式是否是 "YYYY-MM-DD HH:mm:ss" 或类似
            if (trimmedStr.includes(' ')) {
                return trimmedStr.split(' ')[0];
            }

            // 如果已经是纯日期，直接返回
            return trimmedStr;
        },
        handleItem(agent, type) {
            this.$emit('agent-detail', agent, type)
        },
        handleCollection(item, type) {
            item.collection = !item.collection;
            request({
                url: '/agent/market/collection/' + item.id,
                method: type === 1 ? 'post' : 'delete',
                data: {}
            }).then(response => {
                this.$message({
                    type: 'success',
                    message: '操作成功'
                });
            })
        }
    }
}
</script>

<style lang="scss">
@import "@/assets/styles/flex-base";

.agents-item {
    gap: 10px;
    border: 1px solid #eee;
    border-radius: 10px;
    box-sizing: border-box;
    padding: 15px 10px;
    min-height: 164px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    ._status {
        position: absolute;
        top: 0;
        right: 0;
        padding: 3px 10px;
        font-size: 12px;
        border-radius: 0 10px 0 10px;
        color: #fff;
        background-color: #666;

        /* 草稿状态 */
        &._status0 {
            color: #666;
            background-color: #f0f0f0;
        }

        /* 上架状态 */
        &._status1 {
            color: #28a745;
            background-color: #e6ffed;
        }

        /* 下架状态 */
        &._status2 {
            color: #409EFF;
            background-color: rgba(64, 158, 255, 0.1);
        }

        /* 待审核状态 */
        &._status3 {
            color: #ffc107;
            background-color: #fff9e6;
        }

        /* 拒绝状态 */
        &._status4 {
            color: #dc3545;
            background-color: #fff0f0;
        }
    }

    .agents-item-header {
        width: 100%;
        gap: 10px;

        .img-box {
            width: 48px;
            height: 48px;

            .el-image {
                width: 100%;
                height: 100%;
                border-radius: 10px;
                border: 1px solid #eeeeee;
            }

            .default-icon {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-size: 24px;
            }
        }

        .info-box {
            width: calc(100% - 58px);
            gap: 8px;

            .name {
                width: 100%;
                font-weight: 550;
                font-size: 16px;
                color: #000311;
                padding-top: 3px;
            }

            .userName {
                font-weight: 400;
                font-size: 12px;
                color: #848691;
            }
        }
    }

    .agents-item-body {
        width: 100%;
        font-weight: 400;
        font-size: 13px;
        line-height: 22px;
        letter-spacing: 1px;
        color: #64748B;
    }

    .agents-item-footer {
        gap: 3px;
        width: 100%;

        > .flex-row {
            gap: 3px;
        }

        ._tag {
            padding: 3px 10px;
            background: #f8f9fa;
            font-size: 11px;
            color: #626066;
            border-radius: 4px;
            box-sizing: border-box;
        }

        ._time {
            font-size: 12px;
            color: #848691;
        }
    }

    .agents-item-buttons {
        width: 100%;
        gap: 10px;

        ._button {
            font-size: 12px;
            color: #f8ac59;
        }
    }
}
</style>
