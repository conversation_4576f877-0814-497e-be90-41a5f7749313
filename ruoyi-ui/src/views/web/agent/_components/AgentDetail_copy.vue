<template>
    <el-drawer
        :title="agent.name"
        :visible.sync="visible"
        size="1000px"
        :before-close="handleClose"
        class="agent-detail-dialog">

        <div v-if="agent" class="agent-detail">
            <!-- 头部信息 -->
            <div class="detail-header">
                <div class="agent-icon">
                    <el-image
                        :src="agent.img"
                        fit="cover">
                        <template #error>
                            <div class="default-icon">
                                <i class="el-icon-cpu"></i>
                            </div>
                        </template>
                    </el-image>
                </div>

                <div class="agent-info">
                    <h2 class="agent-name">{{ agent.name }}</h2>
                    <p class="agent-description">{{ agent.description || '暂无描述' }}</p>

                    <div class="agent-meta">
                         <span class="meta-item">
                            By
                            {{ agent.userName }}
                        </span>
                        <span v-if="agent.categoryName" class="meta-item">
                            <i class="el-icon-collection-tag"></i>
                            {{ agent.categoryName }}
                        </span>
                        <span class="meta-item">
                            <i class="el-icon-view"></i>
                            {{ formatNumber(agent.viewNum) }} 次查看
                        </span>
                        <span class="meta-item">
                            版本号
                            {{ formatNumber(agent.version || 'V0.0.0') }}
                        </span>
                    </div>
                </div>
            </div>

            <div class="detail-body flex-row">
                <div class="_left flex-column">
                    <div class="_item" @click="scrollTo('detail1')" :class="domID === 'detail1' ? 'active' : ''">
                        详细描述
                    </div>
                    <div class="_item" @click="scrollTo('detail2')" :class="domID === 'detail2' ? 'active' : ''"
                         v-if="agent.chargeable === 1 && agent.charge">
                        收费说明
                    </div>
                    <div class="_item" @click="scrollTo('detail3')" :class="domID === 'detail3' ? 'active' : ''"
                         v-if="agent.api === 1 && agent.apiInfo">支持API
                    </div>
                    <div class="_item" @click="scrollTo('detail4')" :class="domID === 'detail4' ? 'active' : ''"
                         v-if="agent.mcp === 1 && agent.mcpInfo">支持MCP
                    </div>
                    <div class="_item" @click="scrollTo('detail5')" :class="domID === 'detail5' ? 'active' : ''"
                         v-if="agent.onLine === 1 && agent.onLineInfo">
                        在线运行
                    </div>
                </div>

                <div class="_right">
                    <el-scrollbar class="custom-scrollbar" ref="scrollbar">
                        <template v-if="agent.info">
                            <h2 class="agent-h2" id="detail1">详情描述</h2>
                            <div class="detail-content">
                                <markdown-renderer :content="agent.info"></markdown-renderer>
                            </div>
                        </template>

                        <template v-if="agent.chargeable === 1 && agent.charge">
                            <h2 class="agent-h2" id="detail2">收费说明</h2>
                            <div class="detail-content">
                                <markdown-renderer :content="agent.charge"></markdown-renderer>
                            </div>
                        </template>

                        <template v-if="agent.api === 1 && agent.apiInfo">
                            <h2 class="agent-h2" id="detail3">支持API</h2>
                            <div class="detail-content">
                                <markdown-renderer :content="agent.apiInfo"></markdown-renderer>
                            </div>
                        </template>

                        <template v-if="agent.mcp === 1 && agent.mcpInfo">
                            <h2 class="agent-h2" id="detail4">支持MCP</h2>
                            <div class="detail-content">
                                <markdown-renderer :content="agent.mcpInfo"></markdown-renderer>
                            </div>
                        </template>

                        <template v-if="agent.onLine === 1 && agent.onLineInfo">
                            <h2 class="agent-h2" id="detail5">在线运行</h2>
                            <div class="detail-content">
                                <markdown-renderer :content="agent.onLineInfo"></markdown-renderer>
                            </div>
                        </template>
                    </el-scrollbar>
                </div>
            </div>
        </div>
    </el-drawer>
</template>

<script>
import {marketQueryById} from "@/api/web/index.js";
import MarkdownRenderer from "./MarkdownRenderer.vue";

export default {
    name: "AgentDetail",
    components: {MarkdownRenderer},
    data() {
        return {
            visible: false,
            agent: {},
            activeTab: 'basic',
            formattedContent: '', // 添加格式化后的内容
            domID: '',
            scrollTimeout: null,
            sectionOffsets: []
        }
    },
    methods: {
        show(agent) {
            this.agent = agent;
            this.visible = true;
            this.activeTab = 'basic';
            this.loadDetail()
        },

        async loadDetail() {
            try {
                const response = await marketQueryById({
                    id: this.agent.id
                });
                this.agent = response.data;
            } catch (error) {
                console.error('加载详情失败:', error);
            }
        },

        handleClose() {
            this.visible = false;
            this.agent = {};
        },

        formatNumber(num) {
            if (!num) return '0';
            if (num >= 10000) {
                return (num / 10000).toFixed(1) + 'w';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'k';
            }
            return num.toString();
        },
        // 自定义节流函数
        throttle(func, limit) {
            let lastFunc;
            let lastRan;
            return function () {
                const context = this;
                const args = arguments;
                if (!lastRan) {
                    func.apply(context, args);
                    lastRan = Date.now();
                } else {
                    clearTimeout(lastFunc);
                    lastFunc = setTimeout(function () {
                        if ((Date.now() - lastRan) >= limit) {
                            func.apply(context, args);
                            lastRan = Date.now();
                        }
                    }, limit - (Date.now() - lastRan));
                }
            }
        },

        // 计算各个section的位置（考虑条件渲染）
        calculateOffsets() {
            this.$nextTick(() => {
                const offsets = [];
                const sections = ['detail1', 'detail2', 'detail3', 'detail4', 'detail5'];

                sections.forEach(id => {
                    const el = document.getElementById(id);
                    if (el) {
                        offsets.push({
                            id,
                            offset: el.offsetTop
                        });
                    }
                });

                this.sectionOffsets = offsets;
            });
        },

        // 滚动到指定锚点
        scrollTo(id) {
            const el = document.getElementById(id);
            if (el) {
                this.$refs.scrollbar.wrap.scrollTop = el.offsetTop;
                this.domID = id; // 手动点击时更新domID
            }
        },

        // 滚动处理函数
        handleScroll() {
            // 使用自定义节流
            if (this.scrollTimeout) {
                clearTimeout(this.scrollTimeout);
            }
            this.scrollTimeout = setTimeout(() => {
                this.doScrollCheck();
            }, 100);
        },

        // 实际的滚动检查逻辑
        doScrollCheck() {
            const scrollTop = this.$refs.scrollbar.wrap.scrollTop;
            if (!this.sectionOffsets || this.sectionOffsets.length === 0) return;

            // 从下往上查找第一个scrollTop >= offset的section
            for (let i = this.sectionOffsets.length - 1; i >= 0; i--) {
                if (scrollTop >= this.sectionOffsets[i].offset - 50) {
                    // 只有当domID变化时才更新，避免不必要的渲染
                    if (this.domID !== this.sectionOffsets[i].id) {
                        this.domID = this.sectionOffsets[i].id;
                    }
                    break;
                }
            }
        }
    },
    watch: {
        // 监听agent数据变化，重新计算位置
        agent: {
            handler() {
                this.$nextTick(() => {
                    this.calculateOffsets();
                });
            },
            deep: true
        }
    }
}
</script>

<style lang="scss">
// 移除 scoped，确保 Markdown 样式能正确应用
.agent-detail-dialog {
    .agent-detail {
        padding: 0 20px 20px;

        .detail-header {
            display: flex;
            gap: 20px;
            margin-bottom: 24px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;

            .agent-icon {
                width: 80px;
                height: 80px;
                border-radius: 12px;
                overflow: hidden;
                background: #f0f0f0;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;

                .el-image {
                    width: 100%;
                    height: 100%;
                    border-radius: 10px;
                }

                .default-icon {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    font-size: 24px;
                }
            }

            .agent-info {
                flex: 1;

                .agent-name {
                    font-size: 24px;
                    font-weight: 600;
                    color: #2c3e50;
                    margin: 0 0 8px 0;
                }

                .agent-description {
                    font-size: 16px;
                    color: #7f8c8d;
                    line-height: 1.5;
                    margin: 0 0 16px 0;
                }

                .agent-meta {
                    display: flex;
                    gap: 20px;
                    flex-wrap: wrap;

                    .meta-item {
                        display: flex;
                        align-items: center;
                        gap: 4px;
                        font-size: 14px;
                        color: #95a5a6;

                        i {
                            font-size: 16px;
                        }
                    }
                }
            }
        }

        .detail-body {
            width: 100%;
            gap: 15px;

            ._left {
                width: 120px;
                gap: 10px;

                ._item {
                    padding: 10px;
                    background: #f8f9fa;
                    font-size: 14px;
                    font-weight: 550;
                    color: #313033;
                    cursor: pointer;
                    border-radius: 5px;

                    &:hover {
                        color: #f8ac59;
                        font-size: 16px;
                    }

                    &.active {
                        color: #f8ac59;
                        font-size: 16px;
                    }
                }
            }

            ._right {
                flex: 1;

                .custom-scrollbar {
                    height: calc(100vh - 260px);

                    .agent-h2 {
                        color: #f8ac59;
                    }

                    .detail-content {
                        margin-bottom: 24px;
                    }
                }
            }
        }
    }
}
</style>
