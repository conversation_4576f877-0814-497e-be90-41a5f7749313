<template>
    <el-drawer
        :title="agent.name"
        :visible.sync="visible"
        size="1000px"
        :before-close="handleClose"
        class="agent-detail-dialog"
    >

        <template #title>
            <div class="detail-header">
                <div class="agent-icon">
                    <el-image :src="agent.img" fit="cover">
                        <template #error>
                            <div class="default-icon">
                                <i class="el-icon-cpu"></i>
                            </div>
                        </template>
                    </el-image>
                </div>

                <div class="agent-info flex-column jc-center">
                    <h2 class="agent-name">{{ agent.name }}</h2>

                    <div class="agent-meta">
                        <div class="meta-item">
                            By {{ agent.userName }}
                        </div>
                        <div v-if="agent.categoryName" class="meta-item">
                            <i class="el-icon-collection-tag"></i>
                            {{ agent.categoryName }}
                        </div>
                        <div class="meta-item">
                            <i class="iconfont icon-yanjing"></i>
                            {{ formatNumber(agent.viewNum) }} 次查看
                        </div>
                        <div class="meta-item" v-if="agent.platformBaseUrl && agent.onLine === 1">
                            <i class="iconfont icon-yunhang" style="font-size: 12px"></i>
                            {{ formatNumber(agent.useNum) }} 次运行
                        </div>

                        <div class="meta-item">
                            <i class="iconfont icon-shoucang1"></i>
                            {{ formatNumber(agent.collectionNum) }} 次收藏
                        </div>

                        <div class="meta-item" v-if="agent.api === 1">
                            API
                        </div>
                        <div class="meta-item" v-if="agent.mcp === 1">
                            MCP
                        </div>
                        <div class="meta-item" v-if="agent.platformBaseUrl && agent.onLine === 1">
                            在线运行
                        </div>

                        <div class="meta-item">
                            版本号 {{ agent.version || 'V0.0.0' }}
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <div class="agent-detail">
            <div class="agent-description">{{ agent.description || '暂无描述' }}</div>

            <div class="detail-body flex-row">
                <!-- 左侧菜单 -->
                <div class="_left flex-column">
                    <div
                        class="_item"
                        @click="scrollTo('detail1')"
                        :class="{active: activeSection === 'detail1'}"
                    >
                        详情介绍
                    </div>
                    <div
                        class="_item"
                        @click="scrollTo('detail2')"
                        :class="{active: activeSection === 'detail2'}"
                        v-if="agent.chargeable === 1 && agent.charge"
                    >
                        收费说明
                    </div>
                    <div
                        class="_item"
                        @click="scrollTo('detail3')"
                        :class="{active: activeSection === 'detail3'}"
                        v-if="agent.api === 1 && agent.apiInfo"
                    >
                        API
                    </div>
                    <div
                        class="_item"
                        @click="scrollTo('detail4')"
                        :class="{active: activeSection === 'detail4'}"
                        v-if="agent.mcp === 1 && agent.mcpInfo"
                    >
                        MCP
                    </div>
                    <div
                        class="_item"
                        @click="scrollTo('detail5')"
                        :class="{active: activeSection === 'detail5'}"
                        v-if="agent.onLine === 1 && agent.onLineInfo"
                    >
                        在线运行
                    </div>
                </div>

                <!-- 右侧内容区域 -->
                <div class="_right">
                    <el-scrollbar
                        class="custom-scrollbar"
                        ref="scrollbar"
                    >
                        <div id="detail1" class="section">
                            <h2 class="agent-h2">详情介绍</h2>
                            <div class="detail-content" v-if="agent.info">
                                <markdown-renderer :content="agent.info"></markdown-renderer>
                            </div>
                        </div>

                        <div id="detail2" class="section" v-if="agent.chargeable === 1 && agent.charge">
                            <h2 class="agent-h2">收费说明</h2>
                            <div class="detail-content">
                                <markdown-renderer :content="agent.charge"></markdown-renderer>
                            </div>
                        </div>

                        <div id="detail3" class="section" v-if="agent.api === 1 && agent.apiInfo">
                            <h2 class="agent-h2">API</h2>
                            <div class="detail-content">
                                <markdown-renderer :content="agent.apiInfo"></markdown-renderer>
                            </div>
                        </div>

                        <div id="detail4" class="section" v-if="agent.mcp === 1 && agent.mcpInfo">
                            <h2 class="agent-h2">MCP</h2>
                            <div class="detail-content">
                                <markdown-renderer :content="agent.mcpInfo"></markdown-renderer>
                            </div>
                        </div>

                        <div id="detail5" class="section" v-if="agent.onLine === 1 && agent.onLineInfo">
                            <h2 class="agent-h2">在线运行</h2>
                            <div class="detail-content">
                                <markdown-renderer :content="agent.onLineInfo"></markdown-renderer>
                            </div>
                        </div>
                    </el-scrollbar>
                </div>
            </div>
        </div>
    </el-drawer>
</template>

<script>
import {marketQueryById} from "@/api/web/index.js";
import MarkdownRenderer from "./MarkdownRenderer.vue";

export default {
    name: "AgentDetail",
    components: {MarkdownRenderer},
    data() {
        return {
            visible: false,
            agent: {},
            activeTab: 'basic',
            activeSection: 'detail1',
            scrollTimeout: null,
            sectionOffsets: []
        };
    },
    methods: {
        show(agent) {
            this.agent = agent;
            this.visible = true;
            this.activeTab = 'basic';
            this.loadDetail();

            this.$nextTick(() => {
                this.calculateOffsets();
                this.bindScrollEvent();
            });
        },

        async loadDetail() {
            try {
                const response = await marketQueryById({
                    id: this.agent.id
                });
                this.agent = response.data;

                this.$nextTick(() => {
                    this.calculateOffsets();
                });
            } catch (error) {
                console.error('加载详情失败:', error);
            }
        },

        handleClose() {
            if (this.$refs.scrollbar && this.$refs.scrollbar.wrap) {
                this.$refs.scrollbar.wrap.removeEventListener('scroll', this.handleScroll);
            }
            this.visible = false;
            this.agent = {};
        },

        formatNumber(num) {
            if (!num) return '0';
            if (num >= 10000) {
                return (num / 10000).toFixed(1) + 'w';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'k';
            }
            return num.toString();
        },

        bindScrollEvent() {
            if (this.$refs.scrollbar && this.$refs.scrollbar.wrap) {
                this.$refs.scrollbar.wrap.addEventListener('scroll', this.handleScroll);
            }
        },

        calculateOffsets() {
            const offsets = [];
            const sections = ['detail1', 'detail2', 'detail3', 'detail4', 'detail5'];

            sections.forEach(id => {
                const el = document.getElementById(id);
                if (el) {
                    offsets.push({
                        id,
                        offset: el.offsetTop
                    });
                }
            });

            this.sectionOffsets = offsets.sort((a, b) => a.offset - b.offset);
        },

        scrollTo(id) {
            const el = document.getElementById(id);
            if (el && this.$refs.scrollbar) {
                this.$refs.scrollbar.wrap.scrollTo({
                    top: el.offsetTop - 20,
                    behavior: 'smooth'
                });
                this.activeSection = id;
            }
        },

        handleScroll() {
            if (this.scrollTimeout) {
                clearTimeout(this.scrollTimeout);
            }

            this.scrollTimeout = setTimeout(() => {
                if (!this.$refs.scrollbar || !this.sectionOffsets.length) return;

                const scrollTop = this.$refs.scrollbar.wrap.scrollTop;
                const windowHeight = this.$refs.scrollbar.wrap.clientHeight;

                // 找到当前可见的section
                for (let i = 0; i < this.sectionOffsets.length; i++) {
                    const section = this.sectionOffsets[i];
                    const nextSection = this.sectionOffsets[i + 1];
                    const nextOffset = nextSection ? nextSection.offset - windowHeight / 2 : Infinity;

                    if (
                        (scrollTop >= section.offset - 100 && !nextSection) ||
                        (scrollTop >= section.offset - 100 && scrollTop < nextOffset)
                    ) {
                        if (this.activeSection !== section.id) {
                            this.activeSection = section.id;
                        }
                        break;
                    }
                }
            }, 100);
        }
    },
    watch: {
        agent: {
            handler() {
                this.$nextTick(() => {
                    this.calculateOffsets();
                });
            },
            deep: true
        }
    },
    beforeDestroy() {
        if (this.$refs.scrollbar && this.$refs.scrollbar.wrap) {
            this.$refs.scrollbar.wrap.removeEventListener('scroll', this.handleScroll);
        }
        if (this.scrollTimeout) {
            clearTimeout(this.scrollTimeout);
        }
    }
};
</script>

<style lang="scss">
.agent-detail-dialog {
    .el-drawer__body {
        overflow: hidden;
    }

    .el-drawer__header {
        align-items: self-start;

        .el-drawer__close-btn {
            width: 32px;
            height: 32px;
        }

        .detail-header {
            display: flex;
            gap: 20px;
            flex-shrink: 0;

            .agent-icon {
                width: 60px;
                height: 60px;
                border-radius: 12px;
                overflow: hidden;
                background: #f0f0f0;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;

                .el-image {
                    width: 100%;
                    height: 100%;
                    border-radius: 10px;
                }

                .default-icon {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    font-size: 24px;
                }
            }

            .agent-info {
                flex: 1;

                .agent-name {
                    font-size: 24px;
                    font-weight: 600;
                    color: #2c3e50;
                    margin: 0 0 8px 0;
                }

                .agent-meta {
                    display: flex;
                    gap: 20px;
                    flex-wrap: wrap;

                    .meta-item {
                        display: flex;
                        align-items: center;
                        gap: 4px;
                        font-size: 12px;
                        color: #95a5a6;

                        i {
                            font-size: 16px;
                        }
                    }
                }
            }
        }
    }

    .agent-detail {
        padding: 0 20px 20px;
        height: 100%;
        display: flex;
        flex-direction: column;

        .agent-description {
            font-size: 14px;
            color: #7f8c8d;
            line-height: 1.5;
            margin: 0 0 16px 0;
        }

        .detail-body {
            flex: 1;
            display: flex;
            gap: 20px;
            overflow: hidden;

            ._left {
                width: 160px;
                display: flex;
                flex-direction: column;
                gap: 8px;
                padding-right: 10px;
                border-right: 1px solid #eee;
                flex-shrink: 0;

                ._item {
                    padding: 12px 16px;
                    background: #f8f9fa;
                    font-size: 14px;
                    font-weight: 550;
                    color: #313033;
                    cursor: pointer;
                    border-radius: 6px;
                    transition: all 0.3s ease;
                    position: relative;

                    &:hover {
                        color: #f8ac59;
                        background: #f0f0f0;
                    }

                    &.active {
                        color: #fff;
                        background: #f8ac59;
                        font-weight: bold;
                        box-shadow: 0 2px 8px rgba(248, 172, 89, 0.3);

                        &::after {
                            content: '';
                            position: absolute;
                            right: -10px;
                            top: 50%;
                            transform: translateY(-50%);
                            width: 0;
                            height: 0;
                            border-top: 8px solid transparent;
                            border-bottom: 8px solid transparent;
                            border-right: 8px solid #fff;
                        }
                    }
                }
            }

            ._right {
                flex: 1;
                overflow: hidden;

                .custom-scrollbar {
                    height: calc(100vh - 240px);

                    .section {
                        margin-bottom: 40px;

                        .agent-h2 {
                            color: #f8ac59;
                            margin: 24px 0 16px;
                            padding-bottom: 8px;
                            border-bottom: 1px solid #eee;
                        }

                        .detail-content {
                            margin-bottom: 24px;
                        }
                    }
                }
            }
        }
    }
}
</style>
