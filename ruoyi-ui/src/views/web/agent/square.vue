<template>
    <div class="agent-square">
        <div class="plaza-top flex-row ai-center">
            <div class="_item">
                <el-button type="warning" plain icon="el-icon-s-operation" @click="isOpen = !isOpen">
                    {{ isOpen ? '隐藏筛选器' : '展开筛选器' }}
                </el-button>
            </div>
            <div class="_item">
                <el-input placeholder="请输入模型名称" v-model="searchParams.name">
                    <template #append>
                        <el-button icon="el-icon-search" @click="debouncedLoadMarketList"></el-button>
                    </template>
                </el-input>
            </div>
        </div>

        <div class="plaza-body flex-row">
            <div class="_left" v-if="isOpen">
                <el-scrollbar class="category-scrollbar">
                    <div class="category-box">
                        <div class="category-title">类型</div>
                        <div class="category-list flex-row ">
                            <div class="category-item"
                                 v-for="item in dict.type.agent_type"
                                 :class="{'_checked':searchParams.type === parseInt(item.value)}"
                                 @click="changeModel(5,item)">
                                {{ item.label }}
                            </div>
                        </div>
                    </div>

                    <div class="category-box">
                        <div class="category-title">价格</div>
                        <div class="category-list flex-row ">
                            <div class="category-item" :class="{'_checked':searchParams.chargeable === 0}"
                                 @click="changeModel(4,0)">
                                免费
                            </div>
                            <div class="category-item" :class="{'_checked':searchParams.chargeable === 1}"
                                 @click="changeModel(4,1)">
                                收费
                            </div>
                        </div>
                    </div>

                    <div class="category-box">
                        <div class="category-title">支持功能</div>
                        <div class="category-list flex-row ">
                            <div class="category-item" :class="{'_checked':searchParams.api === 1}"
                                 @click="changeModel(2,'api')">
                                API
                            </div>
                            <div class="category-item" :class="{'_checked':searchParams.mcp === 1}"
                                 @click="changeModel(2,'mcp')">
                                MCP
                            </div>
                            <div class="category-item" :class="{'_checked':searchParams.onLine === 1}"
                                 @click="changeModel(2,'onLine')">
                                在线运行
                            </div>
                        </div>
                    </div>

                    <div class="category-box">
                        <div class="category-title">类别</div>
                        <div class="category-list">
                            <template v-for="category in categoryList">
                                <div class="category-item" @click="changeModel(1,category)"
                                     :class="{'_checked':category.id === searchParams.categoryId}">
                                    {{ category.name }}
                                </div>
                            </template>
                        </div>
                    </div>

                </el-scrollbar>
            </div>
            <div class="_right" v-if="dataList.length > 0">
                <agent-item v-for="item in dataList" :key="item.id"
                            :agent="item"
                            @refresh="debouncedLoadMarketList"
                            @agent-click="handleAgentClick(item)"
                            @agent-open="agentOpen(item)">
                </agent-item>

                <!-- 加载更多提示 -->
                <div class="load-more-container" v-if="hasMore || loadingMore">
                    <div class="load-more-text" v-if="loadingMore">
                        <i class="el-icon-loading"></i>
                        正在加载更多...
                    </div>
                    <div class="load-more-text" v-else-if="hasMore">
                        滚动到底部加载更多
                    </div>
                </div>

                <!-- 没有更多数据提示 -->
                <div class="no-more-data" v-if="!hasMore && !loadingMore && dataList.length > 0">
                    <div class="no-more-text">没有更多数据了</div>
                </div>

                <!-- 滚动触发器 -->
                <div ref="scrollTrigger" class="scroll-trigger" v-if="hasMore && !loadingMore">
                    <!-- 这个元素用于触发滚动加载 -->
                </div>


            </div>

            <!--  没有数据的时候  -->
            <el-empty style="flex: 1" description="没有相关数据" v-if="dataList.length === 0 && !loading"></el-empty>

            <!-- 首次加载提示 -->
            <div class="loading-container" v-if="loading && dataList.length === 0">
                <i class="el-icon-loading"></i>
                正在加载数据...
            </div>
        </div>

        <!-- 智能体详情弹窗 -->
        <agent-detail ref="agentDetail"></agent-detail>

        <!--  授权弹窗  -->
        <auth-iframe ref="authIframe"></auth-iframe>
    </div>
</template>

<script>
import {agentAuthorization, agentCateList, agentMarketList} from "../../../api/web";
import AgentItem from "./_components/AgentItem.vue";
import AgentDetail from "./_components/AgentDetail.vue";
import AuthIframe from "./_components/AuthIframe.vue";

export default {
    name: "plaza",
    components: {AuthIframe, AgentDetail, AgentItem},
    dicts: ['agent_type'],
    data() {
        return {
            defaultIcon: 'https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/_upload/icon_supplier.png',
            isOpen: false,
            dataList: [],
            categoryList: [],
            searchParams: {
                pageNum: 1,
                pageSize: 25, // 改为合理的分页大小
                name: '',
                chargeable: '',
                onLine: '',
                mcp: '',
                api: '',
                categoryId: ''
            },
            authLink: '',
            loading: false,
            loadingMore: false, // 加载更多数据的状态
            hasMore: true, // 是否还有更多数据
            total: 0, // 总数据量
            debounceTimer: null,
            scrollTimer: null, // 滚动节流定时器
            scrollContainer: null, // 滚动容器引用
            observer: null // Intersection Observer 实例
        }
    },
    watch: {
        isOpen: {
            deep: true,
            immediate: true,
            handler(newVal) {
            }
        }
    },
    mounted() {
        this.loadMarketList();
        this.loadCateList();

        // 等待DOM渲染完成后添加滚动监听
        this.$nextTick(() => {
            this.addScrollListener();
            this.setupIntersectionObserver();
        });
    },
    beforeDestroy() {
        // 清理定时器
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        if (this.scrollTimer) {
            clearTimeout(this.scrollTimer);
        }
        // 移除滚动监听
        this.removeScrollListener();
        // 移除 Intersection Observer
        if (this.observer) {
            this.observer.disconnect();
        }
    },
    methods: {
        loadMarketList(isLoadMore = false) {
            if (isLoadMore) {
                this.loadingMore = true;
            } else {
                this.loading = true;
                // 重新加载时重置分页
                this.searchParams.pageNum = 1;
                this.hasMore = true;
            }

            agentMarketList(this.searchParams).then(response => {
                const newData = response.data.records || [];

                if (isLoadMore) {
                    // 加载更多时追加数据
                    this.dataList = [...this.dataList, ...newData];
                } else {
                    // 首次加载或筛选时替换数据
                    this.dataList = newData;
                }

                this.total = response.data.total || 0;

                // 判断是否还有更多数据
                this.hasMore = this.dataList.length < this.total;


                // 数据更新后重新设置 observer
                this.$nextTick(() => {
                    if (this.observer && this.$refs.scrollTrigger) {
                        this.observer.disconnect();
                        if (this.hasMore) {
                            this.observer.observe(this.$refs.scrollTrigger);
                        }
                    }
                });

            }).catch(error => {
                console.error('加载智能体列表失败:', error);
                this.$message.error('加载智能体列表失败');
            }).finally(() => {
                this.loading = false;
                this.loadingMore = false;
            })
        },
        loadCateList() {
            agentCateList().then(response => {
                this.categoryList = response.rows || [];
            }).catch(error => {
                console.error('加载分类失败:', error);
            })
        },
        // 加载更多数据
        loadMoreData() {
            if (this.loadingMore || !this.hasMore) {
                return;
            }

            this.searchParams.pageNum += 1;
            this.loadMarketList(true);
        },
        // 添加滚动监听
        addScrollListener() {
            // 查找 el-scrollbar 的滚动容器
            const scrollContainer = this.findScrollContainer();
            if (scrollContainer) {
                this.scrollContainer = scrollContainer;
                scrollContainer.addEventListener('scroll', this.handleScroll);
            } else {
                window.addEventListener('scroll', this.handleScroll);
            }
        },

        // 移除滚动监听
        removeScrollListener() {
            if (this.scrollContainer) {
                this.scrollContainer.removeEventListener('scroll', this.handleScroll);
            } else {
                window.removeEventListener('scroll', this.handleScroll);
            }
        },

        // 查找滚动容器
        findScrollContainer() {
            // 查找 .router-scrollbar 容器
            let container = document.querySelector('.router-scrollbar .el-scrollbar__wrap');
            if (container) {
                return container;
            }

            // 备用方案：查找其他可能的滚动容器
            container = document.querySelector('.el-scrollbar__wrap');
            if (container) {
                return container;
            }

            return null;
        },

        // 滚动监听（带节流）
        handleScroll(event) {
            // 节流处理，避免频繁触发
            if (this.scrollTimer) {
                return;
            }

            this.scrollTimer = setTimeout(() => {
                const target = event.target || this.scrollContainer || document.documentElement;
                const scrollTop = target.scrollTop;
                const scrollHeight = target.scrollHeight;
                const clientHeight = target.clientHeight;
                const distanceToBottom = scrollHeight - (scrollTop + clientHeight);

                // 当滚动到距离底部100px时触发加载更多
                if (distanceToBottom <= 100 && this.hasMore && !this.loadingMore) {
                    this.loadMoreData();
                }

                this.scrollTimer = null;
            }, 100);
        },

        // 设置 Intersection Observer（备用方案）
        setupIntersectionObserver() {
            if (!this.$refs.scrollTrigger) {
                return;
            }

            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadMoreData();
                    }
                });
            }, {
                root: null, // 使用视口作为根
                rootMargin: '100px', // 提前100px触发
                threshold: 0.1
            });

            this.observer.observe(this.$refs.scrollTrigger);
        },
        changeModel(type, item) {
            // type 1：类别  4：价格  2:功能
            switch (type) {
                case 1:
                    this.searchParams.categoryId = this.searchParams.categoryId === item.id ? '' : item.id;
                    break;
                case 4:
                    this.searchParams.chargeable = this.searchParams.chargeable === item ? '' : item;
                    break;
                case 2:
                    this.searchParams[item] = this.searchParams[item] === 1 ? '' : 1;
                    break;
                case 5:
                    this.searchParams.type = this.searchParams.type === parseInt(item.value) ? '' : parseInt(item.value);
                    break;
            }
            this.debouncedLoadMarketList();
        },
        debouncedLoadMarketList() {
            // 清除之前的定时器
            if (this.debounceTimer) {
                clearTimeout(this.debounceTimer);
            }
            // 设置新的定时器，300ms 后执行
            this.debounceTimer = setTimeout(() => {
                this.loadMarketList();
            }, 300);
        },
        handleAgentClick(agent) {
            // 处理智能体点击事件，可以跳转到详情页或直接使用
            console.log('点击智能体:', agent);
            // 这里可以添加跳转逻辑或打开使用对话框
            this.$refs.agentDetail.show(agent);
        },
        agentOpen(item) {
            this.$modal.confirm('是否授权获取相关信息？').then(() => {
                agentAuthorization({
                    agentId: item.id
                }).then(response => {
                    console.log('response', response)
                    this.authLink = response;
                    this.$refs.authIframe.init(response);
                })
            }).catch(() => {
            });
        }
    }
}
</script>

<style lang="scss">
@import "@/assets/styles/flex-base";

.agent-square {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;

    .plaza-top {
        position: sticky;
        background: #fff;
        top: 0;
        gap: 12px;
        z-index: 10;
        padding: 20px 0 20px;
        margin-top: -20px;

        ._item {
            &:last-child {
                width: 500px;
            }
        }


        .el-input__inner {
            &:focus {
                border-color: #e6a23c !important;
            }
        }
    }

    .plaza-body {
        gap: 20px;
        box-sizing: border-box;

        ._left {
            width: 260px;
            box-sizing: border-box;
            padding: 12px;
            border: 1px solid #ededed;
            border-radius: 10px;

            .category-scrollbar {
                width: 100%;
                height: calc(100vh - 185px);
                display: block;

                .el-scrollbar__wrap {
                    overflow-x: hidden !important;
                }
            }

            .category-box {
                width: 100%;
                box-sizing: border-box;
                margin-bottom: 24px;

                .category-title {
                    font-size: 14px;
                    color: #64748B;
                    line-height: 24px;
                    margin-bottom: 10px;
                }

                .category-list {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 12px;

                    .category-item {
                        height: 24px;
                        line-height: 24px;
                        text-align: center;
                        background: #f2f3f5;
                        border: 1px solid #ededed;
                        font-size: 14px;
                        color: #64748B;
                        cursor: pointer;
                        border-radius: 4px;

                        &._checked {
                            background: #e6a23c;
                            color: #ffffff;
                        }
                    }
                }
            }
        }

        ._right {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(330px, 1fr));
            gap: 20px;
            align-content: start;
            padding-top: 5px;
        }
    }
}

/* 加载更多相关样式 */
.load-more-container {
    grid-column: 1 / -1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;

    .load-more-text {
        color: #666;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 8px;

        .el-icon-loading {
            animation: rotating 2s linear infinite;
        }
    }
}

.no-more-data {
    grid-column: 1 / -1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;

    .no-more-text {
        color: #999;
        font-size: 14px;
        position: relative;

        &::before,
        &::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 60px;
            height: 1px;
            background: #e0e0e0;
        }

        &::before {
            left: -80px;
        }

        &::after {
            right: -80px;
        }
    }
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
    color: #666;
    font-size: 14px;
    gap: 8px;

    .el-icon-loading {
        animation: rotating 2s linear infinite;
    }
}

@keyframes rotating {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 滚动触发器样式 */
.scroll-trigger {
    grid-column: 1 / -1;
    height: 1px;
    width: 100%;
    background: transparent;
}


</style>
