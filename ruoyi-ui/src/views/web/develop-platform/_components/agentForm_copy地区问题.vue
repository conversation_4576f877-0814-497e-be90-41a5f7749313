<template>
    <div>
        <el-dialog :visible.sync="visible" :title="form.id ? '查看智能体' : '新增智能体111'" width="800px" top="5vh"
                   :close-on-click-modal="false" :destroy-on-close="true" @close="handleClose">
            <template #title>
                <div class="agent-form-title flex-column">
                    <div class="_top flex-row ai-center">
                        <div class="_title">{{ form.id ? '查看智能体' : '新增智能体' }}</div>
                        <div class="_status" :class="'_status' + status" v-if="status !== ''">
                            {{ statusList[status] }}
                        </div>
                        <div class="_tips" v-if="status === 4">说明：{{ auditReason }}</div>
                    </div>
                </div>
            </template>
            <el-form ref="dataFormRef" :model="form" :rules="rules" label-width="120px" size="medium"
                     v-loading="loading">
                <el-form-item label="产品名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入产品名称"/>
                </el-form-item>

                <el-form-item label="封面图片" prop="img">
                    <image-upload v-model="form.img" :limit="1"/>
                </el-form-item>

                <el-form-item label="产品分类" prop="categoryId">
                    <el-select
                        v-model="form.categoryId"
                        placeholder="请选择产品分类"
                        style="width: 100%"
                    >
                        <el-option
                            v-for="item in categoryOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="区域" prop="area">
                    <el-cascader ref="areaCascader" style="width: 350px" :props="areaProps"
                                 v-model="region" @change="handleAreaChange">
                    </el-cascader>
                </el-form-item>

                <el-form-item label="类型" prop="type">
                    <el-select v-model="form.type" style="width: 350px">
                        <el-option
                            v-for="dict in dict.type.agent_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="parseInt(dict.value)"
                        ></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="产品描述" prop="description">
                    <el-input
                        v-model="form.description"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入产品描述"
                    />
                </el-form-item>

                <el-form-item label="详细描述" prop="info">
                    <el-input
                        v-model="form.info"
                        type="textarea"
                        :rows="5"
                        placeholder="请输入产品详细描述(Markdown格式)"
                    />
                </el-form-item>

                <el-form-item label="是否收费" prop="chargeable">
                    <el-radio-group v-model="form.chargeable">
                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                    <el-input
                        v-if="form.chargeable === 1"
                        v-model="form.charge"
                        type="textarea"
                        :rows="5"
                        placeholder="请输入收费说明(Markdown格式)"
                    />
                </el-form-item>

                <el-form-item label="是否支持API" prop="api">
                    <el-radio-group v-model="form.api">
                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                    <el-input
                        v-if="form.api === 1"
                        v-model="form.apiInfo"
                        type="textarea"
                        :rows="5"
                        placeholder="请输入API说明(Markdown格式)"
                        style="margin-top: 10px"
                    />
                </el-form-item>

                <el-form-item label="是否支持MCP" prop="mcp">
                    <el-radio-group v-model="form.mcp">
                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                    <el-input
                        v-if="form.mcp === 1"
                        v-model="form.mcpInfo"
                        type="textarea"
                        :rows="5"
                        placeholder="请输入MCP说明(Markdown格式)"
                        style="margin-top: 10px"
                    />
                </el-form-item>

                <el-form-item label="是否在线运行" prop="onLine">
                    <el-radio-group v-model="form.onLine">
                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="运行地址" prop="platformBaseUrl" v-if="form.onLine === 1"
                              :required="onLineRequired">
                    <el-input
                        v-model="form.platformBaseUrl"
                        placeholder="请输入运行地址"
                        style="margin-top: 10px"
                    />
                </el-form-item>

                <el-form-item label="运行说明" prop="onLineInfo" v-if="form.onLine === 1">
                    <el-input
                        v-model="form.onLineInfo"
                        type="textarea"
                        :rows="5"
                        placeholder="请输入在线运行说明(Markdown格式)"
                        style="margin-top: 10px"
                    />
                </el-form-item>

                <el-form-item label="版本号" prop="version">
                    <el-input v-model="form.version" placeholder="请输入版本号"/>
                </el-form-item>
            </el-form>

            <div slot="footer" class="dialog-footer">
                <template v-if="form.id">
                    <el-button type="primary" v-if="status === 2" @click="submitUp(form)">上 架</el-button>
                    <el-button type="primary" v-if="status === 1" @click="submitDown(form)">下 架</el-button>
                    <el-button type="primary" v-if="status === 0" @click="submitExamine(form)">提交审核</el-button>
                    <el-button @click="visible = false">取 消</el-button>
                </template>
                <template v-if="!form.id || status === 0 || status === 2 || status === 4">
                    <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
                </template>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import {
    agentCategoryList,
    markeTaddOrUpdate,
    marketQueryById,
    submitDown,
    submitExamine,
    submitUp
} from "../../../../api/web";
import request from "../../../../utils/request";

export default {
    dicts: ['agent_type'],
    data() {
        // 自定义在线运行校验规则
        const validateOnlIne = (rule, value, callback) => {
            // 如果性别是男且年龄为空，则报错
            if (this.form.onLine === 1 && (value === '' || value === null || value === undefined)) {
                callback(new Error('运行地址是必填项'));
            } else {
                callback();
            }
        };

        return {
            loading: false,
            visible: false,
            form: {
                id: null,
                name: '',
                area: '',
                type: '',
                api: 0,
                apiInfo: '',
                categoryId: null,
                charge: '',
                chargeable: 0,
                description: '',
                img: 'https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/_upload/71221593-4693-4afd-a99f-5e5c82a7a68e.png',
                info: '',
                mcp: 0,
                mcpInfo: '',
                onLine: 0,
                onLineInfo: '',
                platformAgentId: '',
                platformBaseUrl: '',
                platformToken: '',
                recommend: 0,
                sort: 0,
                userId: null,
                version: ''
            },
            rules: {
                name: [
                    {required: true, message: '请输入产品名称', trigger: 'blur'},
                    {min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur'}
                ],
                categoryId: [
                    {required: true, message: '请选择产品分类', trigger: 'change'}
                ],
                description: [
                    {required: true, message: '请输入产品描述', trigger: 'blur'}
                ],
                info: [
                    {required: true, message: '请输入详情描述', trigger: 'blur'}
                ],
                charge: [
                    {required: true, message: '请输入产品收费说明', trigger: 'blur'}
                ],
                type: [
                    {required: true, message: '请选择类型', trigger: 'change'}
                ],
                platformBaseUrl: [
                    {
                        validator: validateOnlIne,
                        trigger: 'blur'
                    }
                ]
            },
            areaList: [{
                name: '全国'
            }],
            regionList: [],
            categoryOptions: [],
            area: [],
            region: [],
            auditReason: '',
            status: '',
            statusList: {
                0: '草稿',
                1: '上架',
                2: '下架',
                3: '待审核',
                4: '未通过',
            },
            areaProps: {
                checkStrictly: true,  // 允许独立选择任意级别
                lazy: true,
                expandTrigger: 'click', // 点击即可展开
                lazyLoad: async (node, resolve) => {
                    const {level, value} = node;
                    try {
                        const res = await this.loadRegion(level, value);
                        console.log('lazyLoad res', res)
                        const data = res.data.map(item => ({
                            label: item.name,
                            value: item.id,
                            leaf: level >= 1,  // 假设最多3级（0=省，1=市，2=区）
                            loaded: false       // 标记是否已加载子节点
                        }));
                        resolve(data);
                    } catch (error) {
                        console.error('加载地区失败:', error);
                        resolve([]);
                    }
                }
            }
        }
    },
    computed: {
        onLineRequired() {
            return this.form.onLine === 1;
        }
    },
    methods: {
        handleAreaChange() {
            const panelRefs = this.$refs.areaCascader.$refs.panel;
            const nodes = panelRefs.getCheckedNodes();

            if (nodes.length > 0) {
                const item = nodes[0];
                if (item.level === 1) {
                    console.log('nodes', nodes)
                    panelRefs.lazyLoad(panelRefs.getCheckedNodes()[0])
                }
            }
        },
        categoryList() {
            agentCategoryList().then(res => {
                this.categoryOptions = res.rows
            })
        }
        ,
        handleSubmit() {
            console.log('this.region', this.region)
            if (this.region.length > 0) {
                this.form.area = this.area.join(',')
            }

            this.$refs.dataFormRef.validate(valid => {
                if (valid) {
                    this.loading = true;
                    markeTaddOrUpdate(this.form).then(response => {
                        this.$message.success('操作成功')
                        this.visible = false
                        this.loading = false;
                        this.$emit('refresh')
                    }).catch(() => {
                        this.loading = false;
                    });
                } else {
                    this.$message.error('请检查表单填写是否正确')
                    return false
                }
            })
        }
        ,
        GetById() {
            marketQueryById({id: this.form.id}).then(res => {
                this.form = Object.assign({}, res.data)
                if (this.form.area) {
                    this.area = this.form.area.split(',')
                }
                this.auditReason = res.data.auditReason || ''
                this.status = res.data.status
            })
        }
        ,
        openDialog(id) {
            this.visible = true
            this.categoryList()
            this.loadAreaList()
            this.form.id = ''
            this.status = ''
            if (id) {
                // 编辑模式，填充表单数据
                this.form.id = id
                this.GetById()
            }
        }
        ,
        handleClose() {
            this.$refs.dataFormRef.resetFields()
            this.form = {
                id: null,
                name: '',
                area: '',
                type: '',
                api: 0,
                apiInfo: '',
                categoryId: null,
                charge: '',
                description: '',
                img: 'https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/_upload/71221593-4693-4afd-a99f-5e5c82a7a68e.png',
                info: '',
                mcp: 0,
                mcpInfo: '',
                onLine: 0,
                onLineInfo: '',
                platformAgentId: '',
                platformBaseUrl: '',
                platformToken: '',
                recommend: 0,
                sort: 0,
                userId: null,
                version: ''
            }
            this.visible = false
            this.$emit('close')
        }
        ,
        submitExamine(item) {
            submitExamine({
                ids: [item.id]
            }).then(response => {
                console.log('批量提交审核', response)
                if (response.code === 200) {
                    this.$message.success(response.msg)
                } else {
                    this.$message.error(response.msg)
                }
            }).finally(() => {
                this.GetById()
                this.$emit('refresh')
            })
        }
        ,
        submitDown(item) {
            submitDown({
                ids: [item.id]
            }).then(response => {
                console.log('批量下架', response)
                if (response.code === 200) {
                    this.$message.success(response.msg)
                } else {
                    this.$message.error(response.msg)
                }
            }).finally(() => {
                this.GetById()
                this.$emit('refresh')
            })
        },
        submitUp(item) {
            submitUp({
                ids: [item.id]
            }).then(response => {
                console.log('批量上架', response)
                if (response.code === 200) {
                    this.$message.success(response.msg)
                } else {
                    this.$message.error(response.msg)
                }
            }).finally(() => {
                this.GetById()
                this.$emit('refresh')
            })
        },
        loadAreaList() {
            request({
                url: '/areaCity/tree',
                method: 'get'
            }).then(response => {
                this.areaList = [{name: '全国'}]
                this.areaList = [...this.areaList, ...response.data]
                console.log(this.areaList)
            })
        },
        handleChange(ev) {
            console.log(ev)
        },
        async loadRegion(level, parentId) {
            return await request({
                url: '/areaCity/area',
                method: 'get',
                params: {
                    level: level + 1,
                    parentId: parentId
                }
            });
        }
    }
}
</script>
<style lang="scss" scoped>
.avatar-uploader {
    ::v-deep .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    ::v-deep .el-upload:hover {
        border-color: #409eff;
    }
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

::v-deep .el-dialog__body {
    max-height: calc(90vh - 180px) !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

::v-deep .el-dialog__wrapper {
    overflow: hidden;
}

.agent-form-title {
    gap: 10px;

    ._top {
        gap: 10px;

        ._status {
            color: #f8ac59;
            font-size: 12px;
            font-weight: bold;

            &._status0 {
                color: #666;
            }

            &._status1 {
                color: #28a745;
            }

            &._status2 {
                color: #409EFF;
            }

            &._status3 {
                color: #ffc107;
            }

            &._status4 {
                color: #dc3545;
            }
        }

        ._tips {
            font-size: 12px;
            color: #dc3545;
        }
    }
}
</style>
