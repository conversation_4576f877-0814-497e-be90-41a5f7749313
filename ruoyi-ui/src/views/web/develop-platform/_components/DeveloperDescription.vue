<template>
    <el-card class="box-card" shadow="never">
        <el-steps :active="active" :finish-status="finishStatus" :process-status="processStatus">
            <el-step title="认证说明"></el-step>
            <el-step title="填写资料"></el-step>
            <el-step title="支付费用"></el-step>
            <el-step title="完成"></el-step>
        </el-steps>

        <div class="developer-description" v-if="active === 0">
            <div class="title flex-row jc-space-between ai-center">
                开发者认证计划说明
            </div>
            <div class="auth-title">1. 认证简介</div>
            <div class="auth-item">
                开发者认证是由[广东沃云]推出的官方能力评估计划，旨在验证开发者在特定技术领域（如API集成、SDK开发、平台生态建设等）的专业技能。通过认证的开发者将获得官方资质认可，享受专属权益。
            </div>
            <div class="auth-title">2. 认证目标</div>
            <div class="auth-item">
                提升开发者的技术能力与行业竞争力
            </div>
            <div class="auth-item">
                构建高质量的开发者生态
            </div>
            <div class="auth-item">
                为合作伙伴/客户提供可信赖的技术服务保障
            </div>
            <div class="tips">
                注：本认证的最终解释权归[广东沃云]所有，条款可能随政策调整更新。
            </div>

            <div class="flex-row">
                <el-button type="warning" size="small" @click="handleClick()">
                    立即认证
                </el-button>
            </div>
        </div>

        <div class="developer-info" v-if="active === 1 || active === 3">
            <el-form ref="ruleForm" :model="authInfo" label-width="120px">
                <el-form-item label="类型">
                    个人
                </el-form-item>
                <el-form-item label="认证状态">
                    <el-tag size="small" type="primary" v-if="authInfo.status === 1">
                        <dict-tag :options="dict.type.user_verification_status" :value="authInfo.status"/>
                    </el-tag>
                    <el-tag size="small" type="success" v-if="authInfo.status === 3">
                        <dict-tag :options="dict.type.user_verification_status" :value="authInfo.status"/>
                    </el-tag>
                    <el-tag size="small" type="danger" v-if="authInfo.status === 2">
                        <dict-tag :options="dict.type.user_verification_status" :value="authInfo.status"/>
                    </el-tag>
                    <el-button type="text" size="small" v-if="authInfo.status === 2"
                               @click.stop="seeWhy()"
                               style="color: #f8ac59; margin-left: 10px;">
                        查看原因
                    </el-button>
                    <el-tag size="small" type="warning" v-if="authInfo.status === 9">
                        <dict-tag :options="dict.type.user_verification_status" :value="authInfo.status"/>
                    </el-tag>
                    <el-tag size="small" type="info" v-if="authInfo.status === 0">
                        <dict-tag :options="dict.type.user_verification_status" :value="authInfo.status"/>
                    </el-tag>
                </el-form-item>
                <el-form-item label="身份证姓名" prop="idName">
                    {{ authInfo.idName }}
                </el-form-item>
                <el-form-item label="身份证号码" prop="idNum">
                    {{ encryptIdCard(authInfo.idNum) }}
                </el-form-item>
                <el-form-item label="联系人手机号" prop="phoneNum">
                    {{ authInfo.phoneNum }}
                </el-form-item>
                <el-form-item label="联系人邮箱" prop="email">
                    {{ authInfo.email }}
                </el-form-item>
                <el-form-item label="证明材料" prop="credential">
                    <image-upload v-model="authInfo.credential" :limit="1" disabled/>
                </el-form-item>
                <el-form-item>
                    <el-button type="warning" size="small" @click="handleClick()" v-if="authInfo.status !== 0">
                        重新认证
                    </el-button>
                </el-form-item>
            </el-form>
        </div>

        <!--  支付认证费用  -->
        <div class="developer-info" v-if="active === 2">
            <el-form>
                <el-form-item label="支付类型">开发者认证</el-form-item>
                <el-form-item label="支付金额" style="margin-top: -20px">￥{{ currentPrice }}</el-form-item>
                <el-form-item>
                    <el-button type="warning" size="mini" @click="placeOrder">立即支付</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!--  充值弹出  -->
        <qr-code-dialog ref="qrCodeDialogRef" @refresh="refresh"></qr-code-dialog>
    </el-card>
</template>

<script>
import {listProduct} from "@/api/product/product";
import {createScanPayQrcode, paying, placeOrder, getMyOrderList} from "@/api/product/order";
import QrCodeDialog from "../../account/components/QrCodeDialog.vue";

export default {
    components: {QrCodeDialog},
    emits: ["auth", "why"],
    dicts: ['user_verification_status'],
    props: {
        list: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            active: 0,
            finishStatus: 'success',
            goodList: [],
            isDisabled: false,
            currentPrice: '',
            productId: '' // 认证商品的ID
        }
    },
    computed: {
        authInfo() {
            return this.list[0] || {}
        },
        processStatus() {
            if (this.authInfo.status === 0) {
                this.active = 1
                return 'process'
            } else if (this.authInfo.status === 1) {
                // 商品审核通过以后加载商品
                this.loadGoods();
                this.active = 2
                return 'process'

            } else if (this.authInfo.status === 2) {
                this.active = 1
                return 'error'
            } else if (this.authInfo.status === 3) {

                this.active = 3
                this.finishStatus = 'finish'
                return 'finish'

            } else if (this.authInfo.status === 9) {
                this.active = 1
                return 'error'
            } else {
                this.active = 0
                return 'process'
            }
        }
    },
    methods: {
        handleClick() {
            this.$emit("auth");
        },
        seeWhy() {
            this.$emit("why", this.authInfo.auditReason);
        },
        loadGoods() {
            listProduct({
                pageNum: 1,
                pageSize: 10,
                type: 5
            }).then(res => {
                console.log(res)
                this.goodList = res.rows;
                this.productId = this.goodList[0].productId
                this.currentPrice = this.goodList[0].currentPrice
            })
        },
        refresh(bool) {
            if (bool) {
                this.$store.dispatch('developerModel/loadList')
            }
        },
        placeOrder() {
            if (this.isDisabled) {
                return;
            }

            let params = {
                "isPlace": true,
                "payType": 0,
                "count": 1,
                "productId": this.productId,
            }

            this.isDisabled = true;
            placeOrder(params).then(response => {
                console.log('支付结果AAA', response)
                paying({
                    orderNumber: response.data.orderNum,
                    payType: 1 // 扫码
                }).then(responseA => {
                    console.log('微信回调结果', responseA)

                    createScanPayQrcode({
                        codeUrl: responseA.data
                    }).then(responseB => {
                        console.log('生成二维码结果', responseB)

                        let qrcodeUrl = 'data:image/jpg;base64,' + responseB.data;
                        this.$refs.qrCodeDialogRef.init({
                            qrcodeurl: qrcodeUrl,
                            orderNumber: response.data.orderNum
                        });
                    })
                })
            })
        },
        encryptIdCard(idCard) {
            return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
        }
    }
}
</script>

<style lang="scss">
@import "@/assets/styles/flex-base";

.box-card {
    width: 100%;

    .el-card__body {
        padding: 50px 100px;
    }
}

.developer-description {
    padding-top: 50px;

    .title {
        font-size: 18px;
        font-weight: bold;
        color: #313033;
        margin-bottom: 24px;
    }

    .auth-title {
        font-size: 14px;
        font-weight: bold;
        color: #313033;
        margin-bottom: 10px;
    }

    .auth-item {
        font-size: 14px;
        color: #313033;
        line-height: 24px;
        margin-bottom: 5px;
    }

    .tips {
        font-size: 16px;
        color: #e6a23c;
        line-height: 36px;
        margin-bottom: 24px;
    }

}

.developer-info {
    padding-top: 50px;
}

.auditReason {
    color: #f8ac59;
}
</style>
