<template>
    <el-dialog title="开发者认证" :visible.sync="dialogVisible" width="800px" :before-close="handleClose"
               destroy-on-close :close-on-click-modal="false">
        <div class="developer-certification custom-scrollbar">
            <el-scrollbar class="hide-scrollbar">
                <el-form ref="ruleForm" :model="form" :rules="rules" label-width="120px">
                    <el-form-item label="类型">
                        个人
                    </el-form-item>
                    <el-form-item label="身份证姓名" prop="idName">
                        <el-input v-model="form.idName" placeholder="真实姓名"></el-input>
                    </el-form-item>
                    <el-form-item label="身份证号码" prop="idNum">
                        <el-input v-model="form.idNum" placeholder="身份证号码"></el-input>
                    </el-form-item>
                    <el-form-item label="联系人手机号" prop="phoneNum">
                        <el-input v-model="form.phoneNum" placeholder="有效的手机号码"></el-input>
                    </el-form-item>
                    <el-form-item label="短信验证码" prop="code">
                        <div class="el-item-code">
                            <el-input v-model.trim="form.code" placeholder="请输入验证码"></el-input>
                            <div class="_code" @click.stop="getCode">
                                {{ btnText }}
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item label="联系人邮箱" prop="email">
                        <el-input v-model="form.email" placeholder="真实有效的邮箱地址"></el-input>
                    </el-form-item>
                    <el-form-item label="证明材料" prop="credential">
                        <image-upload v-model="form.credential" :limit="1"/>
                    </el-form-item>
                </el-form>
            </el-scrollbar>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="onSubmit">确 定</el-button>
        </div>
    </el-dialog>

</template>
<script>
import request from '@/utils/request'

export default {
    name: "DeveloperCertification",
    emits: ["close"],
    data() {
        // 严格的身份证校验函数
        const validateIdCardStrict = (rule, value, callback) => {
            if (!value) {
                return callback(new Error('请输入身份证号码'));
            }

            // 身份证号正则表达式（18位）
            const reg = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/;

            if (!reg.test(value)) {
                return callback(new Error('身份证格式不正确'));
            }

            // 校验码验证
            if (value.length === 18) {
                const idCardWi = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]; // 加权因子
                const idCardY = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2]; // 校验码对应值
                let sum = 0;

                for (let i = 0; i < 17; i++) {
                    sum += parseInt(value.substring(i, i + 1)) * idCardWi[i];
                }

                const idCardLast = value.substring(17).toUpperCase();
                const checkCode = idCardY[sum % 11];

                if (idCardLast !== checkCode.toString()) {
                    return callback(new Error('身份证校验码不正确'));
                }
            }

            callback();
        };

        // 邮箱校验函数（可选）
        const validateEmail = (rule, value, callback) => {
            if (!value) {
                // 如果没有输入，直接通过校验
                return callback();
            }

            // 邮箱正则表达式
            const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (reg.test(value)) {
                callback();
            } else {
                callback(new Error('请输入正确的邮箱地址'));
            }
        };


        return {
            dialogVisible: false,
            btnText: '获取验证码',
            canGetCode: true,
            seconds: 60,
            form: {
                "code": "",
                "credential": "",
                "email": "",
                "id": null,
                "idName": "",
                "type": 0, // 0 ：个人  1：企业
                "idNum": "",
                "phoneNum": "",
            },
            rules: {
                idName: [
                    {required: true, message: '请输入身份证姓名', trigger: 'blur'},
                ],
                idNum: [
                    {required: true, message: '请输入身份证号码', trigger: 'blur'},
                    {validator: validateIdCardStrict, trigger: 'blur'}
                ],
                phoneNum: [
                    {required: true, message: '请输入联系人手机号', trigger: 'blur'},
                    {pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur'}
                ],
                code: [
                    {required: true, message: '请输入短信验证码', trigger: 'blur'},
                    {min: 4, max: 4, message: '长度 4 个字符', trigger: 'blur'}
                ],
                email: [
                    {validator: validateEmail, trigger: ['blur', 'change']}
                ],
                credential: [
                    {required: true, message: '请上传身份证证件', trigger: 'change'}
                ]
            }
        };
    },
    methods: {
        init(data) {
            if (data) {
                this.form = Object.assign(this.form, data);
            }

            this.dialogVisible = true;
        },
        handleClose(done) {
            this.$emit('close')
            this.dialogVisible = false
        },
        onSubmit() {
            this.$refs['ruleForm'].validate((valid) => {
                if (valid) {
                    request({
                        url: '/dev/verification/submit',
                        method: 'post',
                        data: this.form
                    }).then(response => {
                        console.log('开发者认证结果', response)
                        if (response.code == 200) {
                            this.$message.success('提交成功')
                            this.handleClose()
                        } else {
                            this.$message.error(response.msg || '发生了错误')
                        }
                    })
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        getCode() {
            if (this.form.phoneNum) {
                if (this.canGetCode) {
                    this.canGetCode = false;
                    this.sendSms();

                    this.countdown(60, () => {
                        this.canGetCode = true;
                    })
                } else {
                    this.$message.error(`倒计时结束后再发送!`);
                }
            } else {
                this.$message.error(`请填写手机号!`);
            }
        },
        countdown(seconds, callback) {

            if (seconds <= 0) {
                callback();
                this.btnText = '重新获取';
                return;
            }
            this.btnText = seconds + '秒';
            setTimeout(() => this.countdown(seconds - 1, callback), 1000);
        },
        sendSms() {
            request({
                url: '/sms/send',
                method: 'get',
                params: {
                    phone: this.form.phoneNum
                }
            }).then(response => {
                console.log('eeeee', response)
            })
        }
    }
}
</script>

<style scoped lang="scss">
.developer-certification {
    .hide-scrollbar {
        height: calc(90vh - 200px);

        .el-item-code {
            position: relative;
            box-sizing: border-box;

            .el-input__inner {

                padding-right: 200px;
            }

            ._code {
                position: absolute;
                right: 1px;
                top: 12px;
                bottom: 12px;
                padding: 0 12px;
                color: #FFA459;
                font-size: 14px;
                border-left: 1px solid #eee;
                line-height: 16px;
                cursor: pointer;
                background: #FFFFFF;
            }
        }
    }
}
</style>
