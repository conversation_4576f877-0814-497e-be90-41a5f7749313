<template>
    <div class="withdraw-apply">
        <el-dialog title="提现申请" :visible.sync="dialogVisible" width="800px" :before-close="handleClose"
                   destroy-on-close :close-on-click-modal="false">
            <div class="withdraw-apply custom-scrollbar">
                <el-scrollbar class="custom-scrollbar">
                    <el-form class="apply-form" ref="ruleForm" :model="form" :rules="rules" label-width="100px">
                        <el-form-item label="可提现金额">
                            {{ info.agentBalance || 0 }}
                        </el-form-item>
                        <el-form-item label="提现金额" prop="amount">
                            <el-input v-model="form.amount" placeholder="请输入提现金额"></el-input>
                        </el-form-item>
                        <el-form-item label="提现方式" prop="type">
                            <el-select v-model="form.type"
                                       @change="handleTypeChange"
                                       placeholder="请选择提现方式">
                                <el-option
                                    v-for="dict in dict.type.account_withdraw_type"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="parseInt(dict.value)"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="提现银行卡" prop="bankId" v-if="form.type === 1">
                            <el-select v-model="bankId" placeholder="请选择提现银行卡">
                                <template v-for="n in bankList">
                                    <el-option :label="n.bankAccount" :value="n.id"></el-option>
                                </template>
                            </el-select>
                        </el-form-item>
                    </el-form>
                </el-scrollbar>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" @click="onSubmit">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import request from "@/utils/request";

export default {
    name: "WithdrawApply",
    emits: ["refresh"],
    dicts: ['account_withdraw_status', 'account_withdraw_type', 'account_withdraw_method'],
    props: {
        info: {
            type: Object,
            required: true
        }
    },
    data() {
        // 自定义校验规则：金额不能大于可提现金额
        const validateAmount = (rule, value, callback) => {
            if (!value) {
                return callback(new Error('提现金额不能为空'));
            }
            if (isNaN(value) || Number(value) <= 0) {
                return callback(new Error('请输入有效的正数金额'));
            }
            if (Number(value) > Number(this.info.agentBalance)) {
                return callback(new Error('提现金额不能超过可提现金额'));
            }
            callback(); // 校验通过
        };

        return {
            dialogVisible: false,
            isDefault: false,
            isDisabled: false, // 禁止重复提交
            bankList: [],
            form: {
                "amount": '',
                "bankId": '', // 校验用的，没有这个字段
                "bankForm": {
                    "accountName": "",
                    "bank": "",
                    "bankAccount": "",
                    "bankSub": "",
                    "qrCode": ""
                },
                "type": 1 // 提现类型 1银行卡 2支付宝 3微信
            },
            rules: {
                amount: [
                    {required: true, message: '请输入提现金额', trigger: 'blur'},
                    {validator: validateAmount, trigger: 'blur'}
                ],
                type: [
                    {required: true, message: '请选择提现方式', trigger: 'change'},
                ],
                // bankId: [
                //     {required: true, message: '请选择提现银行卡', trigger: 'change'},
                // ]
            },
            bankId: ""
        }
    },
    watch: {
        bankId(newVal) {
            this.form.bankId = newVal || ''
            if (newVal) {
                let item = this.bankList.find(n => n.id === newVal);
                Object.keys(this.form.bankForm).forEach(key => {
                    this.form.bankForm[key] = item[key]
                })
            } else {
                this.form.bankForm = {
                    "accountName": "",
                    "bank": "",
                    "bankAccount": "",
                    "bankSub": "",
                    "qrCode": ""
                };
            }
        }
    },
    methods: {
        init() {
            this.dialogVisible = true;
            this.loadBankList()
        },
        handleClose() {
            this.bankId = ''
            this.isDisabled = false;
            this.dialogVisible = false
            this.$refs.ruleForm.resetFields()
        },
        onSubmit() {
            if (this.isDisabled) {
                return;
            }

            if (this.form.type === 1 && !this.bankId) {
                this.$message.error('请选择提现银行卡')
                return;
            }
            this.$refs['ruleForm'].validate((valid) => {
                if (valid) {
                    this.isDisabled = true
                    request({
                        url: '/account/withdraw/apply',
                        method: 'post',
                        data: this.form
                    }).then(response => {
                        console.log('申请提现的结果', response)
                        this.$message.success('操作成功')
                        this.$emit('refresh')
                        this.handleClose()
                    }).finally(() => {
                        this.isDisabled = false
                    })

                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        loadBankList() {
            request({
                url: '/user/bank/page',
                method: 'get',
                params: {
                    pageNum: 1,
                    pageSize: 10000
                }
            }).then(response => {
                console.log('银行卡列表', response)
                this.bankList = response.rows
                let item = this.bankList.find(n => n.isDefault === 1);
                if (item) {
                    this.bankId = item.id
                }
            })
        },
        handleTypeChange(type) {
            this.bankId = ''
        }
    }
}
</script>
<style lang="scss">
.apply-form {
    .el-select {
        width: 100%;
    }
}
</style>
