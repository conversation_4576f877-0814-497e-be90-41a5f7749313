<template>
    <div class="my-agent">
        <el-scrollbar class="agent-scrollbar">
            <div class="my-agent-top flex-row ai-center">
                <el-select v-model="searchParams.status">
                    <el-option label="全部" value=""></el-option>
                    <el-option
                        v-for="dict in dict.type.agent_status"
                        :key="dict.value"
                        :label="dict.label"
                        :value="parseInt(dict.value)"
                    ></el-option>
                </el-select>

                <el-input class="search-input" placeholder="请输入智能体名称查询" size="large"
                          v-model="searchParams.name">
                    <template #append>
                        <el-button icon="el-icon-search" @click="loadData">
                            查询
                        </el-button>
                    </template>
                </el-input>
                <el-button type="warning" @click="$refs.formDialogRef.openDialog()">新增应用</el-button>
            </div>

            <div class="agent-grid">
                <agent-item v-for="item in dataList"
                            :key="item.id"
                            :agent="item"
                            :show-detail="true"
                            @refresh="loadData"
                            @agent-detail="handleEdit"
                            @agent-click="handleAgentClick(item)"
                            @agent-open="agentOpen(item)">
                </agent-item>
            </div>

            <!--  没有数据的时候  -->
            <el-empty description="没有相关数据" v-if="dataList.length === 0"></el-empty>
        </el-scrollbar>


        <!-- 智能体详情弹窗 -->
        <agent-detail ref="agentDetail"></agent-detail>

        <FormDialog ref="formDialogRef" @close="loadData"/>

        <!--  授权弹窗  -->
        <auth-iframe ref="authIframe"></auth-iframe>
    </div>
</template>
<script>
import FormDialog from './_components/agentForm.vue'
import AgentDetail from "../agent/_components/AgentDetail.vue";
import request from "@/utils/request";
import {agentAuthorization, submitDown, submitExamine, submitUp} from "../../../api/web";
import AuthIframe from "../agent/_components/AuthIframe.vue";
import AgentItem from "../agent/_components/AgentItem.vue";

export default {
    components: {
        AgentItem,
        AuthIframe,
        AgentDetail,
        FormDialog
    },
    dicts: ['ai_model_spec_type', 'agent_status'],
    data() {
        return {
            loading: false,
            dataList: [],
            searchParams: {
                name: '',
                status: ''
            }
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        loadData() {
            this.loading = true
            request({
                url: '/agent/market/getMyAgent',
                method: 'get',
                params: {
                    pageNum: 1,
                    pageSize: 10000,
                    ...this.searchParams
                }
            }).then(response => {
                console.log('我的智能体', response)
                this.dataList = response.data
            }).finally(() => {
                this.loading = false;
            })
        },
        handleAgentClick(agent) {
            // 处理智能体点击事件，可以跳转到详情页或直接使用
            console.log('点击智能体:', agent);
            // 这里可以添加跳转逻辑或打开使用对话框
            this.$refs.agentDetail.show(agent);
        },
        agentOpen(item) {
            // window.open(item.platformBaseUrl + '?id=' + item.platformAgentId, '_blank');
            this.$modal.confirm('是否授权获取相关信息？').then(() => {
                agentAuthorization({
                    agentId: item.id
                }).then(response => {
                    console.log('response', response)
                    this.authLink = response;
                    this.$refs.authIframe.init(response);
                    // this.$message.success('授权成功');
                    // window.open(item.platformBaseUrl + '?id=' + item.platformAgentId, '_blank');
                })
            }).catch(() => {
            });
        },
        handleStatus(item) {
            const status = item.status;
            console.log('status', status)

            if (status === 0) {
                this.submitExamine(item)
            } else if (status === 1) {
                this.submitDown(item)
            } else if (status === 2) {
                this.submitUp(item)
            }
        },
        submitExamine(item) {
            submitExamine({
                ids: [item.id]
            }).then(response => {
                console.log('批量提交审核', response)
                if (response.code === 200) {
                    this.$message.success(response.msg)
                } else {
                    this.$message.error(response.msg)
                }
            }).finally(() => {
                this.loadData()
            })
        },
        submitDown(item) {
            submitDown({
                ids: [item.id]
            }).then(response => {
                console.log('批量下架', response)
                if (response.code === 200) {
                    this.$message.success(response.msg)
                } else {
                    this.$message.error(response.msg)
                }
            }).finally(() => {
                this.loadData()
            })
        },
        submitUp(item) {
            submitUp({
                ids: [item.id]
            }).then(response => {
                console.log('批量上架', response)
                if (response.code === 200) {
                    this.$message.success(response.msg)
                } else {
                    this.$message.error(response.msg)
                }
            }).finally(() => {
                this.loadData()
            })
        },
        handleEdit(item, type) {
            switch (type) {
                case 1:
                    this.$refs.formDialogRef.openDialog(item.id)
                    break;
                case 2:
                    this.submitUp(item);
                    break;
                case 3:
                    this.submitDown(item);
                    break;
                case 4:
                    this.submitExamine(item);
                    break;
                case 5:
                    break;
            }

        }
    }
}
</script>

<style scoped lang="scss">
.my-agent {
    .agent-scrollbar {
        width: 100%;
        height: calc(100vh - 56px);

        .my-agent-top {
            padding: 10px 20px;
            background: #fff;
            z-index: 10;
            position: sticky;
            top: 0;
            border-bottom: 1px solid #ededed;
            gap: 20px;

            .search-input {
                width: 500px;
            }
        }

        .agent-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(330px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            margin-top: 24px;
            padding: 0 20px;
        }
    }
}
</style>
