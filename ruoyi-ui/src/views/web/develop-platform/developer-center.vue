<template>
    <div class="developer-center flex-column jc-center ai-center">
        <div class="flex-row developer-body">
            <developer-description @auth="handleAuth" @why="handleWhy"
                                   :list="$store.state.developerModel.list">
            </developer-description>
        </div>

        <!--  认证弹窗  -->
        <developer-certification ref="developerCertificationRef" @close="loadList"></developer-certification>
    </div>
</template>

<script>
import DeveloperDescription from "./_components/DeveloperDescription.vue";
import DeveloperCertification from "./_components/DeveloperCertification.vue";
import {getMyAccount} from "@/api/web";

export default {
    name: "developer-center",
    components: {DeveloperCertification, DeveloperDescription},
    data() {
        return {
            searchParams: {
                pageNum: 1,
                pageSize: 10000,
                name: '',
                categoryId: null
            },
            dataList: [],
            accountInfo: {}
        }
    },
    mounted() {
        this.getMyAccount()
        this.loadList()
    },
    methods: {
        handleAuth() {
            this.$nextTick(() => {
                this.$refs.developerCertificationRef.init(this.$store.state.developerModel.info)
            })
        },
        async loadList() {
            await this.$store.dispatch('developerModel/loadList')
        },
        getMyAccount() { // 加载我的账户信息
            getMyAccount().then(res => {
                console.log('账户信息', res);
                this.accountInfo = res.data || {};
            })
        },
        handleWhy(content) {
            this.$alert(content, '未通过原因', {
                confirmButtonText: '知道了',
                confirmButtonClass: 'el-button--warning el-button--small',
                callback: action => {
                    console.log('action')
                }
            });
        }
    }
}
</script>

<style lang="scss">
@import "@/assets/styles/flex-base";

.developer-center {
    padding: 20px;

    .developer-body {
        gap: 20px;

        .box-card {
            width: 900px;
        }

        .agent-amount {
            flex: 1;
        }
    }
}

</style>
