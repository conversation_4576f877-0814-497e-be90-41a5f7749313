<template>
    <div class="my-earnings flex-row">
        <div class="left-panel">
            <div class="_left-header flex-column">
                <div class="flex-row account-box jc-space-between ai-center">
                    <div class="account-item flex-row jc-center ai-center">
                        <div class="_title">账户余额</div>
                        <div class="_amount">
                            {{ accountInfo.agentBalance || 0 }}
                        </div>
                        <div class="_label">元</div>
                    </div>
                    <el-button type="warning" size="small" @click="handleApply()">申请提现</el-button>
                </div>

                <div class="_left-bottom flex-row ai-center">
                    <div class="_span">总收益金额</div>
                    <div class="_span">{{ otherInfo.totalAmount || 0 }}</div>
                    <div class="_span">待打款金额</div>
                    <div class="_span">{{ otherInfo.withdrawSuccessAmount || 0 }}</div>
                    <div class="_span">提现中金额</div>
                    <div class="_span">{{ otherInfo.withdrawingAmount || 0 }}</div>
                    <div class="_span">已打款金额</div>
                    <div class="_span">{{ otherInfo.remittedAmount || 0 }}</div>
                </div>
            </div>

            <el-table border :data="tableData" style="width: 100%; margin-top: 12px" size="mini">
                <el-table-column prop="amount" label="提现金额"></el-table-column>
                <el-table-column prop="type" label="提现方式">
                    <template #default="scope">
                        <dict-tag :options="dict.type.account_withdraw_type" :value="scope.row.type"/>
                    </template>
                </el-table-column>
                <!--<el-table-column prop="realname" label="用户名称"></el-table-column>
                <el-table-column prop="bankName" label="银行名称"></el-table-column>
                <el-table-column prop="bankAccount" label="银行卡号"></el-table-column>-->
                <el-table-column prop="status" label="提现状态">
                    <template #default="scope">
                        <dict-tag :options="dict.type.account_withdraw_status" :value="scope.row.status"/>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="申请日期"></el-table-column>
                <el-table-column label="操作">
                    <template #default="scope">
                        <el-button type="text" @click="handleDetail(scope.row)" size="mini">查看详情</el-button>
                        <el-button type="text" @click="handleWithdraw(scope.row)"
                                   size="mini"
                                   v-if="scope.row.status === 0">
                            撤销提现
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                v-show="total>0"
                :total="total"
                layout="prev, pager, next"
                :page.sync="pageNum"
                :limit.sync="pageSize"
                @pagination="loadList"
            />

            <withdraw-apply ref="withdrawApplyRef" :info="accountInfo" @refresh="handleRefreshRecord"></withdraw-apply>
        </div>

        <div class="right-panel">
            <div class="record-title" v-if="recordList.length > 0">收入明细</div>
            <el-scrollbar class="custom-scrollbar"
                          style="width:100%; overflow-x:hidden !important;height: calc(100vh - 200px);">
                <div class="record-list">
                    <div v-for="(record, index) in recordList" :key="index" class="record-item">
                        <div class="record-header">
                            <div class="payment-type flex-row ai-center">
                                <el-image :src="record.agentImg"></el-image>
                                <span class="span span1">{{ record.agentName }}</span>
                            </div>
                            <div class="record-amount">¥ {{ record.totalCost }}</div>
                        </div>
                        <div class="record-footer">
                            <div class="record-time">平台服务费：{{ record.serviceFee }}</div>
                            <div class="record-time">收益项目：{{ record.project }}</div>
                        </div>
                    </div>
                </div>
            </el-scrollbar>
        </div>

        <!--  提现详情  -->
        <el-dialog title="提现申请详情" :visible.sync="dialogVisible" width="600px" :close-on-click-modal="false"
                   :destroy-on-close="true">
            <el-form :model="detailItem" label-width="100px">
                <el-form-item label="提现金额">
                    <el-input v-model="detailItem.amount" readonly></el-input>
                </el-form-item>
                <el-form-item label="用户名称">
                    <el-input v-model="detailItem.realname" readonly></el-input>
                </el-form-item>
                <el-form-item label="银行名称">
                    <el-input v-model="detailItem.bankName" readonly></el-input>
                </el-form-item>
                <el-form-item label="银行卡号">
                    <el-input v-model="detailItem.bankAccount" readonly></el-input>
                </el-form-item>
                <el-form-item label="提现状态">
                    <dict-tag :options="dict.type.account_withdraw_status" :value="detailItem.status"/>
                </el-form-item>
                <el-form-item label="申请日期">
                    <el-date-picker type="datetime" v-model="detailItem.createTime" readonly></el-date-picker>
                </el-form-item>
                <el-form-item label="审核说明">
                    <el-input v-model="detailItem.remark" type="textarea" readonly resize="none" :rows="4"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关 闭</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import request from "../../../utils/request";
import {getMyAccount} from "../../../api/web";
import WithdrawApply from "./_components/WithdrawApply.vue";

export default {
    name: "my-earnings",
    components: {WithdrawApply},
    dicts: ['account_withdraw_status', 'account_withdraw_type'],
    data() {
        return {
            accountInfo: {},
            pageSize: 10,
            pageNum: 1,
            total: 0,
            tableData: [],
            recordList: [],
            totalRecord: 0,
            otherInfo: {},

            dialogVisible: false,
            detailItem: {}
        }
    },
    mounted() {
        this.getMyAccount()
        this.loadList()
        this.loadRecord()
        this.loadBillRecord()
    },
    methods: {
        loadList() {
            request({
                url: '/dev/verification/getPage',
                method: 'get',
                params: this.searchParams
            }).then(response => {
                console.log('开发者认证列表', response)
                this.dataList = response.rows || []
            })
        },
        getMyAccount() { // 加载我的账户信息
            getMyAccount().then(res => {
                console.log('账户信息', res);
                this.accountInfo = res.data || {};
            })
            // /bill/record/getDevEarnings
            request({
                url: '/bill/record/getDevEarnings',
                method: 'get',
                params: {
                    pageNum: this.pageNum,
                    pageSize: this.pageSize
                }
            }).then(response => {
                console.log('开发者收益', response)
                this.otherInfo = response.data || {}
            })
        },
        handleApply() {
            this.$refs.withdrawApplyRef.init()
        },
        loadRecord() {
            this.tableData = [];
            request({
                url: '/account/withdraw/getList',
                method: 'get',
                params: {
                    pageNum: this.pageNum,
                    pageSize: this.pageSize
                }
            }).then(response => {
                console.log('提现记录', response)
                this.tableData = response.rows
                this.total = response.total
            })
        },
        handleRefreshRecord() {
            this.pageNum = 1;
            this.loadRecord()
            this.getMyAccount()
        },
        loadBillRecord() {
            // 收益明细
            request({
                url: '/bill/record/getDevList',
                method: 'get',
                params: {
                    pageNum: 1,
                    pageSize: 99999
                }
            }).then(response => {
                console.log('收益明细', response)
                this.recordList = response.rows
                this.totalRecord = response.total
            })
        },
        handleDetail(item) {
            this.dialogVisible = true;
            this.detailItem = item;
        },
        handleWithdraw(item) {
            this.$confirm('确定取消当前提现申请?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                request({
                    url: '/account/withdraw/revokeApprove',
                    method: 'post',
                    data: {
                        ids: [item.id]
                    }
                }).then(response => {
                    this.$message({
                        type: 'success',
                        message: '操作成功'
                    });
                    this.handleRefreshRecord()
                })
            }).catch(() => {
                // this.$message({
                //     type: 'error',
                //     message: '删除失败'
                // });
            });
        }
    }
}
</script>

<style scoped lang="scss">
.my-earnings {
    padding: 20px;
    gap: 24px;

    .left-panel {
        flex: 1;

        ._left-header {
            background: #F3F5FA;
            box-sizing: border-box;
            padding: 20px;
            gap: 10px;
            margin-bottom: 12px;

            .account-box {
                box-sizing: border-box;
                gap: 20px;

                .account-item {
                    gap: 8px;
                    height: 40px;

                    ._title {
                        font-size: 14px;
                        font-weight: 550;
                        color: #333;
                    }


                    ._amount {
                        color: #f8ac59;
                        font-weight: bold;
                        font-size: 18px;
                    }

                    ._label {
                        font-size: 14px;
                        font-weight: 550;
                        color: #333;
                    }

                    &:not(:first-child) {
                        &:before {
                            position: absolute;
                            content: '';
                            width: 2px;
                            height: 50px;
                            left: -10px;
                            top: 0;
                            bottom: 0;
                            background: rgba(230, 162, 60, 0.3);
                        }
                    }
                }
            }

            ._left-bottom {
                gap: 15px;
                font-size: 14px;
                color: #333;

                ._span {
                    &:nth-child(2n) {
                        color: #f8ac59;
                        margin-right: 10px;
                    }
                }
            }
        }
    }

    .right-panel {
        width: 400px;
        background: #ffffff;
        border-radius: 8px;
        padding: 24px 5px 0;
        //box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);


        .balance-amount span {
            font-size: 16px;
            margin-left: 4px;
        }

        .record-title {
            font-size: 16px;
            color: #333333;
            margin-bottom: 16px;
        }

        .record-item {
            padding: 16px 0;
            border: 1px solid #e5e7eb;
            padding: 15px;
            margin-bottom: 12px;
            border-radius: 5px;
        }

        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .payment-type {
            gap: 8px;
            color: #666666;

            .el-image {
                width: 20px;
                height: 20px;
                border-radius: 5px;
            }
        }

        .record-amount {
            font-size: 16px;
            font-weight: 500;
            color: #333333;
        }

        .record-footer {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .record-time, .record-order {
            font-size: 14px;
            color: #999999;
        }
    }
}
</style>
