<template>
    <div class="help-document-container">
        <el-row :gutter="20">
            <!-- 左侧导航 -->
            <el-col :span="6">
                <div class="help-nav">
                    <!-- 搜索框 -->
                    <div class="search-box">
                        <el-input
                            placeholder="搜索帮助文档"
                            v-model="searchText"
                            @keyup.enter.native="handleSearch"
                        >
                            <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
                        </el-input>
                    </div>

                    <!-- 分类导航 -->
                    <el-menu
                        :default-active="activeCategory"
                        class="category-menu"
                        @select="handleCategorySelect"
                    >
                        <el-menu-item
                            v-for="category in categories"
                            :key="category.id"
                            :index="category.id"
                        >
                            <i :class="category.icon"></i>
                            <span slot="title">{{ category.name }}</span>
                        </el-menu-item>
                    </el-menu>
                </div>
            </el-col>

            <!-- 右侧内容 -->
            <el-col :span="18">
                <div class="help-content">
                    <!-- 文档列表 -->
                    <div v-if="!currentDoc" class="doc-list">
                        <h2>{{ currentCategoryName }} 文档</h2>
                        <el-collapse v-model="activeDoc" accordion>
                            <el-collapse-item
                                v-for="doc in filteredDocs"
                                :key="doc.id"
                                :name="doc.id"
                                @click.native="handleDocClick(doc)"
                            >
                                <template slot="title">
                                    <div class="doc-title">
                                        <i class="el-icon-document"></i>
                                        {{ doc.title }}
                                    </div>
                                </template>
                                <div class="doc-summary">{{ doc.summary }}</div>
                            </el-collapse-item>
                        </el-collapse>
                    </div>

                    <!-- 文档详情 -->
                    <div v-else class="doc-detail">
                        <div class="doc-header">
                            <el-button
                                type="text"
                                icon="el-icon-arrow-left"
                                @click="currentDoc = null"
                            >
                                返回列表
                            </el-button>
                            <h2>{{ currentDoc.title }}</h2>
                        </div>
                        <div class="doc-body" v-html="currentDoc.content"></div>
                        <div class="doc-footer">
                            <el-divider></el-divider>
                            <p>最后更新: {{ currentDoc.updateTime }}</p>
                            <el-button
                                type="text"
                                :icon="isFavorited ? 'el-icon-star-on' : 'el-icon-star-off'"
                                @click="toggleFavorite"
                            >
                                {{ isFavorited ? '已收藏' : '收藏' }}
                            </el-button>
                        </div>
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
export default {
    name: 'HelpDocument',
    data() {
        return {
            searchText: '',
            activeCategory: '1', // 默认选中的分类
            activeDoc: '', // 当前展开的文档
            currentDoc: null, // 当前查看的文档详情
            favorites: [], // 收藏的文档ID
            categories: [
                {id: '1', name: '入门指南', icon: 'el-icon-guide'},
                {id: '2', name: '功能使用', icon: 'el-icon-operation'},
                {id: '3', name: '常见问题', icon: 'el-icon-question'},
                {id: '4', name: '高级功能', icon: 'el-icon-magic-stick'},
                {id: '5', name: 'API参考', icon: 'el-icon-document-copy'}
            ],
            docs: [
                {
                    id: '101',
                    categoryId: '1',
                    title: '如何开始使用系统',
                    summary: '介绍系统的基本功能和初次使用的步骤',
                    content: '<h3>如何开始使用系统</h3><p>这里是详细的使用说明...</p>',
                    updateTime: '2023-05-15'
                },
                {
                    id: '102',
                    categoryId: '1',
                    title: '账户注册与登录',
                    summary: '如何注册新账户和登录系统',
                    content: '<h3>账户注册与登录</h3><p>这里是详细的账户说明...</p>',
                    updateTime: '2023-05-10'
                },
                {
                    id: '201',
                    categoryId: '2',
                    title: '数据导入功能',
                    summary: '如何使用数据导入功能批量上传数据',
                    content: '<h3>数据导入功能</h3><p>这里是详细的功能说明...</p>',
                    updateTime: '2023-05-18'
                },
                // 更多文档...
            ]
        }
    },
    computed: {
        // 当前分类名称
        currentCategoryName() {
            const category = this.categories.find(c => c.id === this.activeCategory)
            return category ? category.name : ''
        },
        // 当前分类下的文档
        currentCategoryDocs() {
            return this.docs.filter(doc => doc.categoryId === this.activeCategory)
        },
        // 过滤后的文档（根据搜索文本）
        filteredDocs() {
            if (!this.searchText) return this.currentCategoryDocs

            const searchText = this.searchText.toLowerCase()
            return 1
        },
        // 当前文档是否已收藏
        isFavorited() {
            return this.currentDoc && this.favorites.includes(this.currentDoc.id)
        }
    },
    methods: {
        // 选择分类
        handleCategorySelect(categoryId) {
            this.activeCategory = categoryId
            this.currentDoc = null
            this.activeDoc = ''
        },
        // 点击文档
        handleDocClick(doc) {
            this.currentDoc = doc
        },
        // 搜索文档
        handleSearch() {
            this.currentDoc = null
            this.activeDoc = ''
        },
        // 切换收藏状态
        toggleFavorite() {
            if (!this.currentDoc) return

            const index = this.favorites.indexOf(this.currentDoc.id)
            if (index === -1) {
                this.favorites.push(this.currentDoc.id)
                this.$message.success('已添加到收藏')
            } else {
                this.favorites.splice(index, 1)
                this.$message.info('已取消收藏')
            }
        }
    }
}
</script>

<style scoped>
.help-document-container {
    padding: 20px;
    height: 100%;
}

.help-nav {
    background: #fff;
    border-radius: 4px;
    padding: 10px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.search-box {
    margin-bottom: 20px;
}

.category-menu {
    border-right: none;
}

.help-content {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    min-height: 600px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.doc-list h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
}

.doc-title {
    font-weight: bold;
}

.doc-summary {
    color: #909399;
    font-size: 14px;
}

.doc-header {
    margin-bottom: 20px;
}

.doc-header h2 {
    margin: 10px 0;
}

.doc-body {
    line-height: 1.6;
}

.doc-footer {
    margin-top: 40px;
    color: #909399;
    font-size: 14px;
}
</style>
