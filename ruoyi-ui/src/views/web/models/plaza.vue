<template>
    <div class="plaza">
        <div class="plaza-top flex-row ai-center">
            <div class="_item">
                <el-button type="warning" plain icon="el-icon-s-operation" @click="isOpen = !isOpen">
                    {{ isOpen ? '隐藏筛选器' : '展开筛选器' }}
                </el-button>
            </div>
            <div class="_item">
                <el-input placeholder="请输入模型名称" v-model="searchParams.name">
                    <template #append>
                        <el-button icon="el-icon-search" @click="getModelList"></el-button>
                    </template>
                </el-input>
            </div>
        </div>

        <div class="plaza-body">
            <div class="_left" v-if="isOpen">
                <el-scrollbar class="category-scrollbar">
                    <div class="category-box">
                        <div class="category-title">类别</div>
                        <div class="category-list">
                            <template v-for="(category,categoryIndex) in categoryList">
                                <div class="category-item" @click="changeModel(1,category)"
                                     :class="{'_checked':category.checked}">
                                    {{ category.categoryName }}
                                </div>
                            </template>
                        </div>
                    </div>

                    <div class="category-box">
                        <div class="category-title">标签</div>
                        <div class="category-list">
                            <template v-for="(tag,tagIndex) in tagList">
                                <div class="category-item" @click="changeModel(2,tag)"
                                     :class="{'_checked':tag.checked}">{{ tag.tagName }}
                                </div>
                            </template>
                        </div>
                    </div>

                    <div class="category-box">
                        <div class="category-title">系列/厂商</div>
                        <div class="category-list">
                            <template v-for="(supplier,supplierIndex) in supplierList">
                                <div class="category-item" @click="changeModel(3,supplier)"
                                     :class="{'_checked':supplier.checked}">
                                    {{ supplier.supplierName }}
                                </div>
                            </template>
                        </div>
                    </div>

                    <div class="category-box">
                        <div class="category-title">价格</div>
                        <div class="category-list">
                            <template v-for="(price,priceIndex) in priceList">
                                <div class="category-item" @click="changeModel(4,price)"
                                     :class="{'_checked':price.checked}">{{ price.label }}
                                </div>
                            </template>
                        </div>
                    </div>

                    <div class="category-box">
                        <div class="category-title">上下文</div>
                        <div class="category-list">
                            <template v-for="(con,conIndex) in contextWindowList">
                                <div class="category-item" @click="changeModel(5,con)"
                                     :class="{'_checked':con.checked}">{{ con.label }}
                                </div>
                            </template>
                        </div>
                    </div>

                    <div class="category-box">
                        <div class="category-title">规格</div>
                        <div class="category-list">
                            <template v-for="(spec,specIndex) in specTypeList">
                                <div class="category-item" @click="changeModel(6,spec)"
                                     :class="{'_checked':spec.checked}">{{ spec.dictLabel }}
                                </div>
                            </template>
                        </div>
                    </div>

                    <div class="category-box">
                        <div class="category-title">发布日期</div>
                        <div class="category-list">
                            <template v-for="(date,dateIndex) in dateRangeList">
                                <div class="category-item" @click="changeModel(7,date)"
                                     :class="{'_checked':date.checked}">{{ date.label }}
                                </div>
                            </template>
                        </div>
                    </div>
                </el-scrollbar>
            </div>
            <div class="_right">
                <template v-for="(model,index) in modelList">
                    <div class="_item" @click="tapItem(model)">
                        <div class="item-top">
                            <el-image v-if="model.aiSupplier && model.aiSupplier.supplierIcon"
                                      :src="model.aiSupplier.supplierIcon">
                            </el-image>
                            <el-image v-else :src=" defaultIcon"></el-image>
                            <div class="item-info">
                                <div class="_name">{{ model.name }}</div>
                                <div class="company-price">
                                    <div class="_company" v-if="model.aiSupplier">
                                        <div>{{ model.aiSupplier.supplierName }}</div>
                                        <div style="padding: 0 5px;">|</div>
                                        <div
                                            v-if="model.aiModelPrice && model.aiModelPrice.outputUnitPrice && model.aiModelPrice.outputUnitPrice > 0">
                                            ￥{{ model.aiModelPrice.outputUnitPrice }} /
                                            {{ model.aiModelPrice.billingUnit }}
                                        </div>
                                        <div v-else>免费</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="item-desc text-over2">{{ model.description }}</div>

                        <div class="item-tag flex-row ai-center">
                            <div class="_tag" :class="'categories' + model.categoriesId">
                                {{ model.categoriesName }}
                            </div>
                            <template v-if="model.tags && model.tags.length > 0">
                                <template v-for="(tag,tIndex) in model.tags">
                                    <div class="_tag">{{ tag.tagName }}</div>
                                </template>
                            </template>
                            <div class="_tag gg" v-if="model.spec">{{ formatNumber(model.spec) }}</div>
                            <div class="_tag sxw" v-if="model.contextWindow">
                                {{ formatToK(model.contextWindow) }}
                            </div>
                        </div>
                    </div>
                </template>

                <!--  没有数据的时候  -->
                <el-empty description="没有相关数据" v-if="modelList.length === 0"></el-empty>
            </div>
        </div>

        <!--  详情  -->
        <plaza-detail ref="plazaDetailRef"></plaza-detail>
    </div>
</template>

<script>
import {getModelList, getTagList, getCategoriesList, getSupplierList, getSpecType} from '@/api/web'
import PlazaDetail from "@/views/web/models/components/PlazaDetail.vue";

export default {
    name: "plaza",
    components: {PlazaDetail},
    data() {
        return {
            defaultIcon: 'https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/_upload/icon_supplier.png',
            isOpen: false,
            modelList: [], // original data
            modelListOriginal: [],
            categoryList: [],
            tagList: [],
            supplierList: [],
            specTypeList: [],
            contextWindowList: [
                {label: '≥ 8K', value: 8, checked: false},
                {label: '≥ 16K', value: 16, checked: false},
                {label: '≥ 32K', value: 32, checked: false},
                {label: '≥ 128K', value: 128, checked: false},
            ],
            dateRangeList: [
                {label: '近30天', value: 30, checked: false},
                {label: '近90天', value: 90, checked: false},
            ],
            priceList: [
                {label: '只看免费', value: 1, checked: false},
                // {label: '可用赠费', value: 2, checked: false},
            ],
            searchParams: { // 查询的参数
                categoryIds: [],
                supplierIds: [],
                tagIds: [],
                contextWindow: '',
                dateRange: '',
                name: '',
                spec: '',
            },
            addPriceParams: -1,//  价格：免费的是0，不免费的是大于0
        }
    },
    watch: {
        isOpen: {
            deep: true,
            immediate: true,
            handler(newVal) {

            }
        }
    },
    mounted() {
        this.getModelList();
        this.getAllData();
    },
    methods: {
        getModelList() {
            getModelList(this.searchParams).then(response => {
                console.log('模型列表', response)
                this.modelList = response.data || [];
                this.modelListOriginal = JSON.parse(JSON.stringify(response.data)); // 深度备份一份

                if (this.addPriceParams > 0) {
                } else {
                    if (this.modelList == 1) {
                        this.modelList.filter(n => n.price === 0)
                        this.modelList.filter(n => n.price !== 0)
                    }
                }

            })
        },
        async getAllData() {
            let params = {};
            let data1 = await getCategoriesList({});
            let data2 = await getSupplierList({});
            let data3 = await getTagList({});
            let data4 = await getSpecType({});
            this.categoryList = data1.rows;
            this.supplierList = data2.rows;
            this.tagList = data3.rows;
            this.specTypeList = data4.data;
            console.log('categoryList', this.categoryList)
            console.log('supplierList', this.supplierList)
            console.log('tagList', this.tagList)
            console.log('specTypeList', this.specTypeList)
        },
        changeModel(type, item) {
            // type 1：类别  2：标签 3：系列/厂商 4：价格 5：上下文 6：规格 7：发布日期
            switch (type) {
                case 1:
                    this.categoryList = this.categoryList.map(n => ({
                        ...n,
                        checked: n === item ? !item.checked : n.checked
                    }));

                    const categoryIds = this.categoryList
                        .filter(item => item.checked)
                        .map(item => item.categoryId);

                    this.searchParams.categoryIds = categoryIds;
                    break;
                case 2:
                    this.tagList = this.tagList.map(n => ({
                        ...n,
                        checked: n === item ? !item.checked : n.checked
                    }));

                    const tagIds = this.tagList
                        .filter(item => item.checked)
                        .map(item => item.tagId);

                    this.searchParams.tagIds = tagIds;
                    break;
                case 3:
                    this.supplierList = this.supplierList.map(n => ({
                        ...n,
                        checked: n === item ? !item.checked : n.checked
                    }));

                    const supplierIds = this.supplierList
                        .filter(item => item.checked)
                        .map(item => item.supplierId);

                    this.searchParams.supplierIds = supplierIds;
                    break;
                case 4:
                    this.priceList = this.priceList.map(n => ({
                        ...n,
                        checked: n === item ? !item.checked : false
                    }));
                    // this.addPriceParams = this.addPriceParams === item.value ? -1 : item.value;
                    break;
                case 5:
                    this.contextWindowList = this.contextWindowList.map(n => ({
                        ...n,
                        checked: n === item ? !item.checked : false
                    }));

                    const item5 = this.contextWindowList.find(item => item.checked);
                    this.searchParams.contextWindow = item5 ? item5.value : ''
                    break;
                case 6:
                    this.specTypeList = this.specTypeList.map(n => ({
                        ...n,
                        checked: n === item ? !item.checked : false
                    }));

                    const item6 = this.specTypeList.find(item => item.checked);
                    this.searchParams.spec = item6 ? item6.dictValue : ''
                    break;
                case 7:
                    this.dateRangeList = this.dateRangeList.map(n => ({
                        ...n,
                        checked: n === item ? !item.checked : false
                    }));

                    const item7 = this.dateRangeList.find(item => item.checked);
                    this.searchParams.dateRange = item7 ? item7.value : ''
                    break;
            }

            this.getModelList();
        },
        tapItem(item) {
            this.$nextTick(() => {
                this.$refs.plazaDetailRef.init(item);
            })
        },
        formatToK(num) {
            if (num >= 1000) {
                return (num / 1000).toFixed(num % 1000 === 0 ? 0 : 1) + 'K';
            } else {
                num = 0
            }
            return num.toString();
        },
        formatNumber(num) {
            // 处理规格回显的
            if (num === 0) return '0';


            const absNum = Math.abs(num);
            let value, suffix;

            if (absNum >= 1000000000) {
                // 处理 B (十亿)
                value = Math.round(num / 100000000) / 10; // 四舍五入到 0.1B
                suffix = 'B';
            } else if (absNum >= 1000000) {
                // 处理 M (百万)
                value = Math.round(num / 100000) / 10; // 四舍五入到 0.1M
                suffix = 'M';
            } else if (absNum >= 1000) {
                // 处理 K (千)
                value = Math.round(num / 100) / 10; // 四舍五入到 0.1K
                suffix = 'K';
            } else {
                return num; // 小于 1000 直接返回
            }

            return value + suffix;
            // return {
            //     value: value,
            //     suffix: suffix
            // };
        }
    }
}
</script>

<style lang="scss">
@import "@/assets/styles/flex-base";

.plaza {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;

    .plaza-top {
        position: sticky;
        background: #fff;
        top: 0;
        gap: 12px;
        z-index: 10;
        padding: 20px 0 20px;
        margin-top: -20px;

        ._item {
            &:last-child {
                width: 500px;
            }
        }

        .el-input__inner {
            &:focus {
                border-color: #e6a23c !important;
            }
        }
    }

    .plaza-body {
        display: grid;
        grid-template-columns: auto 1fr;
        gap: 20px;
        box-sizing: border-box;

        ._left {
            width: 260px;
            box-sizing: border-box;
            padding: 12px;
            border: 1px solid #ededed;
            border-radius: 10px;

            .category-scrollbar {
                width: 100%;
                height: calc(100vh - 185px);
                display: block;

                .el-scrollbar__wrap {
                    overflow-x: hidden !important;
                }
            }

            .category-box {
                width: 100%;
                box-sizing: border-box;
                margin-bottom: 24px;

                .category-title {
                    font-size: 14px;
                    color: #64748B;
                    line-height: 24px;
                    margin-bottom: 10px;
                }

                .category-list {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 12px;

                    .category-item {
                        height: 24px;
                        line-height: 24px;
                        text-align: center;
                        background: #f2f3f5;
                        border: 1px solid #ededed;
                        font-size: 14px;
                        color: #64748B;
                        cursor: pointer;
                        border-radius: 4px;

                        &._checked {
                            background: #e6a23c;
                            color: #ffffff;
                        }
                    }
                }
            }
        }

        ._right {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            align-content: start;
            padding-top: 5px;

            ._item {
                background: #fff;
                box-sizing: border-box;
                padding: 12px;
                border: 1px solid #eee;
                border-radius: 10px;
                display: flex;
                flex-direction: column;
                gap: 10px;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    transform: translateY(-4px);
                    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
                }

                .item-top {
                    display: grid;
                    grid-template-columns: auto 1fr;
                    align-items: center;
                    height: 48px;
                    gap: 10px;

                    .el-image {
                        width: 40px;
                        height: 40px;
                    }

                    .item-info {
                        display: grid;
                        grid-template-rows: auto auto;
                        gap: 2px;

                        ._name {
                            font-size: 16px;
                            color: #1E293B;
                        }

                        .company-price {
                            display: flex;
                            align-items: center;

                            ._company {
                                display: flex;
                                align-items: center;
                                font-size: 12px;
                                color: #939099;
                                margin-right: 10px;
                            }

                            ._price {
                                font-size: 12px;
                                color: #626066;
                            }
                        }
                    }
                }

                .item-desc {
                    font-size: 12px;
                    color: #64748B;
                    line-height: 22px;
                    max-height: 44px;
                }

                .item-tag {
                    gap: 8px;

                    ._tag {
                        font-size: 12px;
                        padding: 3px 8px;
                        background: rgb(253, 236, 216);
                        border-radius: 5px;
                        box-sizing: border-box;
                        color: #e6a23c;
                    }
                }
            }
        }
    }

    /* 响应式布局 - 使用 CSS Grid 自动适配 */
    @media (max-width: 768px) {
        .plaza {
            padding: 10px;

            .plaza-top {
                display: grid;
                grid-template-columns: 1fr;
                gap: 12px;

                ._item {
                    &:last-child {
                        width: 100%;
                    }
                }
            }

            .plaza-body {
                display: grid;
                grid-template-columns: 1fr;
                gap: 15px;

                ._left {
                    width: 100%;
                    order: 2;

                    .category-scrollbar {
                        height: auto;
                        max-height: 300px;
                    }
                }

                ._right {
                    order: 1;
                    grid-template-columns: 1fr;
                    gap: 15px;
                }
            }
        }
    }

    @media (min-width: 769px) and (max-width: 1024px) {
        .plaza {
            .plaza-body {
                ._left {
                    width: 240px;
                }

                ._right {
                    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                }
            }
        }
    }

    @media (min-width: 1025px) and (max-width: 1440px) {
        .plaza {
            .plaza-body {
                ._right {
                    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
                }
            }
        }
    }

    @media (min-width: 1441px) {
        .plaza {
            .plaza-body {
                ._right {
                    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                }
            }
        }
    }

    /* 当筛选器关闭时的特殊处理 */
    .plaza-body:not(:has(._left)) {
        grid-template-columns: 1fr;
    }

    .plaza-body:not(:has(._left)) ._right {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    @media (min-width: 1025px) {
        .plaza-body:not(:has(._left)) ._right {
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        }
    }

    @media (min-width: 1441px) {
        .plaza-body:not(:has(._left)) ._right {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        }
    }
}
</style>
