<template>
    <el-drawer title="我是标题" :visible.sync="drawer" size="800px">
        <template #title>
            <div class="model-header flex-row ai-center">
                <div class="logo">
                    <el-image v-if="info.aiSupplier && info.aiSupplier.supplierIcon"
                              :src="info.aiSupplier.supplierIcon" style="width: 44px; height: 44px">
                    </el-image>
                    <el-image :src="defaultIcon" style="width: 44px; height: 44px" v-else></el-image>
                </div>
                <div class="model-title flex-column">
                    <div class="_top flex-row ai-center">
                        <div class="_title">{{ info.name }}</div>
                        <i class="el-icon-document-copy" title="复制" @click="copyText()"></i>
                    </div>
                    <div class="_bottom flex-row ai-center">
                        <div> {{ info.aiSupplier.supplierName }}</div>
                        <div style="padding: 0 5px;">|</div>
                        <div
                            v-if="info.aiModelPrice && info.aiModelPrice.outputUnitPrice && info.aiModelPrice.outputUnitPrice > 0">
                            ￥{{ info.aiModelPrice.outputUnitPrice }} / {{ info.aiModelPrice.billingUnit }}
                        </div>
                        <div v-else>免费</div>
                    </div>
                </div>
            </div>
        </template>

        <div class="model-content flex-column">
            <div class="text-container">
                <div ref="content" class="text-content" :class="{ 'collapsed': isCollapsed }">
                    {{ info.description }}
                </div>
                <el-link v-if="showToggle" type="primary" @click="toggleContent" class="toggle-link">
                    {{ isCollapsed ? '更多' : '收起' }}
                </el-link>
            </div>

            <div class="tags flex-row ai-center">
                <div class="_span" :class="'categories' + info.categoriesId">
                    {{ info.categoriesName }}
                </div>
                <template v-for="(tag,tagIndex) in info.tags">
                    <div class="_span">
                        {{ tag.tagName }}
                    </div>
                </template>
            </div>

            <div class="model-button">
                <el-button type="warning" @click="openPage(info.url)">API文档</el-button>
            </div>

            <div class="info">
                <el-divider>
                    <span class="divider-span">模型信息</span>
                </el-divider>
            </div>

            <div class="model-form">
                <el-form label-width="100px" label-position="left">
                    <el-form-item label="模型名称">
                        <div class="flex-column">
                            <span class="_span">{{ info.apiName }}</span>
                        </div>
                    </el-form-item>
                    <el-form-item label="价格">
                        <div class="flex-column" v-if="info.aiModelPrice">
                            <div class="_span" v-if="info.aiModelPrice.inputUnitPrice > 0">
                                输入：￥{{ info.aiModelPrice.inputUnitPrice }} / {{ info.aiModelPrice.billingUnit }}
                            </div>
                            <div class="_span" v-else>免费</div>
                            <div class="_span" v-if="info.aiModelPrice.outputUnitPrice > 0">
                                输出：￥{{ info.aiModelPrice.outputUnitPrice }} / {{ info.aiModelPrice.billingUnit }}
                            </div>
                            <div class="_span" v-else>免费</div>
                        </div>
                    </el-form-item>
                    <el-form-item label="上下文">
                        <div class="flex-column">
                            <span class="_span">{{ formatToK(info.contextWindow) }}</span>
                        </div>
                    </el-form-item>
                    <el-form-item label="支持能力">
                        <div class="flex-row ai-center abcd">
                            <div class="span">Agent Thought</div>
                            <div class="span">
                                <i class="el-icon-check" v-if="zcnl.agentThought === '1'"></i>
                                <i class="el-icon-close" v-else></i>
                            </div>
                        </div>
                        <div class="flex-row ai-center abcd">
                            <div class="span">Function calling</div>
                            <div class="span">
                                <i class="el-icon-check" v-if="zcnl.functionCalling === '1'"></i>
                                <i class="el-icon-close" v-else></i>
                            </div>
                        </div>
                        <div class="flex-row ai-center abcd">
                            <div class="span">Vision 支持</div>
                            <div class="span">
                                <i class="el-icon-check" v-if="zcnl.vision === '1'"></i>
                                <i class="el-icon-close" v-else></i>
                            </div>
                        </div>
                        <div class="flex-row ai-center abcd">
                            <div class="span">Stream function calling</div>
                            <div class="span">
                                <i class="el-icon-check" v-if="zcnl.streamFunctionCalling === '1'"></i>
                                <i class="el-icon-close" v-else></i>
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item label="发布日期">
                        <div class="flex-column">
                            <span class="_span">{{ info.createTime }}</span>
                        </div>
                    </el-form-item>
                    <el-form-item label="备案"
                                  v-if="info.aiModelFiling.region || info.aiModelFiling.modelName || info.aiModelFiling.filingUnit || info.aiModelFiling.filingNumber || info.aiModelFiling.filingTime">
                        <div class="flex-column">
                            <span class="_span" v-if="info.aiModelFiling.region">
                                属地:     {{ info.aiModelFiling.region }}
                            </span>
                            <span class="_span" v-if=" info.aiModelFiling.modelName">
                                模型名称:  {{ info.aiModelFiling.modelName }}
                            </span>
                            <span class="_span" v-if="info.aiModelFiling.filingUnit">
                                备案单位:  {{ info.aiModelFiling.filingUnit }}
                            </span>
                            <span class="_span" v-if="info.aiModelFiling.filingNumber">
                                备案号:    {{ info.aiModelFiling.filingNumber }}
                            </span>
                            <span class="_span" v-if="info.aiModelFiling.filingTime">
                                备案时间:  {{ info.aiModelFiling.filingTime }}
                            </span>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </el-drawer>
</template>
<script>
import {getModelDetail} from '@/api/web';
import {copyToClipboard} from '@/utils/index'

export default {
    name: "PlazaDetail",
    data() {
        return {
            drawer: false,
            defaultIcon: 'https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/_upload/icon_supplier.png',
            modelId: '',
            info: {
                aiModelPrice: {},
                aiModelFiling: {},
                aiSupplier: {},
            },
            zcnl: {
                vision: 0,
                streamFunctionCalling: 0,
                functionCalling: 0,
                agentThought: 0,
            },
            isCollapsed: true,
            showToggle: false
        }
    },
    methods: {
        init(item) {
            this.drawer = true;
            console.log(item)
            this.modelId = item.id;
            if (this.modelId) {
                this.loadData(this.modelId);
            }
        },
        checkOverflow() {
            this.$nextTick(() => {
                const contentEl = this.$refs.content;
                if (contentEl) {
                    // 检查内容是否溢出
                    this.showToggle = contentEl.scrollHeight > contentEl.clientHeight;
                }
            });
        },
        toggleContent() {
            this.isCollapsed = !this.isCollapsed;
        },
        loadData(id) {
            let params = {
                id: id
            }

            getModelDetail(params).then(response => {
                console.log('模型详情', response)
                this.info = response.data;
                this.info.aiModelPrice = this.info.aiModelPrice || {};
                this.info.aiModelFiling = this.info.aiModelFiling || {};
                this.info.aiSupplier = this.info.aiSupplier || {};

                let item = {
                    vision: this.info.vision || '0',
                    streamFunctionCalling: this.info.streamFunctionCalling || '0',
                    functionCalling: this.info.functionCalling || '0',
                    agentThought: this.info.agentThought || '0',
                }

                this.zcnl = item;

                this.checkOverflow();
            })
        },
        openPage(url) {
            if (url) {
                window.open(url)
            } else {
                this.$message({
                    type: 'warning',
                    message: '未配置文档地址'
                });
            }
        },
        formatToK(num) {
            if (num >= 1000) {
                return (num / 1000).toFixed(num % 1000 === 0 ? 0 : 1) + 'K';
            } else {
                num = 0
            }
            return num.toString();
        },
        copyText(item) {
            copyToClipboard(this.info.apiName)
            this.$message({
                type: 'success',
                message: '复制成功'
            });
        },
    }
}
</script>
<style scoped lang="scss">
@import "@/assets/styles/flex-base";

.model-header {
    box-sizing: border-box;
    gap: 12px;

    .model-title {
        color: #313033;
        gap: 6px;

        ._top {
            gap: 6px;

            .el-icon-share, .el-icon-document-copy {
                cursor: pointer;
            }
        }

        ._bottom {
            font-size: 12px;
            color: #939099;
        }
    }
}

.model-content {
    gap: 20px;
    padding: 0 20px 20px;
}

.desc {
    font-size: 14px;
    line-height: 24px;
    color: #666;
    text-align: justify;
}

.tags {
    gap: 8px;

    ._span {
        padding: 3px 8px;
        //background: #e6a23c;
        background: rgb(253, 236, 216);
        color: #e6a23c;
        font-size: 12px;
        border-radius: 4px;
    }
}


.info {
    padding-top: 20px;

    .divider-span {
        font-size: 20px;
        color: #626066;
    }
}

.model-form {
    .flex-column {
        ._span {
            color: #626066;
        }
    }
}

.nl-table {
    .cell {
        padding: 0 !important;
    }
}

.abcd {
    gap: 24px;
}

.text-container {
    position: relative;
    line-height: 1.6;

    .text-content {
        display: -webkit-box;
        -webkit-line-clamp: 9999; /* 初始显示所有行 */
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
        font-size: 13px;
        color: #626066;
    }

    .text-content.collapsed {
        -webkit-line-clamp: 2; /* 限制显示2行 */
    }

    .toggle-link {
        margin-top: 8px;
        display: inline-block;
    }
}
</style>
