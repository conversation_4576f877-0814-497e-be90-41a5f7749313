<template>
    <el-header class="model-header flex-row jc-space-between ai-center" height="56px">
        <div class="item left">
            彩翼智能体应用广场
            <!--            {{ currentRouteTitle }}-->
        </div>
        <div class="item flex-row ai-center">
            <div class="dev-title" @click="jumpPage('1')">开发者中心</div>
            <el-popover
                :width="200"
                popper-class="user-popover"
                trigger="hover"
                content="Bottom Left prompts info"
                placement="bottom-start"
            >
                <template #reference>
                    <el-image src="https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/default_avatar.jpg"
                              style="width: 32px;height: 32px;cursor: pointer;">
                    </el-image>
                </template>
                <template #default>
                    <div class="user-box flex-column jc-center ai-center">
                        <el-image src="https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/default_avatar.jpg"
                                  style="width: 32px;height: 32px;cursor: pointer;">
                        </el-image>
                        <div class="user-item"
                             style="background: #edf3fe; padding: 3px 8px 0; border-radius: 5px; font-size: 12px;">
                            账号
                        </div>
                        <div class="user-item user-line" style="text-align: center">
                            {{ $userInfo.phonenumber }}
                        </div>
                        <div class="user-item user-center user-line" v-if="$userInfo.userName === 'admin'"
                             @click="handleCommand('1')" style="padding-bottom: 10px"> 后台管理系统
                        </div>
                        <!--                        <div class="user-item user-center" @click="jumpPage('1')"> 开发者认证</div>-->
                        <!--                        <div class="user-item user-center" @click="jumpPage('2')"> 我的智能体</div>-->
                        <!--                        <div class="user-item user-center user-line" style="padding-bottom: 10px"-->
                        <!--                             @click="jumpPage('3')"> 我的收益-->
                        <!--                        </div>-->
                        <div class="user-item user-center" @click="handleCommand('2')"> 退出登录</div>
                    </div>
                </template>
            </el-popover>
        </div>
    </el-header>
</template>

<script>
import {mapGetters} from "vuex";

export default {
    data() {
        return {};
    },
    computed: {
        ...mapGetters(["userInfo"]),
        $userInfo() {
            if (typeof this.userInfo === "string") {
                return JSON.parse(this.userInfo);
            } else {
                return {};
            }
        },
        currentRouteTitle() {
            return this.$route.meta.title
        }
    },
    async mounted() {
        // 加载用户的开发者认证信息
        await this.$store.dispatch('developerModel/loadList')
        console.log(1, this.$userInfo)
    },
    methods: {
        handleCommand(ev) {
            if (ev === '1') {
                window.open('/aiadmin/index')
            } else if (ev === '2') {
                this.$confirm('确定注销并退出系统吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$store.dispatch('LogOut').then(() => {
                        location.href = '/login';
                    })
                }).catch(() => {
                });
            }
        },
        jumpPage(ev) {
            console.log(ev)
            if (ev === '1') {
                this.$router.push('/develop/develop-center')
            } else if (ev === '2') {
                this.$router.push('/develop/my-agent')
            } else if (ev === '3') {
                this.$router.push('/develop/my-earnings')
            }
        }
    },
};
</script>

<style lang="scss">
@import "@/assets/styles/flex-base";

.model-header {
    width: 100%;
    background-color: #edf3fe;

    .item {
        gap: 12px;

        &.left {
            font-size: 18px;
            font-weight: 700;
            color: rgb(51, 65, 85);
        }

        .dev-title {
            font-size: 12px;
            color: #313033;
            cursor: pointer;

            &:hover {
                font-weight: bold;
                color: #f8ac59;
            }
        }
    }
}

.user-popover {
    padding: 18px 0 !important;

    .user-box {
        gap: 5px;
        box-sizing: border-box;

        .user-item {
            box-sizing: border-box;
            cursor: pointer;

            &:hover {
                color: #f8ac59;
                font-weight: bold;
            }
        }

        .user-line {
            width: 100%;
            border-bottom: 1px solid #ededed;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }

        .user-center {
            width: 100%;
            padding: 0 15px;
        }
    }
}

</style>
