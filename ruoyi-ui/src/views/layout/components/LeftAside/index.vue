<template>
    <el-aside class="model-aside" width="220px">
        <div class="model-logo flex-row ai-center jc-center">
            <img :src="logo" alt="logo"/>
        </div>
        <el-scrollbar class="model-el-scrollbar">
            <el-menu
                router
                :default-active="activeIndex"
                class="el-menu-vertical"
                active-text-color="#eda23c"
            >
                <template v-for="(menu,index) in menuList">
                    <!-- 添加v-if条件判断 -->
                    <el-menu-item-group v-if="!(menu.path === '/develop' && !isDeveloper)">
                        <template #title>
                            <div class="_group-title">{{ menu.title }}</div>
                        </template>
                        <template v-for="(child,childIndex) in menu.children">
                            <el-menu-item :index="child.path">
                                <span class="iconfont" :class="child.icon"></span>
                                <span class="_label">{{ child.title }}</span>
                            </el-menu-item>
                        </template>
                    </el-menu-item-group>
                </template>
            </el-menu>
        </el-scrollbar>
    </el-aside>
</template>

<script>
import logo from '@/assets/logo/logo.png'
import {mapGetters} from 'vuex' // 如果需要从vuex获取登录状态

export default {
    data() {
        return {
            logo: logo,
            activeIndex: '',
            isCollapse: false,
            openedMenus: ["/models", "/playground", "/account", "/agent", "/develop"],
            menuList: [
                {
                    title: "智能体",
                    icon: "dashboard",
                    path: "/agent",
                    children: [
                        {
                            title: "智能体广场",
                            icon: "icon-guangchang",
                            path: "/agent/square",
                        }
                    ]
                },
                {
                    title: "模型",
                    icon: "dashboard",
                    path: "/models",
                    children: [
                        {
                            title: "模型广场",
                            icon: "icon-moxingguangchang",
                            path: "/models/plaza",
                        }
                    ]
                },
                {
                    title: "开发平台",
                    icon: "dashboard",
                    path: "/develop",
                    children: [
                        {
                            title: "开发者中心",
                            icon: "icon-kaifazhezhongxin_kaifazhezhongxin",
                            path: "/develop/develop-center",
                        },
                        {
                            title: "我的智能体",
                            icon: "icon-zhinengti",
                            path: "/develop/my-agent",
                        },
                        {
                            title: "我的收益",
                            icon: "icon-shouyi",
                            path: "/develop/my-earnings",
                        }
                    ]
                },
                {
                    title: "体验中心",
                    icon: "dashboard",
                    path: "/playground",
                    children: [
                        {
                            title: "文本对话",
                            icon: "icon-wenbenduihua",
                            path: "/playground/chat",
                        }
                    ]
                },
                {
                    title: "账户管理",
                    icon: "dashboard",
                    path: "/account",
                    children: [
                        {
                            title: "实名认证",
                            icon: "icon-shimingrenzheng",
                            path: "/account/authentication",
                        },
                        {
                            title: "API密钥",
                            icon: "icon-miyueguanli",
                            path: "/account/ak",
                        },
                        {
                            title: "余额充值",
                            icon: "icon-huiyuanchongzhi",
                            path: "/account/expensebill",
                        },
                        {
                            title: "费用账单",
                            icon: "icon-zhangdanguanli",
                            path: "/account/bills",
                        },
                        {
                            title: "发票开具",
                            icon: "icon-fapiao",
                            path: "/account/invoice",
                        },
                        {
                            title: "银行卡管理",
                            icon: "icon-yinxingqia",
                            path: "/account/bank",
                        }
                    ]
                }
            ]
        }
    },
    computed: {
        isDeveloper() {
            return this.$store.state.developerModel.info && this.$store.state.developerModel.info.status === 3
        }
    },
    watch: {
        '$route': {
            deep: true,
            immediate: true,
            handler(to) {
                if (to.path.includes('/account/authentication')) {
                    this.activeIndex = '/account/authentication'
                } else if (to.path.includes('/playground/chat')) {
                    this.isCollapse = true;
                    this.activeIndex = to.path;
                } else {
                    this.activeIndex = to.path;
                }
                console.log('this.activeIndex', this.activeIndex)
            }
        }
    }
}
</script>

<style lang="scss">
@import "@/assets/styles/flex-base";

.model-aside {
    padding: 0;
    margin: 0;
    border-radius: 0;
    max-width: 200px;

    .model-logo {
        height: 56px;
        font-size: 13px;
        font-weight: bold;
        text-align: center;
        background: #edf3fe;
        cursor: pointer;
        border-right: 1px solid #ededed;

        img {
            width: 36px;
            height: 36px;
            border-radius: 4px;
        }
    }

    .model-el-scrollbar {
        width: 100%;
        height: calc(100vh - 56px);
        box-sizing: border-box;
        border-right: 1px solid #ededed;
        background: #fff;

        .el-menu {
            border-right: none;


            span.iconfont {
                padding-right: 3px;
            }

            ._group-title {
                font-size: 14px;
                padding-top: 10px;
                padding-bottom: 5px;
            }
        }

        ._label {
            color: #313033;
        }

        .iconfont {
            margin-right: 6px;
        }


        .is-active.el-menu-item {
            ._label {
                color: #e6a23c !important;
                font-weight: 550;
            }
        }
    }
}
</style>
