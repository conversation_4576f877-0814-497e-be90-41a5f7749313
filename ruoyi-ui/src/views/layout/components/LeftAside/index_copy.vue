<template>
    <el-aside class="model-aside" width="auto">
        <div class="model-logo flex-row ai-center jc-center">
            <img src="https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/_upload/84b6329d-91e4-4587-8e2c-dd1e2690d282.png" alt="logo"/>
            <div v-if="!isCollapse">WoAgent</div>
        </div>
        <el-scrollbar class="model-el-scrollbar">
            <el-menu
                router
                :default-active="activeIndex"
                class="el-menu-vertical"
                @open="handleOpen"
                @close="handleOpen"
                active-text-color="#eda23c"
                :default-openeds="isCollapse?[]:openedMenus"
                :collapse="isCollapse"
            >
                <el-submenu index="/models" @click.native.stop="handlePrevent">
                    <template slot="title">
                        <i v-if="isCollapse" class="el-icon-box"></i>
                        <span class="group-title">模型</span>
                    </template>
                    <el-menu-item index="/models/plaza">
                        <span class="iconfont icon-moxingguangchang"></span>
                        <span class="_label">模型广场</span>
                    </el-menu-item>
                </el-submenu>

                <el-submenu index="/agent" @click.native.stop="handlePrevent">
                    <template slot="title">
                        <i v-if="isCollapse" class="el-icon-box"></i>
                        <span class="group-title">智能体</span>
                    </template>
                    <el-menu-item index="/agent/square">
                        <span class="iconfont icon-moxingguangchang"></span>
                        <span class="_label">智能体广场</span>
                    </el-menu-item>
                </el-submenu>

                <el-submenu index="/develop" @click.native.stop="handlePrevent">
                    <template slot="title">
                        <i v-if="isCollapse" class="el-icon-box"></i>
                        <span class="group-title">开发平台</span>
                    </template>
                    <el-menu-item index="/develop/develop-center">
                        <span class="iconfont icon-moxingguangchang"></span>
                        <span class="_label">开发者中心</span>
                    </el-menu-item>
                    <el-menu-item index="/develop/my-agent">
                        <span class="iconfont icon-moxingguangchang"></span>
                        <span class="_label">我的智能体</span>
                    </el-menu-item>
                </el-submenu>

                <el-submenu index="/playground" @click.native.stop="handlePrevent">
                    <template slot="title">
                        <i v-if="isCollapse" class="el-icon-monitor"></i>
                        <span class="group-title">体验中心</span>
                    </template>
                    <el-menu-item index="/playground/chat" @click="isCollapse=true">
                        <i class="el-icon-chat-dot-round"> </i>
                        <span class="_label">文本对话</span>
                    </el-menu-item>
                </el-submenu>
                <el-submenu index="/account" @click.native.stop="handlePrevent">
                    <template slot="title">
                        <i v-if="isCollapse" class="el-icon-user"></i>
                        <span class="group-title">账户管理</span>
                    </template>
                    <el-menu-item index="/account/authentication">
                        <span class="iconfont icon-shimingrenzheng"></span>
                        <span class="_label">实名认证</span>
                    </el-menu-item>
                    <el-menu-item index="/account/ak">
                        <span class="iconfont icon-miyueguanli"></span>
                        <span class="_label">API 密钥</span>
                    </el-menu-item>
                    <el-menu-item index="/account/expensebill">
                        <span class="iconfont icon-huiyuanchongzhi"></span>
                        <span class="_label">余额充值</span>
                    </el-menu-item>
                    <el-menu-item index="/account/bills">
                        <span class="iconfont icon-zhangdanguanli"></span>
                        <span class="_label">费用账单</span>
                    </el-menu-item>
                    <el-menu-item index="/account/invoice">
                        <span class="iconfont icon-fapiao"></span>
                        <span class="_label">发票开具</span>
                    </el-menu-item>
                </el-submenu>
                <el-menu-item @click="isCollapse=!isCollapse">
                    <i v-if="!isCollapse" class="el-icon-s-fold"
                    ></i>
                    <i v-if="isCollapse" class="el-icon-s-unfold"
                    ></i>
                </el-menu-item>
            </el-menu>

        </el-scrollbar>

    </el-aside>
</template>

<script>
export default {
    data() {
        return {
            activeIndex: '',
            isCollapse: false,
            openedMenus: ["/models", "/playground", "/account", "/agent", "/develop"],
            menuList: [
                {
                    title: "模型",
                    icon: "dashboard",
                    path: "/models",
                    children: [
                        {
                            title: "模型广场",
                            icon: "dashboard",
                            path: "/models/plaza",
                        }
                    ]
                },
                {
                    title: "体验中心",
                    icon: "dashboard",
                    path: "/playground",
                    children: [
                        {
                            title: "文本对话",
                            icon: "dashboard",
                            path: "/playground/chat",
                        }
                    ]
                },
                {
                    title: "账户管理",
                    icon: "dashboard",
                    path: "/account",
                    children: [
                        {
                            title: "实名认证",
                            icon: "dashboard",
                            path: "/account/authentication",
                        },
                        {
                            title: "API密钥",
                            icon: "dashboard",
                            path: "/account/ak",
                        },
                        {
                            title: "余额充值",
                            icon: "dashboard",
                            path: "/account/expensebill",
                        },
                        {
                            title: "费用账单",
                            icon: "dashboard",
                            path: "/account/bills",
                        },
                        {
                            title: "发票开具",
                            icon: "dashboard",
                            path: "/account/invoice",
                        }
                    ]
                },
                {
                    title: "智能体",
                    icon: "dashboard",
                    path: "/agent",
                    children: [
                        {
                            title: "智能体广场",
                            icon: "dashboard",
                            path: "/agent/square",
                        }
                    ]
                },
                {
                    title: "开发平台",
                    icon: "dashboard",
                    path: "/develop",
                    children: [
                        {
                            title: "开发者中心",
                            icon: "dashboard",
                            path: "/develop/develop-center",
                        },
                        {
                            title: "我的智能体",
                            icon: "dashboard",
                            path: "/develop/my-agent",
                        }
                    ]
                },
                {
                    title: "管理后台",
                    icon: "dashboard",
                    path: "/aiadmin/index",
                },
                {
                    title: "个人中心",
                    icon: "user",
                    path: "/user/profile",
                }
            ]
        }
    },
    watch: {
        // 监听路由变化，更新高亮菜单
        '$route': {
            deep: true,
            immediate: true,
            handler(to) {
                // 为了显示高亮
                if (to.path.includes('/account/authentication')) {
                    this.activeIndex = '/account/authentication'
                } else if (to.path.includes('/playground/chat')) {
                    this.isCollapse = true;
                    this.activeIndex = to.path;
                } else {
                    this.activeIndex = to.path;
                }
                console.log('this.activeIndex', this.activeIndex)
                console.log('this.activeIndex', this.activeIndex)
            }
        }
    },
    methods: {
        // 阻止父菜单的点击事件（防止折叠）
        handlePrevent(e) {
            e.preventDefault();
            e.stopPropagation();
        },
        // 监听菜单展开事件（防止意外关闭）
        handleOpen(index) {
            // 永远是展开状态
            this.openedMenus = ["/models", "/playground", "/account"];
        },
    }
}
</script>

<style lang="scss">
@import "@/assets/styles/flex-base";

.model-aside {
    padding: 0;
    margin: 0;
    border-radius: 0;
    max-width: 200px;

    .model-logo {
        height: 56px;
        font-size: 18px;
        font-weight: bold;
        text-align: center;
        background: rgba(230, 162, 60, 1);
        color: #fff;
        gap: 6px;
        cursor: pointer;

        img {
            width: 36px;
            height: 36px;
            border-radius: 100%;
        }
    }

    .model-el-scrollbar {
        width: 100%;
        height: calc(100vh - 56px);
        box-sizing: border-box;

        .group-title {
            color: rgba(0, 0, 0, 0.45);
        }

        ._label {
            color: #313033;
        }

        .iconfont {
            margin-right: 6px;
        }


        .is-active.el-menu-item {
            ._label {
                color: #e6a23c !important;
            }
        }

        .el-submenu__icon-arrow.el-icon-arrow-down {
            display: none !important;
        }
    }
}

span.iconfont {
    padding-right: 10px;
}
</style>
