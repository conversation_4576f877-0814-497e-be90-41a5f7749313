<template>
    <div class="app-container">
        <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-width="68px"
        >
            <el-form-item label="名称" prop="name">
                <el-input
                    v-model="queryParams.name"
                    placeholder="请输入名称"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="分类名" prop="categoriesName">
                <el-input
                    v-model="queryParams.categoriesName"
                    placeholder="请输入分类名"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="简介" prop="description">
                <el-input
                    v-model="queryParams.description"
                    placeholder="请输入简介"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item>
                <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click="handleQuery"
                >搜索
                </el-button
                >
                <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                >重置
                </el-button
                >
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAdd"
                    v-hasPermi="['ai:models:add']"
                >新增
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="el-icon-edit"
                    size="mini"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['ai:models:edit']"
                >修改
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['ai:models:remove']"
                >删除
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['ai:models:export']"
                >导出
                </el-button
                >
            </el-col>
            <right-toolbar
                :showSearch.sync="showSearch"
                @queryTable="getList"
            ></right-toolbar>
        </el-row>

        <el-table
            v-loading="loading"
            :data="modelsList"
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="主键" align="center" prop="id"/>
            <el-table-column label="名称" align="center" prop="name"/>
            <el-table-column
                label="分类"
                align="center"
                prop="categoriesName"
            />
            <el-table-column
                label="封面"
                align="center"
                prop="picture"
                :show-overflow-tooltip="true"
                width="120"
            >
                <template slot-scope="scope">
                    <el-image
                        :src="scope.row.picture"
                        style="height: 50px; width: 50px"
                    ></el-image>
                </template>
            </el-table-column>
            <el-table-column
                label="简介"
                align="center"
                prop="description"
                :show-overflow-tooltip="true"
                width="120"
            />
            <el-table-column
                label="链接"
                align="center"
                prop="url"
                :show-overflow-tooltip="true"
                width="120"
            />
            <el-table-column
                label="二维码"
                align="center"
                prop="qrCode"
                :show-overflow-tooltip="true"
                width="120"
            />
            <el-table-column
                label="推荐指数"
                align="center"
                prop="recommendRate"
            />
            <el-table-column label="浏览数" align="center" prop="viewnum"/>
            <!-- <el-table-column label="收藏数" align="center" prop="favornum" /> -->
            <el-table-column
                label="内容"
                align="center"
                prop="content"
                :show-overflow-tooltip="true"
                width="120"
            />
            <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
            >
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['ai:models:edit']"
                    >修改
                    </el-button
                    >
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['ai:models:remove']"
                    >删除
                    </el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改Ai模型对话框 -->
        <el-dialog
            :title="title"
            :visible.sync="open"
            width="700px"
            append-to-body
            :close-on-click-modal="false"
        >
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="名称" prop="name">
                            <el-input
                                v-model="form.name"
                                placeholder="请输入名称"
                                @blur="checkModelName()"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="分类" prop="categoryId">
                            <el-select
                                v-model="form.categoriesId"
                                placeholder="请选择分类"
                                @change="onSelectChange"
                            >
                                <el-option
                                    v-for="item in categoriesOptions"
                                    :key="item.categoryId"
                                    :label="item.categoryName"
                                    :value="item.categoryId"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12"
                    >
                        <el-form-item label="简介" prop="description">
                            <el-input
                                v-model="form.description"
                                placeholder="请输入简介"
                            />
                        </el-form-item
                        >
                    </el-col>
                    <el-col :span="12"
                    >
                        <el-form-item label="链接" prop="url">
                            <el-input
                                v-model="form.url"
                                placeholder="请输入外部链接"
                            />
                        </el-form-item
                        >
                    </el-col>
                </el-row>
                <el-form-item label="封面" prop="picture">
                    <image-upload v-model="form.picture"/>
                </el-form-item>

                <el-form-item label="二维码" prop="qrCode">
                    <image-upload v-model="form.qrCode"/>
                </el-form-item>
                <el-form-item label="推荐指数" prop="recommendRate">
                    <el-input
                        v-model="form.recommendRate"
                        placeholder="请输入推荐指数"
                    />
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input v-model="form.sort" placeholder="请输入排序"/>
                </el-form-item>
                <el-form-item label="上下文长度" prop="contextWindow">
                    <el-input v-model="form.contextWindow" placeholder="请输入上下文长度"/>
                </el-form-item>
                <el-form-item label="供应商ID" prop="supplierId">
                    <el-input v-model="form.supplierId" placeholder="请输入供应商ID"/>
                </el-form-item>
                <el-form-item label="模型秘钥" prop="apiKey">
                    <el-input v-model="form.apiKey" placeholder="请输入模型秘钥"/>
                </el-form-item>
                <el-form-item label="调用地址" prop="apiUrl">
                    <el-input v-model="form.apiUrl" placeholder="请输入调用地址"/>
                </el-form-item>
                <el-form-item label="调用模型名称" prop="apiName">
                    <el-input v-model="form.apiName" placeholder="请输入调用模型名称"/>
                </el-form-item>
                <el-form-item label="规格" prop="spec">
                    <el-select v-model="form.spec" placeholder="请选择规格">
                        <el-option
                            v-for="dict in dict.type.ai_model_spec_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="parseInt(dict.value)"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="Agent Thought" prop="agentThought">
                    <el-select v-model="form.agentThought" placeholder="请选择Agent Thought">
                        <el-option
                            v-for="dict in dict.type.ai_model_support"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="Function calling" prop="functionCalling">
                    <el-select v-model="form.functionCalling" placeholder="请选择Function calling">
                        <el-option
                            v-for="dict in dict.type.ai_model_support"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="Stream function calling" prop="streamFunctionCalling">
                    <el-select v-model="form.streamFunctionCalling" placeholder="请选择Stream function calling">
                        <el-option
                            v-for="dict in dict.type.ai_model_support"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="Vision 支持" prop="vision">
                    <el-select v-model="form.vision" placeholder="请选择Vision 支持">
                        <el-option
                            v-for="dict in dict.type.ai_model_support"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="流模式返回结果的分隔符" prop="streamSplit">
                    <el-input v-model="form.streamSplit" placeholder="请输入流模式返回结果的分隔符"/>
                </el-form-item>
                <el-form-item label="模型备案ID" prop="filingId">
                    <el-input v-model="form.filingId" placeholder="请输入模型备案ID"/>
                </el-form-item>
                <!-- <el-form-item label="标签" prop="tags">
                    <el-select
                        v-model="form.tags"
                        multiple
                        placeholder="请选择标签"
                    >
                        <el-option
                            v-for="item in tagList"
                            :key="item.tagId"
                            :label="item.tagName"
                            :value="item.tagId"
                        >
                        </el-option>
                    </el-select>
                </el-form-item> -->
                <el-form-item label="内容">
                    <editor v-model="form.content" :min-height="192"/>
                </el-form-item>
                <el-divider content-position="center">模型备案信息</el-divider>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddAiModelFiling">添加
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteAiModelFiling">
                            删除
                        </el-button>
                    </el-col>
                </el-row>
                <el-table :data="aiModelFilingList" :row-class-name="rowAiModelFilingIndex"
                          @selection-change="handleAiModelFilingSelectionChange" ref="aiModelFiling">
                    <el-table-column type="selection" width="50" align="center"/>
                    <el-table-column label="序号" align="center" prop="index" width="50"/>
                    <el-table-column label="模型ID" prop="modelId" width="150">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.modelId" placeholder="请输入模型ID"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="属地" prop="region" width="150">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.region" placeholder="请输入属地"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="备案模型名称" prop="modelName" width="150">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.modelName" placeholder="请输入备案模型名称"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="备案单位" prop="filingUnit" width="150">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.filingUnit" placeholder="请输入备案单位"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="备案号" prop="filingNumber" width="150">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.filingNumber" placeholder="请输入备案号"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="备案时间" prop="filingTime" width="240">
                        <template slot-scope="scope">
                            <el-date-picker clearable v-model="scope.row.filingTime" type="date"
                                            value-format="yyyy-MM-dd" placeholder="请选择备案时间"/>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
            <!-- <el-divider content-position="center">模型标签关联信息</el-divider> -->
            <!-- <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button
                        type="primary"
                        icon="el-icon-plus"
                        size="mini"
                        @click="handleAddAiModelsTags"
                        >添加</el-button
                    >
                </el-col>
                <el-col :span="1.5">
                    <el-button
                        type="danger"
                        icon="el-icon-delete"
                        size="mini"
                        @click="handleDeleteAiModelsTags"
                        >删除</el-button
                    >
                </el-col>
            </el-row>
            <el-table
                :data="aiModelsTagsList"
                :row-class-name="rowAiModelsTagsIndex"
                @selection-change="handleAiModelsTagsSelectionChange"
                ref="aiModelsTags"
            >
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column
                    label="序号"
                    align="center"
                    prop="index"
                    width="50"
                />
                <el-table-column label="模型id" prop="modelsId" width="150" />
                <el-table-column label="标签id" prop="tagsId" width="150">
                    <template slot-scope="scope">
                        <el-input
                            v-model="scope.row.tagsId"
                            placeholder="请输入标签id"
                        />
                    </template>
                </el-table-column>
                <el-table-column label="标签名称" prop="tagsName" width="150">
                    <template slot-scope="scope">
                        <el-input
                            v-model="scope.row.tagsName"
                            placeholder="请输入标签名称"
                        />
                    </template>
                </el-table-column>
            </el-table> -->
            <!-- Tag Selection Dialog -->
            <!-- <el-dialog title="选择标签" :visible.sync="tagDialogVisible" width="30%">
      <el-table :data="tagList" @selection-change="handleTagSelectionChange">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="标签ID" prop="id" width="150"/>
        <el-table-column label="标签名称" prop="name" width="150"/>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="tagDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addSelectedTags">确 定</el-button>
      </div>
    </el-dialog> -->
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    listModels,
    getModels,
    delModels,
    addModels,
    updateModels,
    checkModelNameAPI,
} from "@/api/ai/models";
import {listCategories} from "@/api/ai/categories";
import {listTagsByModelsId} from "@/api/ai/modelsTags";
import {listTags} from "@/api/ai/tags";

export default {
    name: "Models",
    dicts: ['ai_model_spec_type', 'ai_model_support'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 子表选中数据
            checkedAiModelsTags: [],
            // 子表选中数据
            checkedAiModelFiling: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // Ai模型表格数据
            modelsList: [],
            // 模型备案表格数据
            aiModelFilingList: [],
            // 控制标签选择对话框的显示
            tagDialogVisible: false,
            // 存放现有标签数据
            tagList: [],
            // 模型标签关联表格数据
            aiModelsTagsList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            categoriesOptions: [],
            queryParamsByCategories: {
                categoriesId: "",
                categoriesName: "",
            },
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                name: null,
                categoriesId: null,
                categoriesName: null,
                picture: null,
                description: null,
                status: null,
            },
            // tags查询参数
            queryTagsParams: {
                modelsId: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {},
        };
    },
    created() {
        this.getList();
        this.getCategoriesList(); // 组件挂载后获取下拉选项数据
    },
    methods: {
        /** 查询Ai模型标签列表 */
        getCategoriesList() {
            this.loading = true;
            listCategories({pageNum: 1, pageSize: 30}).then((response) => {
                this.categoriesOptions = response.rows;
                this.loading = false;
            });
        },
        /** 查询Ai模型列表 */
        getList() {
            this.loading = true;
            listModels(this.queryParams).then((response) => {
                this.modelsList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                name: null,
                categoriesId: null,
                categoriesName: null,
                picture: null,
                description: null,
                createTime: null,
                updateTime: null,
                status: null,
            };
            this.aiModelsTagsList = [];
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加Ai模型";
            this.tagList = listTags().then((response) => {
                response.rows;
            });
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids;
            getModels(id).then((response) => {
                this.form = response.data;
                this.open = true;
                this.title = "修改Ai模型";
                console.log(response.data.id);
                this.aiModelsTagsList = listTagsByModelsId(
                    response.data.id
                ).data;
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    if (this.form.id != null) {
                        let params = {
                            ...this.form,
                            tags: this.aiModelsTagsList
                        }
                        updateModels(params).then((response) => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addModels(this.form).then((response) => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal
                .confirm('是否确认删除Ai模型编号为"' + ids + '"的数据项？')
                .then(function () {
                    return delModels(ids);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess("删除成功");
                })
                .catch(() => {
                });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download(
                "ai/models/export",
                {
                    ...this.queryParams,
                },
                `models_${new Date().getTime()}.xlsx`
            );
        },
        onSelectChange(value) {
            // 当选择变化时，更新 form 中的 categoryId 和 categoryName
            const selectedCategory = this.categoriesOptions.find(
                (item) => item.categoryId === value
            );
            if (selectedCategory) {
                this.form.categoriesId = selectedCategory.categoryId;
                this.form.categoriesName = selectedCategory.categoryName;
            }
        },
        /** 模型标签关联序号 */
        rowAiModelsTagsIndex({row, rowIndex}) {
            row.index = rowIndex + 1;
        },
        /** 模型标签关联添加按钮操作 */
        handleAddAiModelsTags() {
            let obj = {};
            obj.tagsId = "";
            obj.tagsName = "";
            obj.modelsId = "";
            this.aiModelsTagsList.push(obj);
        },
        /** 模型标签关联删除按钮操作 */
        handleDeleteAiModelsTags() {
            if (this.checkedAiModelsTags.length == 0) {
                this.$modal.msgError("请先选择要删除的模型标签关联数据");
            } else {
                const aiModelsTagsList = this.aiModelsTagsList;
                const checkedAiModelsTags = this.checkedAiModelsTags;
                this.aiModelsTagsList = aiModelsTagsList.filter(function (
                    item
                ) {
                    return checkedAiModelsTags.indexOf(item.index) == -1;
                });
            }
        },
        /** 复选框选中数据 */
        handleAiModelsTagsSelectionChange(selection) {
            this.checkedAiModelsTags = selection.map((item) => item.index);
        },
        // 应用名称查重
        checkModelName() {
            checkModelNameAPI(this.form.name).then((response) => {
                if (response.code == 200) {
                    this.$modal.msgSuccess("应用名称可用");
                } else {
                    this.$modal.msgError("应用名称重复");
                }
            });
        },
        /** 模型备案添加按钮操作 */
        handleAddAiModelFiling() {
            let obj = {};
            obj.modelId = "";
            obj.region = "";
            obj.modelName = "";
            obj.filingUnit = "";
            obj.filingNumber = "";
            obj.filingTime = "";
            this.aiModelFilingList.push(obj);
        },
        /** 模型备案删除按钮操作 */
        handleDeleteAiModelFiling() {
            if (this.checkedAiModelFiling.length == 0) {
                this.$modal.msgError("请先选择要删除的模型备案数据");
            } else {
                const aiModelFilingList = this.aiModelFilingList;
                const checkedAiModelFiling = this.checkedAiModelFiling;
                this.aiModelFilingList = aiModelFilingList.filter(function (item) {
                    return checkedAiModelFiling.indexOf(item.index) == -1
                });
            }
        },
        /** 复选框选中数据 */
        handleAiModelFilingSelectionChange(selection) {
            this.checkedAiModelFiling = selection.map(item => item.index)
        },
    },
};
</script>
