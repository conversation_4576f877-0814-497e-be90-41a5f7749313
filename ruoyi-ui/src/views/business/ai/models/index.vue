<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px">
            <el-form-item label="名称" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入名称" clearable
                          @keyup.enter.native="handleQuery"/>
            </el-form-item>
            <el-form-item label="分类" prop="categoriesId">
                <el-select v-model="queryParams.categoriesId" placeholder="请选择分类">
                    <el-option label="全部" value=""></el-option>
                    <el-option v-for="item in categoriesOptions" :key="item.categoryId" :label="item.categoryName"
                               :value="item.categoryId"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                           v-hasPermi="['ai:models:add']">新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
                           v-hasPermi="['ai:models:edit']">修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
                           @click="handleDelete" v-hasPermi="['ai:models:remove']">删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                           v-hasPermi="['ai:models:export']">导出
                </el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="modelsList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="主键" align="center" prop="id" width="60"/>
            <el-table-column label="名称" align="center" prop="name"/>
            <el-table-column label="分类" align="center" prop="categoriesName"/>
            <el-table-column label="上下文长度" align="center" prop="contextWindow"/>
            <el-table-column label="最大token上限" align="center" prop="maxToken"/>
            <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                               v-hasPermi="['ai:models:edit']">修改
                    </el-button>
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                               v-hasPermi="['ai:models:remove']">删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="getList"/>

        <!-- 添加或修改Ai模型对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body :close-on-click-modal="false" destroy-on-close>
            <el-tabs type="border-card" v-model="tabsActive" @tab-click="tabClick">
                <el-tab-pane label="基本信息" name="1">
                    <el-form class="form-dialog" ref="form" :model="form" :rules="rules" label-width="200px">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="名称" prop="name">
                                    <el-input v-model="form.name" placeholder="请输入名称" :disabled="form.id"
                                              @blur="checkModelName()"/>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="分类" prop="categoryId">
                                    <el-select v-model="form.categoriesId" placeholder="请选择分类"
                                               @change="onSelectChange" :disabled="form.id">
                                        <el-option v-for="item in categoriesOptions" :key="item.categoryId"
                                                   :label="item.categoryName" :value="item.categoryId"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="供应商" prop="supplierId">
                                    <el-select v-model="form.supplierId" placeholder="请选择供应商">
                                        <el-option v-for="item in supplierList" :key="item.supplierId"
                                                   :label="item.supplierName" :value="item.supplierId"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="调用模型名称" prop="apiName">
                                    <el-input v-model="form.apiName" placeholder="请输入调用模型名称"/>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="调用地址" prop="apiUrl">
                                    <el-input v-model="form.apiUrl" placeholder="请输入调用地址"/>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="模型秘钥" prop="apiKey">
                                    <el-input v-model="form.apiKey" placeholder="请输入模型秘钥" :type="keyType">
                                        <template #append>
                                            <el-button @click="changeType">
                                                <i class="el-icon-key"></i>
                                            </el-button>
                                        </template>
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12" v-if="form.categoriesId !== 5">
                                <el-form-item label="上下文长度" prop="contextWindow">
                                    <el-input v-model="form.contextWindow" placeholder="请输入上下文长度"/>
                                </el-form-item>
                            </el-col>
                            <template v-if="form.categoriesId == 1">
                                <el-col :span="12">
                                    <el-form-item label="最大token上限" prop="maxToken">
                                        <el-input v-model="form.maxToken" placeholder="请输入最大token上限"/>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="Agent Thought" prop="agentThought">
                                        <el-select v-model="form.agentThought" placeholder="请选择Agent Thought">
                                            <el-option v-for="dict in dict.type.ai_model_support" :key="dict.value"
                                                       :label="dict.label" :value="dict.value"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="Function calling" prop="functionCalling">
                                        <el-select v-model="form.functionCalling" placeholder="请选择Function calling">
                                            <el-option v-for="dict in dict.type.ai_model_support" :key="dict.value"
                                                       :label="dict.label" :value="dict.value"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="Stream function calling" prop="streamFunctionCalling">
                                        <el-select v-model="form.streamFunctionCalling"
                                                   placeholder="请选择Stream function calling">
                                            <el-option v-for="dict in dict.type.ai_model_support" :key="dict.value"
                                                       :label="dict.label" :value="dict.value"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="Vision 支持" prop="vision">
                                        <el-select v-model="form.vision" placeholder="请选择Vision 支持">
                                            <el-option v-for="dict in dict.type.ai_model_support" :key="dict.value"
                                                       :label="dict.label" :value="dict.value"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="流模式返回结果的分隔符" prop="streamSplit">
                                        <el-input v-model="form.streamSplit"
                                                  placeholder="请输入流模式返回结果的分隔符"/>
                                    </el-form-item>
                                </el-col>
                            </template>
                            <template v-if="form.categoriesId === 7">
                                <el-col :span="24">
                                    <el-form-item label="可用声音(用英文逗号分隔)" prop="apiName">
                                        <el-input v-model="form.voice" placeholder="请输入可用声音"/>
                                    </el-form-item>
                                </el-col>
                            </template>
                            <el-col :span="24">
                                <el-form-item label="简介" prop="description">
                                    <el-input v-model="form.description" placeholder="请输入简介" type="textarea"/>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="计费策略" name="2">
                    <el-form class="form-dialog" ref="priceForm" :model="priceForm" :rules="rulesPrice"
                             label-width="180px">
                        <el-row>
                            <el-col :span="12" v-if="form.categoriesId == 8">
                                <el-form-item label="输入单价" prop="inputUnitPrice">
                                    <el-input v-model="priceForm.inputUnitPrice" placeholder="请输入输入单价"/>
                                </el-form-item>
                            </el-col>

                            <el-col :span="12"
                                    v-else-if="form.categoriesId == 2 || form.categoriesId == 4 ||form.categoriesId === 7">
                                <el-form-item label="输出单价" prop="outputUnitPrice">
                                    <el-input v-model="priceForm.outputUnitPrice" placeholder="请输入输出单价"/>
                                </el-form-item>
                            </el-col>
                            <template v-else>
                                <el-col :span="12">
                                    <el-form-item label="输入单价" prop="inputUnitPrice">
                                        <el-input v-model="priceForm.inputUnitPrice" placeholder="请输入输入单价"/>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="12">
                                    <el-form-item label="输出单价" prop="outputUnitPrice">
                                        <el-input v-model="priceForm.outputUnitPrice" placeholder="请输入输出单价"/>
                                    </el-form-item>
                                </el-col>
                            </template>
                            <el-col :span="12">
                                <el-form-item label="计费单位" prop="billingUnit">
                                    <el-select v-model="priceForm.billingUnit" placeholder="请选择计费单位">
                                        <el-option v-for="dict in dict.type.bill_unit" :key="dict.value"
                                                   :label="dict.label" :value="dict.value"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="详细信息" name="3">
                    <el-form class="form-dialog" ref="filingForm" :model="filingForm" :rules="rulesFiling"
                             label-width="180px">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="文档链接" prop="url">
                                    <el-input v-model="form.url" placeholder="请输入外部链接"/>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="标签">
                                    <el-select multiple v-model="aiModelsTags" placeholder="请选择标签">
                                        <el-option v-for="item in tagList" :key="item.tagId" :label="item.tagName"
                                                   :value="item.tagId"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="推荐指数" prop="recommendRate">
                                    <el-input v-model="form.recommendRate" placeholder="请输入推荐指数"/>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="排序" prop="sort">
                                    <el-input v-model="form.sort" placeholder="请输入排序"/>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="规格" prop="spec">
                                    <el-input v-model.number="spec2" placeholder="请输入规格" @change="changeSpec()">
                                        <template #append>
                                            <el-select v-model="spec" style="width: 60px" @change="changeSpec()">
                                                <el-option v-for="(n, nIndex) in specList" :label="n.label"
                                                           :value="n.value" :key="nIndex"></el-option>
                                            </el-select>
                                        </template>
                                    </el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-divider>备案信息</el-divider>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="属地" prop="region">
                                    <el-input v-model="filingForm.region" placeholder="请输入属地"/>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="备案模型名称" prop="modelName">
                                    <el-input v-model="filingForm.modelName" placeholder="请输入备案模型名称"/>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="备案单位" prop="filingUnit">
                                    <el-input v-model="filingForm.filingUnit" placeholder="请输入备案单位"/>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="备案号" prop="filingNumber">
                                    <el-input v-model="filingForm.filingNumber" placeholder="请输入备案号"/>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="备案时间" prop="filingTime">
                                    <el-date-picker v-model="filingForm.filingTime" type="date"
                                                    placeholder="选择日期"></el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-tab-pane>
            </el-tabs>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {listModels, getModels, delModels, addModels, updateModels, checkModelNameAPI} from '@/api/ai/models';
import {listCategories} from '@/api/ai/categories';
import {updatePrice, addPrice} from '@/api/ai/price';
import {addFiling, updateFiling} from '@/api/ai/filing';
import {listTags} from '@/api/ai/tags';
import {listSupplier} from '@/api/ai/supplier';

export default {
    name: 'Models',
    dicts: ['ai_model_support', 'bill_unit'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 子表选中数据
            checkedAiModelsTags: [],
            // 子表选中数据
            checkedAiModelFiling: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // Ai模型表格数据
            modelsList: [],
            // 模型备案表格数据
            aiModelFilingList: [],
            // 控制标签选择对话框的显示
            tagDialogVisible: false,
            // 存放现有标签数据
            tagList: [],
            aiModelsTags: [],
            supplierList: [],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            categoriesOptions: [],
            queryParamsByCategories: {
                categoriesId: ''
            },
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                name: null,
                categoriesId: '',
                picture: null,
                status: null
            },
            // tags查询参数
            queryTagsParams: {
                modelsId: null
            },
            // 表单参数
            form: {
                filingId: null
            },
            // 表单校验
            rules: {
                name: [{required: true, message: '输入名称', trigger: 'blur'}],
                apiUrl: [{required: true, message: '调用地址', trigger: 'blur'}],
                apiName: [{required: true, message: '调用模型名称', trigger: 'blur'}],
                categoriesId: [{required: true, message: '请选择分类', trigger: 'change'}]
            },

            priceForm: {
                inputUnitPrice: '', // 输入单价
                outputUnitPrice: '', // 输出单价
                billingUnit: '' // 计费单位
            }, // 价格信息
            rulesPrice: {},

            filingForm: {
                region: '', // 属地
                modelName: '', // 备案模型名称
                filingUnit: '', // 备案单位
                filingNumber: '', // 备案号
                filingTime: '' // 备案时间
            }, // 备案信息
            rulesFiling: {},
            tabsActive: '1',
            priceInit: true, // 要执行表达重置
            filingInit: false, // 要执行表达重置

            keyType: 'password',
            specList: [
                {label: 'K', value: 'K', num: 1000},
                {label: 'M', value: 'M', num: 1000000},
                {label: 'B', value: 'B', num: 1000000000}
            ],
            spec: 'B',
            spec2: ''
        };
    },
    created() {
        listTags().then((response) => {
            this.tagList = response.rows;
        });

        listSupplier().then((response) => {
            this.supplierList = response.rows;
        });
        this.getList();
        this.getCategoriesList(); // 组件挂载后获取下拉选项数据
    },
    watch: {
        'form.categoriesId': {
            deep: true,
            immediate: true,
            handler(newVal) {
                console.log('form.categoriesId', newVal);
                if (newVal === 4 || newVal === 7 || newVal === 8 || newVal === 2) {
                    this.priceForm.billingUnit = '';
                    if (newVal === 4) {
                        this.priceForm.billingUnit = 'Video';
                    } else if (newVal === 7 || newVal === 8) {
                        this.priceForm.billingUnit = 'Audio';
                    } else if (newVal === 2) {
                        this.priceForm.billingUnit = 'Image';
                    }
                } else {
                    this.priceForm.billingUnit = 'Tokens';
                }
            }
        }
    },
    methods: {
        /** 查询Ai模型标签列表 */
        getCategoriesList() {
            this.loading = true;
            listCategories({pageNum: 1, pageSize: 30}).then((response) => {
                this.categoriesOptions = response.rows;
                this.loading = false;
            });
        },
        /** 查询Ai模型列表 */
        getList() {
            this.loading = true;
            listModels(this.queryParams).then((response) => {
                this.modelsList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                name: null,
                categoriesId: '',
                streamSplit: '/n/n',
                picture: null,
                createTime: null,
                updateTime: null,
                status: null
            };
            this.aiModelsTags = [];
            this.resetForm('form');
            this.priceForm = {
                inputUnitPrice: '', // 输入单价
                outputUnitPrice: '', // 输出单价
                billingUnit: '' // 计费单位
            };
            this.filingForm = {
                region: '', // 属地
                modelName: '', // 备案模型名称
                filingUnit: '', // 备案单位
                filingNumber: '', // 备案号
                filingTime: '' // 备案时间
            };
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = '添加Ai模型';

            this.tabsActive = '1';
            this.priceInit = true;
            this.filingInit = true;
            this.resetForm('form');

            this.priceForm = {
                inputUnitPrice: '', // 输入单价
                outputUnitPrice: '', // 输出单价
                billingUnit: '' // 计费单位
            };
            this.filingForm = {
                region: '', // 属地
                modelName: '', // 备案模型名称
                filingUnit: '', // 备案单位
                filingNumber: '', // 备案号
                filingTime: '' // 备案时间
            };
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids;
            getModels(id).then((response) => {
                this.form = response.data;
                this.open = true;
                this.title = '修改Ai模型';
                console.log('this.form', this.form);

                this.tabsActive = '1';
                this.priceInit = true;
                this.filingInit = true;
                this.resetForm('form');
                this.priceForm = {
                    inputUnitPrice: '', // 输入单价
                    outputUnitPrice: '', // 输出单价
                    billingUnit: '' // 计费单位
                };
                this.filingForm = {
                    region: '', // 属地
                    modelName: '', // 备案模型名称
                    filingUnit: '', // 备案单位
                    filingNumber: '', // 备案号
                    filingTime: '' // 备案时间
                };

                if (this.form.aiModelFiling) {
                    Object.keys(this.filingForm).forEach((n) => {
                        this.filingForm[n] = this.form.aiModelFiling[n];
                    });
                }

                if (this.form.aiModelPrice) {
                    Object.keys(this.priceForm).forEach((n) => {
                        this.priceForm[n] = this.form.aiModelPrice[n];
                    });
                }

                if (this.form.tags) {
                    this.aiModelsTags = this.form.tags.map((n) => n.tagId);
                }

                if (this.form.spec) {
                    let spec = this.formatNumber(this.form.spec);
                    this.spec2 = spec.value;
                    this.spec = spec.suffix;
                }
            });
        },
        /** 提交按钮 */
        submitForm() {
            console.log('aiModelsTags', this.aiModelsTags);
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    let tagList = [];
                    this.aiModelsTags.forEach((n) => {
                        tagList.push({
                            tagId: n
                        });
                    });

                    // 组装参数
                    let params = {
                        ...this.form,
                        tags: tagList,
                        aiModelPrice: this.priceForm,
                        aiModelFiling: this.aiModelFiling
                    };

                    if (this.form.id != null) {
                        updateModels(params).then((response) => {
                            this.$modal.msgSuccess('修改成功');
                            this.saveFiling();
                            this.savePrice();
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addModels(params).then((response) => {
                            this.$modal.msgSuccess('新增成功');
                            this.saveFiling(response.data);
                            this.savePrice(response.data);
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        saveFiling(id) {
            // 保存备案信息
            let filingId = this.form.aiModelFiling && this.form.aiModelFiling.filingId;
            // 组装参数
            let params = {
                ...this.filingForm,
                filingId: filingId,
                modelId: this.form.id || id
            };

            if (filingId) {
                updateFiling(params).then((response) => {
                });
            } else {
                addFiling(params).then((response) => {
                });
            }
        },
        savePrice(id) {
            // 保存备案信息

            let priceId = this.form.aiModelPrice && this.form.aiModelPrice.priceId;

            // 组装参数
            let params = {
                ...this.priceForm,
                priceId: priceId,
                modelId: this.form.id || id
            };

            if (priceId) {
                updatePrice(params).then((response) => {
                });
            } else {
                addPrice(params).then((response) => {
                });
            }
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal
                .confirm('是否确认删除Ai模型编号为"' + ids + '"的数据项？')
                .then(function () {
                    return delModels(ids);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess('删除成功');
                })
                .catch(() => {
                });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download(
                'ai/models/export',
                {
                    ...this.queryParams
                },
                `models_${new Date().getTime()}.xlsx`
            );
        },
        onSelectChange(value) {
            // 当选择变化时，更新 form 中的 categoryId 和 categoryName
            const selectedCategory = this.categoriesOptions.find((item) => item.categoryId === value);
            if (selectedCategory) {
                this.form.categoriesId = selectedCategory.categoryId;
                this.form.categoriesName = selectedCategory.categoryName;
            }
        },
        // 应用名称查重
        checkModelName() {
            checkModelNameAPI(this.form.name).then((response) => {
                if (response.data) {
                    this.$modal.msgSuccess('应用名称可用');
                } else {
                    this.form.name = ''
                    this.$modal.msgError('应用名称重复');
                }
            });
        },
        /** 模型备案添加按钮操作 */
        handleAddAiModelFiling() {
            let obj = {};
            obj.modelId = '';
            obj.region = '';
            obj.modelName = '';
            obj.filingUnit = '';
            obj.filingNumber = '';
            obj.filingTime = '';
            this.aiModelFilingList.push(obj);
        },
        /** 模型备案删除按钮操作 */
        handleDeleteAiModelFiling() {
            if (this.checkedAiModelFiling.length == 0) {
                this.$modal.msgError('请先选择要删除的模型备案数据');
            } else {
                const aiModelFilingList = this.aiModelFilingList;
                const checkedAiModelFiling = this.checkedAiModelFiling;
                this.aiModelFilingList = aiModelFilingList.filter(function (item) {
                    return checkedAiModelFiling.indexOf(item.index) == -1;
                });
            }
        },
        /** 复选框选中数据 */
        handleAiModelFilingSelectionChange(selection) {
            this.checkedAiModelFiling = selection.map((item) => item.index);
        },
        tabClick() {
            if (!this.form.id) {
                if (this.tabsActive === '2' && this.priceInit) {
                    this.priceInit = false;
                    this.$refs.priceForm.clearValidate();
                } else if (this.tabsActive === '3' && this.filingInit) {
                    this.filingInit = false;
                    this.$refs.filingForm.clearValidate();
                }
            }
        },
        changeType() {
            this.keyType = this.keyType === 'password' ? 'text' : 'password';
        },
        changeSpec() {
            if (this.spec2) {
                console.log('this.spec', this.spec, this.specList);
                let item = this.specList.find((n) => n.value === this.spec);
                console.log('item', item);
                this.form.spec = this.spec2 * item.num;
            }
        },
        formatNumber(num) {
            // 处理规格回显的
            if (num === 0) return '0';

            const absNum = Math.abs(num);
            let value, suffix;

            if (absNum >= 1000000000) {
                // 处理 B (十亿)
                value = Math.round(num / 100000000) / 10; // 四舍五入到 0.1B
                suffix = 'B';
            } else if (absNum >= 1000000) {
                // 处理 M (百万)
                value = Math.round(num / 100000) / 10; // 四舍五入到 0.1M
                suffix = 'M';
            } else if (absNum >= 1000) {
                // 处理 K (千)
                value = Math.round(num / 100) / 10; // 四舍五入到 0.1K
                suffix = 'K';
            } else {
                return num.toString(); // 小于 1000 直接返回
            }

            return {
                value: value,
                suffix: suffix
            };
        }
    }
};
</script>
<style lang="scss">
.form-dialog {
    .el-select,
    .el-date-editor.el-input,
    .el-date-editor.el-input__inner {
        width: 100%;
    }
}
</style>
