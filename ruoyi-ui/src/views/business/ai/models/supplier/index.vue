<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px">
            <el-form-item label="供应商名称" prop="supplierName">
                <el-input
                    v-model="queryParams.supplierName"
                    placeholder="请输入供应商名称"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="供应商图标" prop="supplierIcon">
                <el-input
                    v-model="queryParams.supplierIcon"
                    placeholder="请输入供应商图标"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="联系邮箱" prop="contactEmail">
                <el-input
                    v-model="queryParams.contactEmail"
                    placeholder="请输入联系邮箱"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="联系电话" prop="contactPhone">
                <el-input
                    v-model="queryParams.contactPhone"
                    placeholder="请输入联系电话"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAdd"
                    v-hasPermi="['ai:supplier:add']"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="el-icon-edit"
                    size="mini"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['ai:supplier:edit']"
                >修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['ai:supplier:remove']"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['ai:supplier:export']"
                >导出
                </el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="supplierList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="供应商ID" align="center" prop="supplierId"/>
            <el-table-column label="供应商名称" align="center" prop="supplierName"/>
            <el-table-column label="供应商图标" align="center" prop="supplierIcon">
                <template #default="scope">
                    <el-image :src="scope.row.supplierIcon" style="width: 30px; height: 30px; vertical-align: middle" v-if="scope.row.supplierIcon"></el-image>
                </template>
            </el-table-column>
            <el-table-column label="供应商简介" align="center" prop="description"/>
            <el-table-column label="联系邮箱" align="center" prop="contactEmail"/>
            <el-table-column label="联系电话" align="center" prop="contactPhone"/>
            <el-table-column label="状态" align="center" prop="status"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['ai:supplier:edit']"
                    >修改
                    </el-button>
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['ai:supplier:remove']"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改供应商对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="供应商名称" prop="supplierName">
                    <el-input v-model="form.supplierName" placeholder="请输入供应商名称"/>
                </el-form-item>
                <el-form-item label="供应商图标" prop="supplierIcon">
                    <!--          <el-input v-model="form.supplierIcon" placeholder="请输入供应商图标" />-->
                    <image-upload v-model="form.supplierIcon" :limit="1"/>
                </el-form-item>
                <el-form-item label="供应商简介" prop="description">
                    <el-input v-model="form.description" type="textarea" placeholder="请输入内容"/>
                </el-form-item>
                <el-form-item label="联系邮箱" prop="contactEmail">
                    <el-input v-model="form.contactEmail" placeholder="请输入联系邮箱"/>
                </el-form-item>
                <el-form-item label="联系电话" prop="contactPhone">
                    <el-input v-model="form.contactPhone" placeholder="请输入联系电话"/>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {listSupplier, getSupplier, delSupplier, addSupplier, updateSupplier} from "@/api/ai/supplier";

export default {
    name: "Supplier",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 供应商表格数据
            supplierList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                supplierName: null,
                supplierIcon: null,
                description: null,
                contactEmail: null,
                contactPhone: null,
                status: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {}
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询供应商列表 */
        getList() {
            this.loading = true;
            listSupplier(this.queryParams).then(response => {
                this.supplierList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                supplierId: null,
                supplierName: null,
                supplierIcon: null,
                description: null,
                contactEmail: null,
                contactPhone: null,
                status: null,
                createTime: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.supplierId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加供应商";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const supplierId = row.supplierId || this.ids
            getSupplier(supplierId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改供应商";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.supplierId != null) {
                        updateSupplier(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addSupplier(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const supplierIds = row.supplierId || this.ids;
            this.$modal.confirm('是否确认删除供应商编号为"' + supplierIds + '"的数据项？').then(function () {
                return delSupplier(supplierIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('ai/supplier/export', {
                ...this.queryParams
            }, `supplier_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>
