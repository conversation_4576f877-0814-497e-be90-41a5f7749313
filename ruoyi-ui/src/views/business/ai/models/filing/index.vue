<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模型ID" prop="modelId">
        <el-input
          v-model="queryParams.modelId"
          placeholder="请输入模型ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="属地" prop="region">
        <el-input
          v-model="queryParams.region"
          placeholder="请输入属地"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="备案模型名称" prop="modelName">
        <el-input
          v-model="queryParams.modelName"
          placeholder="请输入备案模型名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="备案单位" prop="filingUnit">
        <el-input
          v-model="queryParams.filingUnit"
          placeholder="请输入备案单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="备案号" prop="filingNumber">
        <el-input
          v-model="queryParams.filingNumber"
          placeholder="请输入备案号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="备案时间" prop="filingTime">
        <el-date-picker clearable
          v-model="queryParams.filingTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择备案时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['ai:filing:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['ai:filing:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['ai:filing:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ai:filing:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="filingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="备案ID" align="center" prop="filingId" />
      <el-table-column label="模型ID" align="center" prop="modelId" />
      <el-table-column label="属地" align="center" prop="region" />
      <el-table-column label="备案模型名称" align="center" prop="modelName" />
      <el-table-column label="备案单位" align="center" prop="filingUnit" />
      <el-table-column label="备案号" align="center" prop="filingNumber" />
      <el-table-column label="备案时间" align="center" prop="filingTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.filingTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['ai:filing:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['ai:filing:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改模型备案对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="模型ID" prop="modelId">
          <el-input v-model="form.modelId" placeholder="请输入模型ID" />
        </el-form-item>
        <el-form-item label="属地" prop="region">
          <el-input v-model="form.region" placeholder="请输入属地" />
        </el-form-item>
        <el-form-item label="备案模型名称" prop="modelName">
          <el-input v-model="form.modelName" placeholder="请输入备案模型名称" />
        </el-form-item>
        <el-form-item label="备案单位" prop="filingUnit">
          <el-input v-model="form.filingUnit" placeholder="请输入备案单位" />
        </el-form-item>
        <el-form-item label="备案号" prop="filingNumber">
          <el-input v-model="form.filingNumber" placeholder="请输入备案号" />
        </el-form-item>
        <el-form-item label="备案时间" prop="filingTime">
          <el-date-picker clearable
            v-model="form.filingTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择备案时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFiling, getFiling, delFiling, addFiling, updateFiling } from "@/api/ai/filing";

export default {
  name: "Filing",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 模型备案表格数据
      filingList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        modelId: null,
        region: null,
        modelName: null,
        filingUnit: null,
        filingNumber: null,
        filingTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询模型备案列表 */
    getList() {
      this.loading = true;
      listFiling(this.queryParams).then(response => {
        this.filingList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        filingId: null,
        modelId: null,
        region: null,
        modelName: null,
        filingUnit: null,
        filingNumber: null,
        filingTime: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.filingId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加模型备案";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const filingId = row.filingId || this.ids
      getFiling(filingId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改模型备案";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.filingId != null) {
            updateFiling(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFiling(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const filingIds = row.filingId || this.ids;
      this.$modal.confirm('是否确认删除模型备案编号为"' + filingIds + '"的数据项？').then(function() {
        return delFiling(filingIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('ai/filing/export', {
        ...this.queryParams
      }, `filing_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
