<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px">
            <el-form-item label="用户名称" prop="username">
                <el-input
                    v-model="queryParams.username"
                    placeholder="请输入用户名称"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="起始时间" prop="startDate">
                <el-date-picker clearable
                                v-model="queryParams.startDate"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="请选择起始时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间" prop="endDate">
                <el-date-picker clearable
                                v-model="queryParams.endDate"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="请选择结束时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAdd"
                    v-hasPermi="['product:token:add']"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="el-icon-edit"
                    size="mini"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['product:token:edit']"
                >修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['product:token:remove']"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['product:token:export']"
                >导出
                </el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="tokenList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="用户token主键" align="center" prop="tokenId"/>
            <el-table-column label="用户名称" align="center" prop="username"/>
            <el-table-column label="类型" align="center" prop="type">
                <template #default="scope">
                    {{ statusName(scope.row.type, 1) }}
                </template>
            </el-table-column>
            <el-table-column label="可用输入token数量" align="center" prop="inputTokenQuota"/>
            <el-table-column label="可用输出token数量" align="center" prop="outputTokenQuota"/>
            <el-table-column label="起始时间" align="center" prop="startDate" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="结束时间" align="center" prop="endDate" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center" prop="status">
                <template #default="scope">
                    {{ statusName(scope.row.status, 2) }}
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['product:token:edit']"
                    >修改
                    </el-button>
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['product:token:remove']"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改用户token数量对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="540px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="140px">
                <el-form-item label="用户名称" prop="userId">
                    <!--                    <el-input v-model="form.username" placeholder="请输入用户名称"/>-->
                    <el-select
                        style="width: 100%"
                        v-model="form.userId" filterable remote
                        reserve-keyword
                        placeholder="请输入关键词"
                        :remote-method="remoteMethod"
                        :loading="loading">
                        <el-option
                            v-for="item in userList"
                            :key="item.userId"
                            :label="item.userName"
                            :value="item.userId">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="可用输入token数量" prop="inputTokenQuota">
                    <el-input v-model="form.inputTokenQuota" placeholder="请输入可用输入token数量"/>
                </el-form-item>
                <el-form-item label="可用输出token数量" prop="outputTokenQuota">
                    <el-input v-model="form.outputTokenQuota" placeholder="请输入可用输出token数量"/>
                </el-form-item>
                <el-form-item label="起始时间" prop="startDate">
                    <el-date-picker clearable
                                    v-model="form.startDate"
                                    type="datetime"
                                    format="yyyy-MM-dd HH:mm:ss"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    placeholder="请选择起始时间">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="结束时间" prop="endDate">
                    <el-date-picker clearable
                                    v-model="form.endDate"
                                    type="datetime"
                                    format="yyyy-MM-dd HH:mm:ss"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    placeholder="请选择结束时间">
                    </el-date-picker>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {listToken, getToken, delToken, addToken, updateToken} from "@/api/product/userToken";
import {getUser, listUser} from "@/api/system/user";

export default {
    name: "Token",
    dicts: ['product_type', 'user_account_status'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 用户token数量表格数据
            tokenList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                userId: null,
                username: null,
                type: null,
                inputTokenQuota: null,
                outputTokenQuota: null,
                startDate: null,
                endDate: null,
                status: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                userId: [
                    {required: true, message: "用户ID不能为空", trigger: "blur"}
                ],
            },
            userList: []
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询用户token数量列表 */
        getList() {
            this.loading = true;
            listToken(this.queryParams).then(response => {
                this.tokenList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                tokenId: null,
                userId: null,
                username: null,
                type: null,
                inputTokenQuota: null,
                outputTokenQuota: null,
                startDate: null,
                endDate: null,
                status: null,
                createTime: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.tokenId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加用户token数量";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const tokenId = row.tokenId || this.ids
            getToken(tokenId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改用户token数量";

                // 正确处理远程搜索回显的问题
                getUser(this.form.userId).then(response => {
                    this.userList = [response.data]
                })
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.tokenId != null) {
                        updateToken(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addToken(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const tokenIds = row.tokenId || this.ids;
            this.$modal.confirm('是否确认删除用户token数量编号为"' + tokenIds + '"的数据项？').then(function () {
                return delToken(tokenIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('product/token/export', {
                ...this.queryParams
            }, `token_${new Date().getTime()}.xlsx`)
        },
        statusName(value, type) {
            if (type === 1) {
                let item = this.dict.type.product_type.find(n => n.value == value);
                if (item) {
                    return item.label;
                } else {
                    return '';
                }
            } else {
                if (type === 1) {
                    let item = this.dict.type.user_account_status.find(n => n.value == value);
                    if (item) {
                        return item.label;
                    } else {
                        return '';
                    }
                }
            }
        },
        remoteMethod(query) {
            if (query !== '') {
                this.loading = true;
                let params = {
                    pageNum: 1,
                    pageSize: 100,
                    userName: query,
                }
                listUser(params).then(response => {
                    console.log('用户列表', response)
                    this.loading = false;
                    this.userList = response.rows;
                })
            } else {
                this.userList = [];
            }
        },
    }
};
</script>
