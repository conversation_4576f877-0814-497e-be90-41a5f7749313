<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="关联用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入关联用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="关联用户密匙" prop="keyId">
        <el-input
          v-model="queryParams.keyId"
          placeholder="请输入关联用户密匙"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="关联模型ID" prop="modelId">
        <el-input
          v-model="queryParams.modelId"
          placeholder="请输入关联模型ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="输入Token消耗量" prop="inputTokens">
        <el-input
          v-model="queryParams.inputTokens"
          placeholder="请输入输入Token消耗量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="输出Token消耗量" prop="outputTokens">
        <el-input
          v-model="queryParams.outputTokens"
          placeholder="请输入输出Token消耗量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="输入费用" prop="inputCost">
        <el-input
          v-model="queryParams.inputCost"
          placeholder="请输入输入费用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="输出费用" prop="outputCost">
        <el-input
          v-model="queryParams.outputCost"
          placeholder="请输入输出费用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="总费用" prop="totalCost">
        <el-input
          v-model="queryParams.totalCost"
          placeholder="请输入总费用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="调用的API路径" prop="apiEndpoint">
        <el-input
          v-model="queryParams.apiEndpoint"
          placeholder="请输入调用的API路径"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="扣费状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择扣费状态" clearable>
          <el-option
            v-for="dict in dict.type.user_bill_record_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关联调用记录ID" prop="invokeRecordId">
        <el-input
          v-model="queryParams.invokeRecordId"
          placeholder="请输入关联调用记录ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="请求时间" prop="requestTime">
        <el-date-picker clearable
          v-model="queryParams.requestTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择请求时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="响应时间" prop="responseTime">
        <el-date-picker clearable
          v-model="queryParams.responseTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择响应时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="总耗时(秒)" prop="responseDuration">
        <el-input
          v-model="queryParams.responseDuration"
          placeholder="请输入总耗时(秒)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="套餐扣减量(输入)" prop="packageInputTokens">
        <el-input
          v-model="queryParams.packageInputTokens"
          placeholder="请输入套餐扣减量(输入)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="套餐扣减量(输出)" prop="packageOutputTokens">
        <el-input
          v-model="queryParams.packageOutputTokens"
          placeholder="请输入套餐扣减量(输出)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['product:billRecord:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['product:billRecord:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['product:billRecord:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['product:billRecord:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="billRecordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="消费记录唯一标识" align="center" prop="billId" />
      <el-table-column label="关联用户ID" align="center" prop="userId" />
      <el-table-column label="关联用户密匙" align="center" prop="keyId" />
      <el-table-column label="关联模型ID" align="center" prop="modelId" />
      <el-table-column label="输入Token消耗量" align="center" prop="inputTokens" />
      <el-table-column label="输出Token消耗量" align="center" prop="outputTokens" />
      <el-table-column label="输入费用" align="center" prop="inputCost" />
      <el-table-column label="输出费用" align="center" prop="outputCost" />
      <el-table-column label="总费用" align="center" prop="totalCost" />
      <el-table-column label="调用的API路径" align="center" prop="apiEndpoint" />
      <el-table-column label="扣费状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.user_bill_record_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="关联调用记录ID" align="center" prop="invokeRecordId" />
      <el-table-column label="请求时间" align="center" prop="requestTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.requestTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="响应时间" align="center" prop="responseTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.responseTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="总耗时(秒)" align="center" prop="responseDuration" />
      <el-table-column label="套餐扣减量(输入)" align="center" prop="packageInputTokens" />
      <el-table-column label="套餐扣减量(输出)" align="center" prop="packageOutputTokens" />
      <el-table-column label="关联智能体" align="center" prop="agentId" />
      <el-table-column label="服务费" align="center" prop="serviceFee" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['product:billRecord:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['product:billRecord:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户下消费记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="关联用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入关联用户ID" />
        </el-form-item>
        <el-form-item label="关联用户密匙" prop="keyId">
          <el-input v-model="form.keyId" placeholder="请输入关联用户密匙" />
        </el-form-item>
        <el-form-item label="关联模型ID" prop="modelId">
          <el-input v-model="form.modelId" placeholder="请输入关联模型ID" />
        </el-form-item>
        <el-form-item label="输入Token消耗量" prop="inputTokens">
          <el-input v-model="form.inputTokens" placeholder="请输入输入Token消耗量" />
        </el-form-item>
        <el-form-item label="输出Token消耗量" prop="outputTokens">
          <el-input v-model="form.outputTokens" placeholder="请输入输出Token消耗量" />
        </el-form-item>
        <el-form-item label="输入费用" prop="inputCost">
          <el-input v-model="form.inputCost" placeholder="请输入输入费用" />
        </el-form-item>
        <el-form-item label="输出费用" prop="outputCost">
          <el-input v-model="form.outputCost" placeholder="请输入输出费用" />
        </el-form-item>
        <el-form-item label="总费用" prop="totalCost">
          <el-input v-model="form.totalCost" placeholder="请输入总费用" />
        </el-form-item>
        <el-form-item label="调用的API路径" prop="apiEndpoint">
          <el-input v-model="form.apiEndpoint" placeholder="请输入调用的API路径" />
        </el-form-item>
        <el-form-item label="扣费状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.user_bill_record_status"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="关联调用记录ID" prop="invokeRecordId">
          <el-input v-model="form.invokeRecordId" placeholder="请输入关联调用记录ID" />
        </el-form-item>
        <el-form-item label="请求时间" prop="requestTime">
          <el-date-picker clearable
            v-model="form.requestTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择请求时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="响应时间" prop="responseTime">
          <el-date-picker clearable
            v-model="form.responseTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择响应时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="总耗时(秒)" prop="responseDuration">
          <el-input v-model="form.responseDuration" placeholder="请输入总耗时(秒)" />
        </el-form-item>
        <el-form-item label="套餐扣减量(输入)" prop="packageInputTokens">
          <el-input v-model="form.packageInputTokens" placeholder="请输入套餐扣减量(输入)" />
        </el-form-item>
        <el-form-item label="套餐扣减量(输出)" prop="packageOutputTokens">
          <el-input v-model="form.packageOutputTokens" placeholder="请输入套餐扣减量(输出)" />
        </el-form-item>
        <el-form-item label="关联智能体id" prop="agentId">
          <el-input v-model="form.agentId" placeholder="关联智能体id" />
        </el-form-item>
        <el-form-item label="服务费" prop="serviceFee">
          <el-input v-model="form.serviceFee" placeholder="服务费" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listBillRecord, getBillRecord, delBillRecord, addBillRecord, updateBillRecord } from "@/api/product/billRecord";

export default {
  name: "BillRecord",
  dicts: ['user_bill_record_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户下消费记录表格数据
      billRecordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        keyId: null,
        modelId: null,
        inputTokens: null,
        outputTokens: null,
        inputCost: null,
        outputCost: null,
        totalCost: null,
        apiEndpoint: null,
        status: null,
        invokeRecordId: null,
        requestTime: null,
        responseTime: null,
        responseDuration: null,
        packageInputTokens: null,
        packageOutputTokens: null,
        agentId: null,
        serviceFee: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户下消费记录列表 */
    getList() {
      this.loading = true;
      listBillRecord(this.queryParams).then(response => {
        this.billRecordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        billId: null,
        userId: null,
        keyId: null,
        modelId: null,
        inputTokens: null,
        outputTokens: null,
        inputCost: null,
        outputCost: null,
        totalCost: null,
        apiEndpoint: null,
        status: null,
        invokeRecordId: null,
        requestTime: null,
        responseTime: null,
        responseDuration: null,
        packageInputTokens: null,
        packageOutputTokens: null,
        agentId: null,
        serviceFee: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.billId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户下消费记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const billId = row.billId || this.ids
      getBillRecord(billId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改用户下消费记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.billId != null) {
            updateBillRecord(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBillRecord(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const billIds = row.billId || this.ids;
      this.$modal.confirm('是否确认删除用户下消费记录编号为"' + billIds + '"的数据项？').then(function() {
        return delBillRecord(billIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('bill/record/export', {
        ...this.queryParams
      }, `billRecord_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
