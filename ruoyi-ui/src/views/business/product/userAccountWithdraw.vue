<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="100px">
            <el-form-item label="提款单号" prop="orderNo">
                <el-input
                    v-model="queryParams.orderNo"
                    placeholder="请输入提款单号"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="提现方式" prop="type">
                <el-select v-model="queryParams.type"
                           placeholder="请选择提现方式" clearable>
                    <el-option
                        v-for="dict in dict.type.account_withdraw_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
                    <el-option
                        v-for="dict in dict.type.account_withdraw_status"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="提现用户" prop="userId">
                <el-input
                    v-model="queryParams.userId"
                    placeholder="请输入提现用户"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['account:withdraw:export']"
                >导出
                </el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="withdrawList" @selection-change="handleSelectionChange" border>
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="主键ID" align="center" prop="id" width="150" show-overflow-tooltip/>
            <el-table-column label="提款单号" align="center" prop="orderNo" width="150" show-overflow-tooltip/>
            <el-table-column label="提现方式" align="center" prop="type" width="150" show-overflow-tooltip>
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.account_withdraw_type" :value="scope.row.type"/>
                </template>
            </el-table-column>
            <el-table-column label="出款方式" align="center" prop="method" width="150" show-overflow-tooltip>
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.account_withdraw_method" :value="scope.row.method"/>
                </template>
            </el-table-column>
            <el-table-column label="真实姓名" align="center" prop="realname" width="150" show-overflow-tooltip/>
            <el-table-column label="银行名称" align="center" prop="bankName" width="150" show-overflow-tooltip/>
            <el-table-column label="收款账号" align="center" prop="bankAccount" width="150" show-overflow-tooltip/>
            <el-table-column label="提款金额" align="center" prop="amount" width="150" show-overflow-tooltip/>
            <el-table-column label="手续费" align="center" prop="fee" width="150" show-overflow-tooltip/>
            <el-table-column label="实际到账金额" align="center" prop="totalAmount" width="150" show-overflow-tooltip/>
            <el-table-column label="状态" align="center" prop="status" width="150" show-overflow-tooltip>
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.account_withdraw_status" :value="scope.row.status"/>
                </template>
            </el-table-column>
            <el-table-column label="备注" align="center" prop="remark" width="150" show-overflow-tooltip/>
            <el-table-column label="提现用户" align="center" prop="userId" width="150" show-overflow-tooltip/>
            <el-table-column label="申请时间" align="center" prop="createTime" width="180" show-overflow-tooltip/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180"
                             fixed="right">
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['account:withdraw:edit']"
                    >详情
                    </el-button>
                    <!--
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['account:withdraw:remove']"
                    >删除
                    </el-button>-->
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleAuth(scope.row)"
                        v-if="scope.row.status === 0"
                        v-hasPermi="['account:withdraw:remove']"
                    >审核
                    </el-button>
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handlePic(scope.row)"
                        v-if="scope.row.status === 1"
                        v-hasPermi="['account:withdraw:remove']"
                    >上传打款凭证
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改用户提现对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
            <el-scrollbar class="custom-scrollbar" style="height: calc(90vh - 200px)">
                <el-form ref="form" :model="form" :rules="rules" label-width="120px" disabled>
                    <el-form-item label="提款单号" prop="orderNo">
                        <el-input v-model="form.orderNo" placeholder="请输入提款单号"/>
                    </el-form-item>
                    <el-form-item label="提现方式" prop="type">
                        <el-select v-model="form.type"
                                   placeholder="请选择提现方式">
                            <el-option
                                v-for="dict in dict.type.account_withdraw_type"
                                :key="dict.value"
                                :label="dict.label"
                                :value="parseInt(dict.value)"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="出款方式" prop="method">
                        <el-input v-model="form.method" placeholder="请输入出款方式"/>
                    </el-form-item>
                    <el-form-item label="真实姓名" prop="realname">
                        <el-input v-model="form.realname" placeholder="请输入真实姓名"/>
                    </el-form-item>
                    <el-form-item label="银行名称" prop="bankName">
                        <el-input v-model="form.bankName" placeholder="请输入银行名称"/>
                    </el-form-item>
                    <el-form-item label="收款账号" prop="bankAccount">
                        <el-input v-model="form.bankAccount" placeholder="请输入收款账号"/>
                    </el-form-item>
                    <el-form-item label="提款金额" prop="amount">
                        <el-input v-model="form.amount" placeholder="请输入提款金额"/>
                    </el-form-item>
                    <el-form-item label="手续费" prop="fee">
                        <el-input v-model="form.fee" placeholder="请输入手续费"/>
                    </el-form-item>
                    <el-form-item label="实际到账金额" prop="totalAmount">
                        <el-input v-model="form.totalAmount" placeholder="请输入实际到账金额"/>
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
                        <el-radio-group v-model="form.status">
                            <el-radio
                                v-for="dict in dict.type.account_withdraw_status"
                                :key="dict.value"
                                :label="parseInt(dict.value)"
                            >{{ dict.label }}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="备注" prop="remark">
                        <el-input v-model="form.remark" placeholder="请输入备注"/>
                    </el-form-item>
                    <el-form-item label="注册IP" prop="registerIp">
                        <el-input v-model="form.registerIp" placeholder="请输入注册IP"/>
                    </el-form-item>
                    <el-form-item label="注册IP实际地址" prop="registerIpAddress">
                        <el-input v-model="form.registerIpAddress" placeholder="请输入注册IP实际地址"/>
                    </el-form-item>
                    <el-form-item label="提现IP" prop="withdrawIp">
                        <el-input v-model="form.withdrawIp" placeholder="请输入提现IP"/>
                    </el-form-item>
                    <el-form-item label="提现IP实际地址" prop="withdrawIpAddress">
                        <el-input v-model="form.withdrawIpAddress" placeholder="请输入提现IP实际地址"/>
                    </el-form-item>
                    <el-form-item label="提现用户" prop="userId">
                        <el-input v-model="form.userId" placeholder="请输入提现用户"/>
                    </el-form-item>
                </el-form>
            </el-scrollbar>
            <div slot="footer" class="dialog-footer">
                <!--<el-button type="primary" @click="submitForm">确 定</el-button>-->
                <el-button @click="cancel">关 闭</el-button>
            </div>
        </el-dialog>

        <!--   审核弹窗     -->
        <el-dialog title="审核意见" :visible.sync="dialogVisible" width="50%" :close-on-click-modal="false"
                   :destroy-on-close="true">
            <el-form :model="authForm" label-width="100px">
                <el-form-item label="审核意见" prop="remark">
                    <el-input v-model="authForm.remark" autocomplete="off" type="textarea" :rows="5"></el-input>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="authForm.status">
                        <el-radio :label="1">通过</el-radio>
                        <el-radio :label="2">拒绝</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="handleAuthSubmit">确 定</el-button>
            </div>
        </el-dialog>

        <!--   审核弹窗     -->
        <el-dialog title="上传银行打款证明" :visible.sync="dialogVisible2" width="30%" :close-on-click-modal="false"
                   :destroy-on-close="true">
            <el-form :model="authForm2" label-width="100px">
                <el-form-item prop="proof" label="上传凭证">
                    <image-upload v-model="authForm2.proof" :limit="1"/>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible2 = false">取 消</el-button>
                <el-button type="primary" @click="handleAuthSubmit2">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {listWithdraw, getWithdraw, delWithdraw, addWithdraw, updateWithdraw} from "@/api/product/userAccountWithdraw";
import {devWithdrawApple} from "@/api/product/userAccountWithdraw";
import request from "../../../utils/request";

export default {
    name: "Withdraw",
    dicts: ['account_withdraw_status', 'account_withdraw_type', 'account_withdraw_method'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 用户提现表格数据
            withdrawList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                orderNo: null,
                type: null,
                method: null,
                realname: null,
                bankName: null,
                bankAccount: null,
                amount: null,
                fee: null,
                totalAmount: null,
                status: null,
                registerIp: null,
                registerIpAddress: null,
                withdrawIp: null,
                withdrawIpAddress: null,
                userId: null,
                orderByColumn:'createTime',
                isAsc:'desc'
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                orderNo: [
                    {required: true, message: "提款单号不能为空", trigger: "blur"}
                ],
            },

            // 审核相关
            dialogVisible: false,
            authForm: {
                ids: [],
                remark: '',
                status: ""
            },

            dialogVisible2: false,
            authForm2: {
                "proof": "",
                "withdrawId": ''
            },
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询用户提现列表 */
        getList() {
            this.loading = true;
            listWithdraw(this.queryParams).then(response => {
                this.withdrawList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                orderNo: null,
                type: null,
                method: null,
                realname: null,
                bankName: null,
                bankAccount: null,
                amount: null,
                fee: null,
                totalAmount: null,
                status: null,
                remark: null,
                registerIp: null,
                registerIpAddress: null,
                withdrawIp: null,
                withdrawIpAddress: null,
                createTime: null,
                updateTime: null,
                userId: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加用户提现";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            getWithdraw(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "用户提现详情";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        updateWithdraw(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addWithdraw(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal.confirm('是否确认删除用户提现编号为"' + ids + '"的数据项？').then(function () {
                return delWithdraw(ids);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('account/withdraw/export', {
                ...this.queryParams
            }, `withdraw_${new Date().getTime()}.xlsx`)
        },
        handleAuth(item) {
            this.dialogVisible = true;
            this.authForm.remark = ''
            this.authForm.status = ''
            this.authForm.ids = [item.id];
        },
        handleAuthSubmit() {
            if (!this.authForm.status) {
                return this.$message.error('请填写审核结果')
            }
            if (this.authForm.status === 2 && !this.authForm.remark) {
                return this.$message.error('请填写审核意见')
            }

            devWithdrawApple(this.authForm).then(response => {
                this.$modal.msgSuccess("操作成功");
                this.dialogVisible = false;
                this.getList();
            });
        },
        handleAuthSubmit2() {
            if (!this.authForm2.proof) {
                return this.$message.error('请上传图片')
            }

            request({
                url: '/account/withdraw/bankConfirm',
                method: 'post',
                data: this.authForm2
            }).then(response => {
                this.$modal.msgSuccess("操作成功");
                this.dialogVisible2 = false;
                this.getList();
            });
        },
        handlePic(item) {
            this.dialogVisible2 = true;
            this.authForm2.withdrawId = item.id
        }
    }
};
</script>
