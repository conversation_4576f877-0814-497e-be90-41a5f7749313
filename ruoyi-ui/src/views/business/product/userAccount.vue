<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px">
            <el-form-item label="用户名称" prop="userId">
                <el-input
                    v-model="queryParams.username"
                    placeholder="请输入用户名称"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAdd"
                    v-hasPermi="['product:account:add']"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="el-icon-edit"
                    size="mini"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['product:account:edit']"
                >修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['product:account:remove']"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['product:account:export']"
                >导出
                </el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="accountList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="主键" align="center" prop="accountId"/>
            <el-table-column label="用户名称" align="center" prop="username"/>
            <el-table-column label="账户余额" align="center" prop="balance"/>
            <el-table-column label="赠送余额" align="center" prop="giftBalance"/>
            <el-table-column label="总余额" align="center" prop="overallBalance"/>
            <el-table-column label="账户状态" align="center" prop="status">
                <template #default="scope">
                    {{ statusName(scope.row.status) }}
                </template>
            </el-table-column>
            <!--            <el-table-column label="剩余免费Token额度" align="center" prop="freeTokenQuota"/>-->
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['product:account:edit']"
                    >修改
                    </el-button>
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['product:account:remove']"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改用户账户对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="540px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="140px">
                <el-form-item label="用户名称" prop="username">
                    <!--                    <el-input v-model="form.username" placeholder="请输入用户名称"/>-->
                    <el-select
                        style="width: 100%"
                        v-model="form.userId" filterable remote
                        reserve-keyword
                        placeholder="请输入关键词"
                        :remote-method="remoteMethod"
                        :loading="loading">
                        <el-option
                            v-for="item in userList"
                            :key="item.userId"
                            :label="item.userName"
                            :value="item.userId">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="账户余额" prop="balance">
                    <el-input v-model.number="form.balance" placeholder="请输入账户余额"
                              @change="changeOverallBalance"/>
                </el-form-item>
                <el-form-item label="赠送余额" prop="giftBalance">
                    <el-input v-model.number="form.giftBalance" placeholder="请输入赠送余额"
                              @change="changeOverallBalance"/>
                </el-form-item>
                <el-form-item label="总余额" prop="overallBalance">
                    <el-input v-model="form.overallBalance" placeholder="请输入总余额" readonly/>
                </el-form-item>
                <!--                <el-form-item label="剩余免费Token额度" prop="freeTokenQuota">-->
                <!--                    <el-input v-model="form.freeTokenQuota" placeholder="请输入剩余免费Token额度"/>-->
                <!--                </el-form-item>-->
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {listAccount, getAccount, delAccount, addAccount, updateAccount} from "@/api/product/userAccount";
import {getUser, listUser} from "@/api/system/user";

export default {
    name: "Account",
    dicts: ['user_account_status'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 用户账户表格数据
            accountList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                userId: null,
                username: null,
                balance: null,
                giftBalance: null,
                overallBalance: null,
                status: null,
                freeTokenQuota: null
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {},
            userList: []
        };
    },
    created() {
        this.getList();
        console.log('aaa', this.dict.type.user_account_status)
    },
    methods: {
        /** 查询用户账户列表 */
        getList() {
            this.loading = true;
            listAccount(this.queryParams).then(response => {
                this.accountList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                accountId: null,
                userId: null,
                username: null,
                balance: null,
                giftBalance: null,
                overallBalance: null,
                status: null,
                createTime: null,
                freeTokenQuota: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.accountId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加用户账户";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const accountId = row.accountId || this.ids
            getAccount(accountId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改用户账户";

                // 正确处理远程搜索回显的问题
                getUser(this.form.userId).then(response => {
                    this.userList = [response.data]
                })
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.accountId != null) {
                        updateAccount(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addAccount(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const accountIds = row.accountId || this.ids;
            this.$modal.confirm('是否确认删除用户账户编号为"' + accountIds + '"的数据项？').then(function () {
                return delAccount(accountIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('product/account/export', {
                ...this.queryParams
            }, `account_${new Date().getTime()}.xlsx`)
        },
        changeOverallBalance() {
            this.form.overallBalance = this.form.balance + this.form.giftBalance
        },
        statusName(value) {
            let item = this.dict.type.user_account_status.find(n => n.value == value);
            if (item) {
                return item.label;
            } else {
                return '';
            }
        },
        remoteMethod(query) {
            if (query !== '') {
                this.loading = true;
                let params = {
                    pageNum: 1,
                    pageSize: 100,
                    userName: query,
                }
                listUser(params).then(response => {
                    console.log('用户列表', response)
                    this.loading = false;
                    this.userList = response.rows;
                })
            } else {
                this.userList = [];
            }
        },
    }
};
</script>
