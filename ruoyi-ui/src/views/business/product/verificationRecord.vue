<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="核销数量" prop="count">
        <el-input
          v-model="queryParams.count"
          placeholder="请输入核销数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="核销商品" prop="productId">
        <el-input
          v-model="queryParams.productId"
          placeholder="请输入核销商品"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="核销状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择核销状态" clearable>
          <el-option
            v-for="dict in dict.type.verification_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="模型名称" prop="modelName">
        <el-input
          v-model="queryParams.modelName"
          placeholder="请输入模型名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="核销记录" prop="verificationName">
        <el-input
          v-model="queryParams.verificationName"
          placeholder="请输入核销记录"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="核销人" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入核销人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['product:record:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['product:record:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['product:record:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['product:record:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange" @sort-change="handleSortChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="核销数量" align="center" prop="count" />
      <el-table-column label="核销商品" align="center" prop="productId" />
      <el-table-column label="核销状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.verification_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="模型名称" align="center" prop="modelName" />
      <el-table-column label="核销记录" align="center" prop="verificationName" />
      <el-table-column label="核销人" align="center" prop="userId" />
      <el-table-column label="创建时间" align="center" prop="createTime" :sort-orders="['descending','ascending']" sortable="custom" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['product:record:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['product:record:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改核销记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false" destroy-on-close>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="核销数量" prop="count">
          <el-input v-model="form.count" placeholder="请输入核销数量" />
        </el-form-item>
        <el-form-item label="核销商品" prop="productId">
          <el-input v-model="form.productId" placeholder="请输入核销商品" />
        </el-form-item>
        <el-form-item label="核销状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.verification_status"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="模型名称" prop="modelName">
          <el-input v-model="form.modelName" placeholder="请输入模型名称" />
        </el-form-item>
        <el-form-item label="核销记录" prop="verificationName">
          <el-input v-model="form.verificationName" placeholder="请输入核销记录" />
        </el-form-item>
        <el-form-item label="核销人" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入核销人" />
        </el-form-item>
        <el-divider content-position="center">核销商品信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddVerificationProduct">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteVerificationProduct">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="verificationProductList" :row-class-name="rowVerificationProductIndex" @selection-change="handleVerificationProductSelectionChange" ref="verificationProduct">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
          <el-table-column label="核销商品id" prop="productId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.productId" placeholder="请输入核销商品id" />
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="quantity" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.quantity" placeholder="请输入数量" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRecord, getRecord, delRecord, addRecord, updateRecord } from "@/api/product/verificationRecord";

export default {
  name: "Record",
  dicts: ['verification_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedVerificationProduct: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 核销记录表格数据
      recordList: [],
      // 核销商品表格数据
      verificationProductList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        count: null,
        productId: null,
        status: null,
        modelName: null,
        verificationName: null,
        userId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询核销记录列表 */
    getList() {
      this.loading = true;
      listRecord(this.queryParams).then(response => {
        this.recordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        count: null,
        productId: null,
        status: null,
        modelName: null,
        verificationName: null,
        userId: null,
        createBy: null,
        createTime: null,
        updateTime: null
      };
      this.verificationProductList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加核销记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getRecord(id).then(response => {
        this.form = response.data;
        this.verificationProductList = response.data.verificationProductList;
        this.open = true;
        this.title = "修改核销记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.verificationProductList = this.verificationProductList;
          if (this.form.id != null) {
            updateRecord(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRecord(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 排序 */
    handleSortChange(column){
      this.queryParams.orderByColumn = column.prop;//查询字段是表格中字段名字
      this.queryParams.isAsc = column.order;//动态取值排序顺序
      console.log(this.queryParams);
      this.getList();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除核销记录编号为"' + ids + '"的数据项？').then(function() {
        return delRecord(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 核销商品序号 */
    rowVerificationProductIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 核销商品添加按钮操作 */
    handleAddVerificationProduct() {
      let obj = {};
      obj.productId = "";
      obj.quantity = "";
      this.verificationProductList.push(obj);
    },
    /** 核销商品删除按钮操作 */
    handleDeleteVerificationProduct() {
      if (this.checkedVerificationProduct.length == 0) {
        this.$modal.msgError("请先选择要删除的核销商品数据");
      } else {
        const verificationProductList = this.verificationProductList;
        const checkedVerificationProduct = this.checkedVerificationProduct;
        this.verificationProductList = verificationProductList.filter(function(item) {
          return checkedVerificationProduct.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleVerificationProductSelectionChange(selection) {
      this.checkedVerificationProduct = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('verification/record/export', {
        ...this.queryParams
      }, `record_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
