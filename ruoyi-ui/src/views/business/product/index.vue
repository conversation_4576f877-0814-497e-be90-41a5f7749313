<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px">
            <el-form-item label="商品名称" prop="productName">
                <el-input
                    v-model="queryParams.productName"
                    placeholder="请输入商品名称"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="商品类型" prop="type">
                <el-select v-model="queryParams.type" placeholder="请选择商品类型" clearable>
                    <el-option
                        v-for="dict in dict.type.product_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAdd"
                    v-hasPermi="['product:product:add']"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="el-icon-edit"
                    size="mini"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['product:product:edit']"
                >修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['product:product:remove']"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['product:product:export']"
                >导出
                </el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="productList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="商品id" align="center" prop="productId"/>
            <el-table-column label="商品名称" align="center" prop="productName"/>
            <el-table-column label="商品图片" align="center" prop="pic" width="100">
                <template slot-scope="scope">
                    <image-preview :src="scope.row.pic" :width="24" :height="24" v-if="scope.row.pic"/>
                </template>
            </el-table-column>
            <el-table-column label="现价格" align="center" prop="currentPrice"/>
            <el-table-column label="原价格" align="center" prop="originalPrice"/>
            <el-table-column label="会员价格" align="center" prop="memberPrice"/>
            <el-table-column label="单位" align="center" prop="unit"/>
            <el-table-column label="商品类型" align="center" prop="type">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.product_type" :value="scope.row.type"/>
                </template>
            </el-table-column>
            <el-table-column label="有效时限" align="center" prop="validDate"/>
            <el-table-column label="可用输入token数量" align="center" prop="inputTokenQuota" width="200px">
                <template #default="scope">
                    {{ formatNumber(scope.row.inputTokenQuota) }}
                </template>
            </el-table-column>
            <el-table-column label="可用输出token数量" align="center" prop="outputTokenQuota" width="200px">
                <template #default="scope">
                    {{ formatNumber(scope.row.outputTokenQuota) }}
                </template>
            </el-table-column>
            <el-table-column label="备注" align="center" prop="remark"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['product:product:edit']"
                    >修改
                    </el-button>
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['product:product:remove']"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改AI商品对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="540px" append-to-body :close-on-click-modal="false" destroy-on-close>
            <el-scrollbar class="custom-scrollbar" style="height: 600px">
                <el-form ref="form" :model="form" :rules="rules" label-width="140px" size="mini">
                    <el-form-item label="商品名称" prop="productName">
                        <el-input v-model="form.productName" placeholder="请输入商品名称"/>
                    </el-form-item>
                    <el-form-item label="商品图片" prop="pic">
                        <image-upload v-model="form.pic" :limit="1"/>
                    </el-form-item>
                    <el-form-item label="现价格" prop="currentPrice">
                        <el-input v-model="form.currentPrice" placeholder="请输入现价格"/>
                    </el-form-item>
                    <el-form-item label="原价格" prop="originalPrice">
                        <el-input v-model="form.originalPrice" placeholder="请输入原价格"/>
                    </el-form-item>
                    <el-form-item label="会员价" prop="originalPrice">
                        <el-input v-model="form.memberPrice" placeholder="请输入会员价"/>
                    </el-form-item>
                    <el-form-item label="单位" prop="unit">
                        <el-input v-model="form.unit" placeholder="请输入单位"/>
                    </el-form-item>
                    <el-form-item label="商品类型" prop="type">
                        <el-select v-model="form.type" placeholder="请选择商品类型">
                            <el-option
                                v-for="dict in dict.type.product_type"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="有效时限" prop="validDate" v-if="form.type =='0'">
                        <el-input v-model="form.validDate" placeholder="请输入有效时限">
                            <template #append>天</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="可用输入token数量" prop="inputTokenQuota">
                        <el-input v-model="form.inputTokenQuota" placeholder="请输入可用输入token数量"/>
                    </el-form-item>
                    <el-form-item label="可用输出token数量" prop="outputTokenQuota">
                        <el-input v-model="form.outputTokenQuota" placeholder="请输入可用输出token数量"/>
                    </el-form-item>
                    <el-form-item label="备注" prop="remark">
                        <el-input v-model="form.remark" placeholder="请输入备注"/>
                    </el-form-item>
                </el-form>
            </el-scrollbar>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {listProduct, getProduct, delProduct, addProduct, updateProduct} from "@/api/product/product";

export default {
    name: "Product",
    dicts: ['product_type'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // AI商品表格数据
            productList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                productName: null,
                pic: null,
                currentPrice: null,
                originalPrice: null,
                memberPrice: null,
                unit: null,
                type: null,
                packageType: null
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {}
        };
    },
    created() {
        this.getList();
        console.log('商品类型', this.dict.type.product_type)
    },
    methods: {
        /** 查询AI商品列表 */
        getList() {
            this.loading = true;
            listProduct(this.queryParams).then(response => {
                this.productList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                productId: null,
                productName: null,
                pic: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null,
                currentPrice: null,
                originalPrice: null,
                unit: null,
                type: null,
                remark: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.productId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加AI商品";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const productId = row.productId || this.ids
            getProduct(productId).then(response => {
                this.form = response.data;
                this.form.type = this.form.type + ''
                this.open = true;
                this.title = "修改AI商品";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.productId != null) {
                        updateProduct(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addProduct(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const productIds = row.productId || this.ids;
            this.$modal.confirm('是否确认删除AI商品编号为"' + productIds + '"的数据项？').then(function () {
                return delProduct(productIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('product/product/export', {
                ...this.queryParams
            }, `product_${new Date().getTime()}.xlsx`)
        },
        formatNumber(num) {
            // 处理规格回显的
            if (num === 0) return '0';


            const absNum = Math.abs(num);
            let value, suffix;

            if (absNum >= 1000000000) {
                // 处理 B (十亿)
                value = Math.round(num / 100000000) / 10; // 四舍五入到 0.1B
                suffix = 'B';
            } else if (absNum >= 1000000) {
                // 处理 M (百万)
                value = Math.round(num / 100000) / 10; // 四舍五入到 0.1M
                suffix = 'M';
            } else if (absNum >= 1000) {
                // 处理 K (千)
                value = Math.round(num / 100) / 10; // 四舍五入到 0.1K
                suffix = 'K';
            } else {
                return num; // 小于 1000 直接返回
            }

            return value + suffix;
            // return {
            //     value: value,
            //     suffix: suffix
            // };
        }
    }
};
</script>
