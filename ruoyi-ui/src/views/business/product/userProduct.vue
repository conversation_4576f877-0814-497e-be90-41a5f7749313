<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px">
            <el-form-item label="用户id" prop="userId">
                <el-input
                    v-model="queryParams.userId"
                    placeholder="请输入用户id"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="手机号" prop="userPhoneNumber">
                <el-input
                    v-model="queryParams.userPhoneNumber"
                    placeholder="请输入手机号"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="商品id" prop="productId">
                <el-input
                    v-model="queryParams.productId"
                    placeholder="请输入商品id"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="商品名称" prop="productName">
                <el-input
                    v-model="queryParams.productName"
                    placeholder="请输入商品名称"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAdd"
                    v-hasPermi="['product:product:add']"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="el-icon-edit"
                    size="mini"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['product:product:edit']"
                >修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['product:product:remove']"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['product:product:export']"
                >导出
                </el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="productList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"/>
<!--            <el-table-column label="主键" align="center" prop="id"/>-->
            <el-table-column label="用户id" align="center" prop="userId"/>
            <el-table-column label="用户名" align="center" prop="userName" width="200"/>
            <el-table-column label="手机号" align="center" prop="userPhoneNumber" width="200"/>
            <el-table-column label="商品id" align="center" prop="productId"/>
            <el-table-column label="商品名称" align="center" prop="productName" width="200"/>
            <el-table-column label="可用数量" align="center" prop="quantity" width="200"/>
            <el-table-column label="总数量" align="center" prop="totalQuantity"/>
            <el-table-column label="单位" align="center" prop="unit"/>
            <el-table-column label="创建日期" align="center" prop="createTime" width="200"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="200">
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['product:product:edit']"
                    >修改
                    </el-button>
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['product:product:remove']"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改用户商品对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body :close-on-click-modal="false" destroy-on-close>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="用户id" prop="userId">
                    <el-input v-model="form.userId" placeholder="请输入用户id"/>
                </el-form-item>
                <el-form-item label="商品id" prop="productId">
                    <!--<el-input v-models="form.productId" placeholder="请输入商品id"/>-->
                    <el-select v-model="form.productId" placeholder="请选择商品" style="width: 100%;">
                        <template v-for="(good,goodIndex) in goodList">
                            <el-option :label="good.productName" :value="good.productId"></el-option>
                        </template>
                    </el-select>
                </el-form-item>
                <el-form-item label="可用数量" prop="quantity">
                    <el-input v-model="form.quantity" placeholder="请输入可用数量"/>
                </el-form-item>
                <el-form-item label="总数量" prop="totalQuantity">
                    <el-input v-model="form.totalQuantity" placeholder="请输入总数量"/>
                </el-form-item>
                <el-form-item label="已使用量" prop="useQuantity">
                    <el-input v-model="form.useQuantity" placeholder="请输入已使用量"/>
                </el-form-item>
                <el-form-item label="单位" prop="unit">
                    <el-input v-model="form.unit" placeholder="请输入单位"/>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {listProduct, getProduct, delProduct, addProduct, updateProduct} from "@/api/product/userProduct";
import request from "@/utils/request";

export default {
    name: "Product",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 用户商品表格数据
            productList: [],
            goodList: [], // 商品列表
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                userId: null,
                productId: null,
                quantity: null,
                totalQuantity: null,
                useQuantity: null,
                unit: null,
                userPhoneNumber: null,
                productName: null
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                quantity: [
                    {required: true, message: "可用数量不能为空", trigger: "blur"}
                ],
                totalQuantity: [
                    {required: true, message: "总数量不能为空", trigger: "blur"}
                ],
                useQuantity: [
                    {required: true, message: "已使用量不能为空", trigger: "blur"}
                ],
            }
        };
    },
    created() {
        this.getList();
        this.getProductList();
    },
    methods: {
        /** 查询用户商品列表 */
        getList() {
            this.loading = true;
            let params = {
                ...this.queryParams
            }

            if (!params.orderByColumn) {
                params.orderByColumn = 'id';
                params.isAsc = 'descending'
            }

            listProduct(params).then(response => {
                this.productList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                userId: null,
                productId: null,
                quantity: null,
                totalQuantity: null,
                useQuantity: null,
                unit: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加用户商品";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            getProduct(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改用户商品";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        updateProduct(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addProduct(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal.confirm('是否确认删除用户商品编号为"' + ids + '"的数据项？').then(function () {
                return delProduct(ids);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('user/product/export', {
                ...this.queryParams
            }, `product_${new Date().getTime()}.xlsx`)
        },
        getProductList() {
            request({
                url: '/product/list',
                method: 'get',
                params: {
                    pageNum: 1,
                    pageSize: 500
                }
            }).then(response => {
                console.log('商品列表', response)
                this.goodList = response.rows;
            })
        }
    }
};
</script>
