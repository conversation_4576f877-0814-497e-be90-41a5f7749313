<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px">
            <el-form-item label="反馈类型" prop="type">
                <el-select v-model="queryParams.type" placeholder="请选择反馈类型" clearable>
                    <el-option
                        v-for="dict in dict.type.feedback_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <!--            <el-col :span="1.5">
                            <el-button
                                type="primary"
                                plain
                                icon="el-icon-plus"
                                size="mini"
                                @click="handleAdd"
                                v-hasPermi="['api_hub:feedback:add']"
                            >新增
                            </el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button
                                type="success"
                                plain
                                icon="el-icon-edit"
                                size="mini"
                                :disabled="single"
                                @click="handleUpdate"
                                v-hasPermi="['api_hub:feedback:edit']"
                            >修改
                            </el-button>
                        </el-col>-->
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['api_hub:feedback:remove']"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['api_hub:feedback:export']"
                >导出
                </el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="feedbackList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="主键" align="center" prop="id"/>
            <el-table-column label="反馈人" align="center" prop="userId"/>
            <el-table-column label="手机号" align="center" prop="phoneNumber"/>
            <el-table-column label="反馈类型" align="center" prop="type">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.feedback_type" :value="scope.row.type"/>
                </template>
            </el-table-column>
            <el-table-column label="反馈内容" align="center" prop="content"/>
            <el-table-column label="反馈图片" align="center" prop="images">
                <template slot-scope="scope">
                    <el-image :src="scope.row.images" style="width: 40px; height: 40px; cursor: pointer;" fit="cover"
                              :preview-src-list="[scope.row.images]" v-if="scope.row.images"></el-image>
                </template>
            </el-table-column>
            <el-table-column label="联系方式" align="center" prop="information"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <!--                    <el-button-->
                    <!--                        size="mini"-->
                    <!--                        type="text"-->
                    <!--                        icon="el-icon-edit"-->
                    <!--                        @click="handleUpdate(scope.row)"-->
                    <!--                        v-hasPermi="['api_hub:feedback:edit']"-->
                    <!--                    >修改-->
                    <!--                    </el-button>-->
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['api_hub:feedback:remove']"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改意见反馈对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="反馈人" prop="userId">
                    <el-input v-model="form.userId" placeholder="请输入反馈人"/>
                </el-form-item>
                <el-form-item label="反馈类型" prop="type">
                    <el-select v-model="form.type" placeholder="请选择反馈类型">
                        <el-option
                            v-for="dict in dict.type.feedback_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="parseInt(dict.value)"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="反馈内容">
                    <editor v-model="form.content" :min-height="192"/>
                </el-form-item>
                <el-form-item label="反馈图片" prop="images">
                    <el-input v-model="form.images" placeholder="请输入反馈图片"/>
                </el-form-item>
                <el-form-item label="联系方式" prop="information">
                    <el-input v-model="form.information" placeholder="请输入联系方式"/>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {listFeedback, getFeedback, delFeedback, addFeedback, updateFeedback} from "@/api/feedback/feedback";

export default {
    name: "Feedback",
    dicts: ['feedback_type'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 意见反馈表格数据
            feedbackList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                userId: null,
                type: null,
                content: null,
                images: null,
                information: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                userId: [
                    {required: true, message: "反馈人不能为空", trigger: "blur"}
                ],
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询意见反馈列表 */
        getList() {
            this.loading = true;
            listFeedback(this.queryParams).then(response => {
                this.feedbackList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                userId: null,
                type: null,
                content: null,
                images: null,
                information: null,
                createTime: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加意见反馈";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            getFeedback(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改意见反馈";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        updateFeedback(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addFeedback(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal.confirm('是否确认删除意见反馈编号为"' + ids + '"的数据项？').then(function () {
                return delFeedback(ids);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('feedback/export', {
                ...this.queryParams
            }, `feedback_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>
