<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="60px">
            <el-form-item label="调用者" prop="username">
                <el-input
                    v-model="queryParams.username"
                    placeholder="请输入调用者"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table ref="tables" v-loading="loading" :data="recordList" @selection-change="handleSelectionChange" :default-sort="defaultSort" @sort-change="handleSortChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="主键" align="center" prop="id"/>
            <el-table-column label="调用者" align="center" prop="username"/>
            <el-table-column label="请求完整路径" align="center" prop="path"/>
            <el-table-column label="模型id" align="center" prop="modelId"/>
            <el-table-column label="调用状态" align="center" prop="invokeStatus"/>
            <el-table-column label="调用时间" align="center" prop="createTime" sortable="custom" :sort-orders="['descending', 'ascending']"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['api_hub:record:edit']"
                    >查看
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改接口调用记录对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="调用者" prop="username">
                    <el-input v-model="form.username" placeholder="请输入调用者"/>
                </el-form-item>
                <el-form-item label="请求完整路径" prop="path">
                    <el-input v-model="form.path" placeholder="请输入请求完整路径"/>
                </el-form-item>
                <el-form-item label="模型id" prop="modelId">
                    <el-input v-model="form.modelId" placeholder="请输入模型id"/>
                </el-form-item>
                <el-form-item label="参数" prop="invokeParameter">
                    <el-input v-model="form.invokeParameter" type="textarea" placeholder="请输入内容"/>
                </el-form-item>
                <el-form-item label="响应内容" prop="invokeResult">
                    <el-input v-model="form.invokeResult" type="textarea" placeholder="请输入内容"/>
                </el-form-item>
<!--                <el-form-item label="处理结果" prop="callbackResult">-->
<!--                    <el-input v-model="form.callbackResult" placeholder="请输入处理结果"/>-->
<!--                </el-form-item>-->
<!--                <el-form-item label="关联核销记录" prop="billId">-->
<!--                    <el-input v-model="form.billId" placeholder="请输入关联核销记录"/>-->
<!--                </el-form-item>-->
            </el-form>
<!--            <div slot="footer" class="dialog-footer">-->
<!--                <el-button type="primary" @click="submitForm">确 定</el-button>-->
<!--                <el-button @click="cancel">取 消</el-button>-->
<!--            </div>-->
        </el-dialog>
    </div>
</template>

<script>
import {listRecord, getRecord, delRecord, addRecord, updateRecord} from "@/api/api_hub/record";

export default {
    name: "Record",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 接口调用记录表格数据
            recordList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                username: null,
                path: null,
                modelId: null,
                invokeParameter: null,
                invokeStatus: null,
                invokeResult: null,
                callbackResult: null,
                billId: null,
            },
            // 默认排序 createTime 降序排序
            defaultSort: {prop: 'createTime', order: 'descending'},
            // 表单参数
            form: {},
            // 表单校验
            rules: {}
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询接口调用记录列表 */
        getList() {
            this.loading = true;
            listRecord(this.queryParams).then(response => {
                this.recordList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                username: null,
                path: null,
                modelId: null,
                invokeParameter: null,
                invokeStatus: null,
                invokeResult: null,
                callbackResult: null,
                billId: null,
                createTime: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
            //重置排序参数：对包含ref属性值为table的元素排序
            this.$refs.table.sort(this.defaultSort.prop, this.defaultSort.order)
        },
        /** 排序触发事件：将选中列的prop、order值获取并赋值给查询参数，供后台列表查询用 */
        handleSortChange(column, prop, order) {
        this.queryParams.orderByColumn = column.prop;
        this.queryParams.isAsc = column.order;
        this.getList();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加接口调用记录";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            getRecord(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "接口调用记录详情";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        updateRecord(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addRecord(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal.confirm('是否确认删除接口调用记录编号为"' + ids + '"的数据项？').then(function () {
                return delRecord(ids);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('invoke/record/export', {
                ...this.queryParams
            }, `record_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>
