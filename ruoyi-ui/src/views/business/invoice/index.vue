<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="80px">
            <el-form-item label="抬头名称" prop="invoiceTitle">
                <el-input
                    v-model="queryParams.invoiceTitle"
                    placeholder="请输入抬头名称"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="税号" prop="taxNum">
                <el-input
                    v-model="queryParams.taxNum"
                    placeholder="请输入税号"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['api_hub:invoice:export']"
                >导出
                </el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="invoiceList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="发票唯一标识" align="center" prop="invoiceId" width="200" show-overflow-tooltip/>
            <el-table-column label="用户id" align="center" prop="userId" width="200" show-overflow-tooltip/>
            <el-table-column label="开票金额" align="center" prop="amount" width="200" show-overflow-tooltip/>
            <el-table-column label="发票类型" align="center" prop="invoiceType" width="200" show-overflow-tooltip/>
            <el-table-column label="抬头名称" align="center" prop="invoiceTitle" width="200" show-overflow-tooltip/>
            <el-table-column label="税号" align="center" prop="taxNum" width="200" show-overflow-tooltip/>
            <el-table-column label="费用项名称" align="center" prop="amountType" width="200" show-overflow-tooltip/>
            <el-table-column label="发票状态" align="center" prop="invoiceStatus" width="200" show-overflow-tooltip/>
            <el-table-column label="申请时间" align="center" prop="applyDate" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.applyDate, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="开票日期" align="center" prop="issueDate" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.issueDate, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="交付方式" align="center" prop="deliveryMethod" width="200" show-overflow-tooltip/>
            <el-table-column label="交付信息" align="center" prop="deliveryInfo" width="200" show-overflow-tooltip/>
            <el-table-column label="备注" align="center" prop="remark" width="200" show-overflow-tooltip/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right"
                             width="100">
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleAuth(scope.row)"
                    >审核
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改用户开票申请对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body :close-on-click-modal="false">
            <el-form ref="form" :model="form" :rules="rules" label-width="120px" disabled>
                <el-form-item label="用户id" prop="userId">
                    <el-input v-model="form.userId" placeholder="请输入用户id"/>
                </el-form-item>
                <el-form-item label="开票金额" prop="amount">
                    <el-input v-model="form.amount" placeholder="请输入开票金额"/>
                </el-form-item>
                <el-form-item label="抬头名称" prop="invoiceTitle">
                    <el-input v-model="form.invoiceTitle" placeholder="请输入抬头名称"/>
                </el-form-item>
                <el-form-item label="税号" prop="taxNum">
                    <el-input v-model="form.taxNum" placeholder="请输入税号"/>
                </el-form-item>
                <el-form-item label="申请时间" prop="applyDate">
                    <el-date-picker clearable
                                    v-model="form.applyDate"
                                    type="date"
                                    value-format="yyyy-MM-dd"
                                    placeholder="请选择申请时间">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="开票日期" prop="issueDate">
                    <el-date-picker clearable
                                    v-model="form.issueDate"
                                    type="date"
                                    value-format="yyyy-MM-dd"
                                    placeholder="请选择开票日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="交付方式" prop="deliveryMethod">
                    <el-input v-model="form.deliveryMethod" placeholder="请输入交付方式"/>
                </el-form-item>
                <el-form-item label="交付信息" prop="deliveryInfo">
                    <el-input v-model="form.deliveryInfo" placeholder="请输入交付信息"/>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" placeholder="请输入备注"/>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="cancel">关 闭</el-button>
            </div>
        </el-dialog>

        <!--   审核弹窗     -->
        <el-dialog title="审核意见" :visible.sync="dialogVisible" width="50%" :close-on-click-modal="false"
                   :destroy-on-close="true">
            <el-form :model="authForm" label-width="100px">
                <el-form-item label="审核意见" prop="auditReason">
                    <el-input v-model="authForm.auditReason" autocomplete="off" type="textarea" :rows="5"></el-input>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="authForm.status">
                        <el-radio :label="1">通过</el-radio>
                        <el-radio :label="2">拒绝</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="handleAuthSubmit">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {listInvoice, getInvoice, delInvoice, addInvoice, updateInvoice} from "@/api/invoice/userInvoice";
import {invoiceExamine} from "../../../api/web";

export default {
    name: "Invoice",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 用户开票申请表格数据
            invoiceList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                userId: null,
                amount: null,
                invoiceType: null,
                invoiceTitle: null,
                taxNum: null,
                amountType: null,
                invoiceStatus: null,
                applyDate: null,
                issueDate: null,
                deliveryMethod: null,
                deliveryInfo: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {},

            // 审核相关
            dialogVisible: false,
            authForm: {
                ids: [],
                auditReason: '',
                status: ""
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询用户开票申请列表 */
        getList() {
            this.loading = true;
            listInvoice(this.queryParams).then(response => {
                this.invoiceList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                invoiceId: null,
                userId: null,
                amount: null,
                invoiceType: null,
                invoiceTitle: null,
                taxNum: null,
                amountType: null,
                invoiceStatus: null,
                applyDate: null,
                issueDate: null,
                deliveryMethod: null,
                deliveryInfo: null,
                remark: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.invoiceId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加用户开票申请";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const invoiceId = row.invoiceId || this.ids
            getInvoice(invoiceId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "用户开票申请";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.invoiceId != null) {
                        updateInvoice(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addInvoice(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const invoiceIds = row.invoiceId || this.ids;
            this.$modal.confirm('是否确认删除用户开票申请编号为"' + invoiceIds + '"的数据项？').then(function () {
                return delInvoice(invoiceIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('api_hub/invoice/export', {
                ...this.queryParams
            }, `invoice_${new Date().getTime()}.xlsx`)
        },
        handleAuth(item) {
            this.dialogVisible = true;
            this.authForm.auditReason = ''
            this.authForm.status = ''
            this.authForm.ids = [item.invoiceId];
        },
        handleAuthSubmit() {
            if (!this.authForm.status) {
                return this.$message.error('请填写审核结果')
            }
            if (this.authForm.status === 2 && !this.authForm.auditReason) {
                return this.$message.error('请填写审核意见')
            }

            invoiceExamine(this.authForm).then(response => {
                this.$modal.msgSuccess("操作成功");
                this.dialogVisible = false;
                this.getList();
            });
        }
    }
};
</script>
