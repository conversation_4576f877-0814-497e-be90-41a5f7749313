<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px">
            <el-form-item label="调用者" prop="userId">
                <el-input
                    v-model="queryParams.userId"
                    placeholder="请输入调用者"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="请求完整路径" prop="path">
                <el-input
                    v-model="queryParams.path"
                    placeholder="请输入请求完整路径"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="接口id" prop="interfaceInfoId">
                <el-input
                    v-model="queryParams.interfaceInfoId"
                    placeholder="请输入接口id"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="参数" prop="invokeParameter">
                <el-input
                    v-model="queryParams.invokeParameter"
                    placeholder="请输入参数"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="长任务id" prop="taskId">
                <el-input
                    v-model="queryParams.taskId"
                    placeholder="请输入长任务id"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="处理结果" prop="callbackResult">
                <el-input
                    v-model="queryParams.callbackResult"
                    placeholder="请输入处理结果"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="消息发送结果" prop="msgResult">
                <el-input
                    v-model="queryParams.msgResult"
                    placeholder="请输入消息发送结果"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="关联核销记录" prop="verificationRecordId">
                <el-input
                    v-model="queryParams.verificationRecordId"
                    placeholder="请输入关联核销记录"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <!-- <el-row :gutter="10" class="mb8">
             <el-col :span="1.5">
                 <el-button
                     type="primary"
                     plain
                     icon="el-icon-plus"
                     size="mini"
                     @click="handleAdd"
                     v-hasPermi="['ai:record:add']"
                 >新增</el-button>
             </el-col>
             <el-col :span="1.5">
                 <el-button
                     type="success"
                     plain
                     icon="el-icon-edit"
                     size="mini"
                     :disabled="single"
                     @click="handleUpdate"
                     v-hasPermi="['ai:record:edit']"
                 >修改</el-button>
             </el-col>
             <el-col :span="1.5">
                 <el-button
                     type="danger"
                     plain
                     icon="el-icon-delete"
                     size="mini"
                     :disabled="multiple"
                     @click="handleDelete"
                     v-hasPermi="['ai:record:remove']"
                 >删除</el-button>
             </el-col>
             <el-col :span="1.5">
                 <el-button
                     type="warning"
                     plain
                     icon="el-icon-download"
                     size="mini"
                     @click="handleExport"
                     v-hasPermi="['ai:record:export']"
                 >导出</el-button>
             </el-col>
             <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
         </el-row>-->

        <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="主键" align="center" prop="id"/>
            <el-table-column label="调用者" align="center" prop="userId"/>
            <el-table-column label="请求完整路径" align="center" prop="path" :show-overflow-tooltip="true"/>
            <el-table-column label="接口id" align="center" prop="interfaceInfoId"/>
            <el-table-column label="参数" align="center" prop="invokeParameter"/>
            <el-table-column label="调用状态" align="center" prop="invokeStatus"/>
            <el-table-column label="响应内容" align="center" prop="invokeResult">
                <template slot-scope="scope">
                    <el-tooltip class="item" effect="dark" :content="scope.row.invokeResult" placement="top-start">
                        <el-button type="text" style="color: #303033">{{ scope.row.invokeResult }}</el-button>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column label="长任务id" align="center" prop="taskId"/>
            <el-table-column label="处理结果" align="center" prop="callbackResult"/>
            <el-table-column label="消息发送结果" align="center" prop="msgResult"/>
            <el-table-column label="关联核销记录" align="center" prop="verificationRecordId"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['ai:record:edit']"
                    >修改
                    </el-button>
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['ai:record:remove']"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改接口调用记录对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false" destroy-on-close>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="调用者" prop="userId">
                    <el-input v-model="form.userId" placeholder="请输入调用者"/>
                </el-form-item>
                <el-form-item label="请求完整路径" prop="path">
                    <el-input v-model="form.path" placeholder="请输入请求完整路径"/>
                </el-form-item>
                <el-form-item label="接口id" prop="interfaceInfoId">
                    <el-input v-model="form.interfaceInfoId" placeholder="请输入接口id"/>
                </el-form-item>
                <el-form-item label="参数" prop="invokeParameter">
                    <el-input v-model="form.invokeParameter" placeholder="请输入参数"/>
                </el-form-item>
                <el-form-item label="响应内容" prop="invokeResult">
                    <el-input v-model="form.invokeResult" type="textarea" placeholder="请输入内容"/>
                </el-form-item>
                <el-form-item label="长任务id" prop="taskId">
                    <el-input v-model="form.taskId" placeholder="请输入长任务id"/>
                </el-form-item>
                <el-form-item label="处理结果" prop="callbackResult">
                    <el-input v-model="form.callbackResult" placeholder="请输入处理结果"/>
                </el-form-item>
                <el-form-item label="消息发送结果" prop="msgResult">
                    <el-input v-model="form.msgResult" placeholder="请输入消息发送结果"/>
                </el-form-item>
                <el-form-item label="关联核销记录" prop="verificationRecordId">
                    <el-input v-model="form.verificationRecordId" placeholder="请输入关联核销记录"/>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {listRecord, getRecord, delRecord, addRecord, updateRecord} from "@/api/invoke/record";

export default {
    name: "Record",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 接口调用记录表格数据
            recordList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                userId: null,
                path: null,
                interfaceInfoId: null,
                invokeParameter: null,
                invokeStatus: null,
                invokeResult: null,
                taskId: null,
                callbackResult: null,
                msgResult: null,
                verificationRecordId: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {}
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询接口调用记录列表 */
        getList() {
            this.loading = true;
            listRecord(this.queryParams).then(response => {
                this.recordList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                userId: null,
                path: null,
                interfaceInfoId: null,
                invokeParameter: null,
                invokeStatus: null,
                invokeResult: null,
                taskId: null,
                callbackResult: null,
                msgResult: null,
                verificationRecordId: null,
                createTime: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加接口调用记录";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            getRecord(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改接口调用记录";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        updateRecord(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addRecord(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal.confirm('是否确认删除接口调用记录编号为"' + ids + '"的数据项？').then(function () {
                return delRecord(ids);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('ai/record/export', {
                ...this.queryParams
            }, `record_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>
<style>
.el-tooltip__popper {
    max-width: calc(100vw - 300px);
}
</style>
