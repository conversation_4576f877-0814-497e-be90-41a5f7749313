<template>
    <div class="app-container">
        <!-- <el-form
            :models="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-width="68px"
        >
            <el-form-item label="图片地址" prop="pictureUrl">
                <el-input
                    v-models="queryParams.pictureUrl"
                    placeholder="请输入图片地址"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="是否可用" prop="availability">
                <el-input
                    v-models="queryParams.availability"
                    placeholder="请输入是否可用  1代表可用 ，0代表不可用"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="排序" prop="sort">
                <el-input
                    v-models="queryParams.sort"
                    placeholder="请输入排序"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item>
                <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click="handleQuery"
                    >搜索</el-button
                >
                <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                    >重置</el-button
                >
            </el-form-item>
        </el-form> -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAdd"
                    v-hasPermi="['ai:carousel:add']"
                    >新增</el-button
                >
            </el-col>

            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['ai:carousel:remove']"
                    >删除</el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['ai:carousel:export']"
                    >导出</el-button
                >
            </el-col>
            <right-toolbar
                :showSearch.sync="showSearch"
                @queryTable="getList"
            ></right-toolbar>
        </el-row>

        <el-table
            v-loading="loading"
            :data="carouselList"
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="图片" align="center" prop="pictureUrl">
                <template slot-scope="scope">
                    <el-image
                        :src="scope.row.pictureUrl"
                        style="height: 105px; width: 210px"
                    ></el-image>
                </template>
            </el-table-column>
            <el-table-column
                label="是否上线"
                align="center"
                prop="availability"
            >
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.availability == 1" type="success"
                        >是</el-tag
                    >
                    <el-tag v-else type="danger">否</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="图片顺序" align="center" prop="sort" />
            <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
            >
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['ai:carousel:edit']"
                        >修改</el-button
                    >
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['ai:carousel:remove']"
                        >删除</el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改AZX轮播图保存类对话框 -->
        <el-dialog
            :title="title"
            :visible.sync="open"
            width="500px"
            append-to-body
            destroy-on-close
        >
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="图片地址" prop="pictureUrl">
                    <el-input
                        v-model="form.pictureUrl"
                        placeholder="请输入图片地址"
                    />
                </el-form-item>
                <el-form-item label="是否上线" prop="availability">
                    <el-radio-group v-model="form.availability">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                    </el-radio-group>
                    <!-- <el-input
                        v-models="form.availability"
                        placeholder="请输入是否可用  1代表可用 ，0代表不可用"
                    /> -->
                </el-form-item>
                <el-form-item>
                    <Image-Upload @input="handleInput"></Image-Upload>
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input v-model="form.sort" placeholder="请输入排序" />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    listCarousel,
    delCarousel,
    addCarousel,
} from "@/api/carousel/carousel";

export default {
    name: "Carousel",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // AZX轮播图保存类表格数据
            carouselList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                pictureUrl: null,
                availability: null,
                sort: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {},
            inputData: "",
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询AZX轮播图保存类列表 */
        getList() {
            this.loading = true;
            listCarousel(this.queryParams).then((response) => {
                this.carouselList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
            console.log(this.form);
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                pictureUrl: null,
                availability: null,
                delFlag: null,
                sort: null,
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加AZX轮播图保存类";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            this.open = true;
            // 解构赋值 row 不然默认值会丢失
            this.form = { ...row };
            this.title = "修改AZX轮播图保存类";
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    if (this.form.id != null) {
                        addCarousel(this.form).then((response) => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addCarousel(this.form).then((response) => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            console.log(row.id, this.ids);
            let ids = "";
            if (row.id) {
                ids = [row.id];
            } else if (this.ids) {
                ids = this.ids;
            }
            this.$modal
                .confirm(
                    '是否确认删除AZX轮播图保存类编号为"' + ids + '"的数据项？'
                )
                .then(function () {
                    return delCarousel(ids);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess("删除成功");
                })
                .catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download(
                "ai/carousel/export",
                {
                    ...this.queryParams,
                },
                `carousel_${new Date().getTime()}.xlsx`
            );
        },
        handleInput(data) {
            this.form.pictureUrl = data;
        },
    },
};
</script>
