<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="100px">
            <el-form-item label="用户id" prop="userId">
                <el-input
                    v-model="queryParams.userId"
                    placeholder="请输入用户id"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="用户手机号" prop="phone">
                <el-input
                    v-model="queryParams.phone"
                    placeholder="请输入用户手机号"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="会员开始时间" prop="startDate">
                <el-date-picker clearable
                                v-model="queryParams.startDate"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="请选择会员开始时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="会员结束时间" prop="endDate">
                <el-date-picker clearable
                                v-model="queryParams.endDate"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="请选择会员结束时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAdd"
                    v-hasPermi="['ai:membership:add']"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="el-icon-edit"
                    size="mini"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['ai:membership:edit']"
                >修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['ai:membership:remove']"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['ai:membership:export']"
                >导出
                </el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="membershipList" @selection-change="handleSelectionChange"
                  @sort-change="handleSortChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="主键" align="center" prop="id" :sort-orders="['descending','ascending']"
                             sortable="custom"/>
            <el-table-column label="用户id" align="center" prop="userId"/>
            <el-table-column label="用户手机号" align="center" prop="phone"/>
            <el-table-column label="会员开始时间" align="center" prop="startDate" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="会员结束时间" align="center" prop="endDate" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="会员类型[【0:月会会员,1:年会会员】" align="center" prop="type"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['ai:membership:edit']"
                    >修改
                    </el-button>
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['ai:membership:remove']"
                        style="margin-right: 12px"
                    >删除
                    </el-button>
                    <el-popover
                        placement="left"
                        :width="60"
                        trigger="click"
                        :content="freeTimes"
                    >
                        <template #reference>
                            <el-button size="mini"
                                       type="text"
                                       @click="handleSee(scope.row)"
                                       icon="el-icon-view">
                                查看次数
                            </el-button>
                        </template>
                    </el-popover>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改会员对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="100px">
                <el-form-item label="用户id" prop="userId">
                    <el-input v-model="form.userId" placeholder="请输入用户id"/>
                </el-form-item>
                <el-form-item label="用户手机号" prop="phone">
                    <el-input v-model="form.phone" placeholder="请输入用户手机号"/>
                </el-form-item>
                <el-form-item label="会员开始时间" prop="startDate">
                    <el-date-picker clearable
                                    v-model="form.startDate"
                                    type="date"
                                    value-format="yyyy-MM-dd"
                                    placeholder="请选择会员开始时间">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="会员结束时间" prop="endDate">
                    <el-date-picker clearable
                                    v-model="form.endDate"
                                    type="date"
                                    value-format="yyyy-MM-dd"
                                    placeholder="请选择会员结束时间">
                    </el-date-picker>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {listMembership, getMembership, delMembership, addMembership, updateMembership} from "@/api/membership/index";
import request from '@/utils/request'

export default {
    name: "Membership",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 会员表格数据
            membershipList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                userId: null,
                phone: null,
                startDate: null,
                endDate: null,
                type: null
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {},
            freeTimes: '', // 免费次数提示内容
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询会员列表 */
        getList() {
            this.loading = true;
            let params = {
                ...this.queryParams
            }

            if (!params.orderByColumn) {
                params.orderByColumn = 'id';
                params.isAsc = 'descending'
            }

            listMembership(params).then(response => {
                this.membershipList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                userId: null,
                phone: null,
                startDate: null,
                endDate: null,
                createTime: null,
                type: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加会员";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            getMembership(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改会员";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        updateMembership(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addMembership(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        handleSortChange(column) {
            this.queryParams.orderByColumn = column.prop;//查询字段是表格中字段名字
            this.queryParams.isAsc = column.order;//动态取值排序顺序
            console.log(this.queryParams);
            this.getList();
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal.confirm('是否确认删除会员编号为"' + ids + '"的数据项？').then(function () {
                return delMembership(ids);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('ai/membership/export', {
                ...this.queryParams
            }, `membership_${new Date().getTime()}.xlsx`)
        },
        handleSee(row) {
            const userId = row.userId;
            this.freeTimes = '';

            request({
                url: '/product/membership/getFreeCountByUserId',
                method: 'get',
                params: {
                    userId: userId
                }
            }).then(response => {
                console.log('免费次数', response)
                if (response.code === 200) {
                    this.freeTimes = response.data >= 0 ? '剩余' + response.data + '次' : '未查询到数据';
                    console.log(' this.freeTimes', this.freeTimes)
                }
            }).catch(e => {
                this.freeTimes = '未查询到数据';
            })
        }
    }
};
</script>
