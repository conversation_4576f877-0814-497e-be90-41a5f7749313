<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px">
            <el-form-item label="素材名称" prop="name">
                <el-input
                    v-model="queryParams.name"
                    placeholder="请输入素材名称"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="素材类型" prop="materialType">
                <el-select v-model="queryParams.materialType" placeholder="请选择素材类型" clearable>
                    <el-option
                        v-for="dict in dict.type.material_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="示例名称" prop="material">
                <el-input
                    v-model="queryParams.material"
                    placeholder="请输入示例名称"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="排序" prop="sort">
                <el-input
                    v-model="queryParams.sort"
                    placeholder="请输入排序"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAdd"
                    v-hasPermi="['ai:lib:add']"
                >新增
                </el-button>
            </el-col>
            <!--            <el-col :span="1.5">-->
            <!--                <el-button-->
            <!--                    type="success"-->
            <!--                    plain-->
            <!--                    icon="el-icon-edit"-->
            <!--                    size="mini"-->
            <!--                    :disabled="single"-->
            <!--                    @click="handleUpdate"-->
            <!--                    v-hasPermi="['ai:lib:edit']"-->
            <!--                >修改-->
            <!--                </el-button>-->
            <!--            </el-col>-->
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['ai:lib:remove']"
                >删除
                </el-button>
            </el-col>
            <!--            <el-col :span="1.5">-->
            <!--                <el-button-->
            <!--                    type="warning"-->
            <!--                    plain-->
            <!--                    icon="el-icon-download"-->
            <!--                    size="mini"-->
            <!--                    @click="handleExport"-->
            <!--                    v-hasPermi="['ai:lib:export']"-->
            <!--                >导出-->
            <!--                </el-button>-->
            <!--            </el-col>-->
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="libList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="主键" align="center" prop="id"/>
            <el-table-column label="素材名称" align="center" prop="name"/>
            <el-table-column label="素材类型" align="center" prop="materialType">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.material_type" :value="scope.row.materialType"/>
                </template>
            </el-table-column>
            <el-table-column label="示例名称" align="center" prop="material"/>
            <el-table-column label="上传示例" align="center" prop="picture"/>
            <el-table-column label="排序" align="center" prop="sort"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <!--                    <el-button-->
                    <!--                        size="mini"-->
                    <!--                        type="text"-->
                    <!--                        icon="el-icon-edit"-->
                    <!--                        @click="handleUpdate(scope.row)"-->
                    <!--                        v-hasPermi="['ai:lib:edit']"-->
                    <!--                    >修改-->
                    <!--                    </el-button>-->
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['ai:lib:remove']"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改素材管理对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false" destroy-on-close>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="素材名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入素材名称"/>
                </el-form-item>
                <el-form-item label="素材类型" prop="materialType">
                    <el-select v-model="form.materialType" placeholder="请选择素材类型">
                        <el-option
                            v-for="dict in dict.type.material_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="parseInt(dict.value)"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="示例名称" prop="material">
                    <el-input v-model="form.material" placeholder="请输入示例名称"/>
                </el-form-item>
                <el-form-item label="上传示例" prop="picture">
                    <!--<file-upload v-models="form.picture"/>-->
                    <el-button type="primary" size="mini" @click="toUploadVideo">上传文件</el-button>
                </el-form-item>
                <el-form-item v-if="form.picture">
                    <video class="_video" controls>
                        <source :src="form.picture + '?timestamp=' + timestamp">
                    </video>
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input v-model="form.sort" placeholder="请输入排序"/>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>

        <!--上传视频的按钮-->
        <input type="file" v-show="false" ref="uploadVideo" @change="handleVideo" accept="video/*"/>
    </div>
</template>

<script>
import {listLib, getLib, delLib, addLib, updateLib} from "@/api/api_hub/material";
import {DelDrivingExamples, PostDrivingExamples, uploadResources} from "@/api/api_hub/ai-ajax";
import {uploadChange} from "@/api/api_hub/uploadFileCos";

export default {
    name: "Lib",
    dicts: ['material_type'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 素材管理表格数据
            libList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                name: null,
                materialType: null,
                material: null,
                picture: null,
                sort: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                name: [
                    {required: true, message: '请输入素材名称', trigger: 'blur'},
                ],
                materialType: [
                    {required: true, message: '请输入素材名称', trigger: 'blur'},
                ],
                material: [
                    {required: true, message: '请输入示例名称', trigger: 'blur'},
                    {min: 2, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur'},
                    {validator: this.isAccountValid, trigger: 'blur'}
                ],
                picture: [
                    {required: true, message: '请上传示例', trigger: 'blur'},
                ]
            },
            timestamp: ''
        };
    },
    created() {

        this.getList();
    },
    methods: {
        /** 查询素材管理列表 */
        getList() {
            this.loading = true;
            listLib(this.queryParams).then(response => {
                this.libList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                name: null,
                materialType: null,
                material: null,
                picture: null,
                sort: null,
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加素材管理";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            getLib(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改素材管理";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        updateLib(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addLib(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal.confirm('是否确认删除素材管理编号为"' + ids + '"的数据项？').then(function () {
                return delLib(ids).then(response => {
                    console.log('删除结果', response)
                    if (response.code === 200) {
                        DelDrivingExamples(row.material + '.mp4').then(eData => {
                            console.log('删除结果2', eData)
                        })
                    }
                });
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('ai/lib/export', {
                ...this.queryParams
            }, `lib_${new Date().getTime()}.xlsx`)
        },
        isAccountValid(rule, value, callback) {
            if (!value) {
                return;
            }
            const regex = /^[A-Za-z][A-Za-z0-9]*$/;
            if (!regex.test(value)) {
                callback(new Error("第一个字符必须是字母，后面可以是字母、数字，总长度为3-20"));
            } else {
                callback();
            }
        },
        toUploadVideo() {
            if (this.form.material) {
                this.$refs.uploadVideo.value = '';
                this.$refs.uploadVideo.click();
            } else {
                this.$message({message: '先输入素材示例名称', type: 'error'})
            }
        },
        handleVideo(e) { // 只能上传MP4
            const file = e.target.files[0];
            const allowedVideoTypes = ['video/mp4'];
            console.log('file', file)

            if (allowedVideoTypes.includes(file.type)) {
                this.form.picture = ''
                const loading = this.$loading({
                    lock: true,
                    text: '上传中...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
                PostDrivingExamples({
                    fileName: this.form.material + '.mp4',
                    file: file, // 文件
                    success: (res) => {
                        console.log('上传结果', res)
                        if (res.code === 200) {
                            uploadChange({
                                files: e.target.files,
                                folder: 'material', // 文件路径
                                fileName: res.data
                            }).then(response => {
                                console.log('上传到腾讯云结果', response)

                                if (response['data'].statusCode === 200) {
                                    this.form.picture = 'https://' + response['data']['Location'];
                                    this.timestamp = Date.now();
                                    console.log('this.form.picture', this.form.picture)
                                }

                                loading.close();
                            })
                        }
                    },
                    fail: (error) => {
                        console.log('上传失败', error)
                        loading.close();
                    }
                })

            } else {
                // 不是视频，提示用户
                this.$message({message: '只能上传mp4格式的文件', type: 'error'})
            }
        },

    }
};
</script>

<style lang="scss">
._video {
    width: 100%;
    height: 150px;
}
</style>
