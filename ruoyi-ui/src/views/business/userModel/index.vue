<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px">
            <el-form-item label="用户id" prop="userId">
                <el-input
                    v-model="queryParams.userId"
                    placeholder="请输入用户id"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="模型类型" prop="type">
                <el-select v-model="queryParams.type" placeholder="请选择模型类型" clearable>
                    <el-option
                        v-for="dict in dict.type.user_model_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="模型名称" prop="modelName">
                <el-input
                    v-model="queryParams.modelName"
                    placeholder="请输入模型名称"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAdd"
                    v-hasPermi="['api_hub:models:add']"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="el-icon-edit"
                    size="mini"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['api_hub:models:edit']"
                >修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['api_hub:models:remove']"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['api_hub:models:export']"
                >导出
                </el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="modelList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="主键" align="center" prop="id"/>
            <el-table-column label="用户id" align="center" prop="userId"/>
            <el-table-column label="模型类型" align="center" prop="type">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.user_model_type" :value="scope.row.type"/>
                </template>
            </el-table-column>
            <el-table-column label="模型名称" align="center" prop="modelName"/>
            <el-table-column label="状态" align="center" prop="status"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['api_hub:models:edit']"
                    >修改
                    </el-button>
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['api_hub:models:remove']"
                    >删除
                    </el-button>
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-s-tools"
                        @click="handleDeleteTask(scope.row)"
                        v-hasPermi="['api_hub:models:remove']"
                    >查看任务配置
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改用户模型对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false" destroy-on-close>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="用户id" prop="userId">
                    <el-input v-model="form.userId" placeholder="请输入用户id"/>
                </el-form-item>
                <el-form-item label="模型类型" prop="type">
                    <el-select v-model="form.type" placeholder="请选择模型类型">
                        <el-option
                            v-for="dict in dict.type.user_model_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="parseInt(dict.value)"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="模型名称" prop="modelName">
                    <el-input v-model="form.modelName" placeholder="请输入模型名称"/>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>

        <!-- 添加或修改用户模型对话框 -->
        <el-dialog title="任务配置" :visible.sync="openTask" width="900px" append-to-body>
            <el-button type="primary" class="sxpz" size="small" icon="el-icon-refresh" @click="refreshConfig">刷新配置
            </el-button>
            <el-tabs type="border-card" @tab-click="tabClick" v-model="tabVal">
                <el-tab-pane label="图片修复" name="apps.app_service.revive_image">
                    <el-table
                        :data="tableData"
                        style="width: 100%">
                        <el-table-column
                            prop="describe"
                            label="名称">
                        </el-table-column>
                        <el-table-column
                            prop="start_date"
                            label="开始时间"
                            width="220">
                        </el-table-column>
                        <el-table-column
                            prop="end_date"
                            width="220"
                            label="结束时间">
                        </el-table-column>
                        <el-table-column
                            prop="status"
                            label="状态">
                        </el-table-column>
                        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                            <template slot-scope="scope">
                                <el-button
                                    size="mini"
                                    type="text"
                                    v-if="scope.$index == 0"
                                    icon="el-icon-delete"
                                    @click="handleTaskDelete(scope.row)"
                                    v-hasPermi="['api_hub:models:remove']"
                                >删除当前任务
                                </el-button>
                                <el-button
                                    size="mini"
                                    type="text"
                                    v-if="scope.$index == 0 && taskStatus === 'FAILURE'"
                                    icon="el-icon-refresh"
                                    @click="handleRedo(scope.row)"
                                    v-hasPermi="['api_hub:models:remove']"
                                >重新提交
                                </el-button>
                                <el-button
                                    size="mini"
                                    type="text"
                                    v-if="scope.row.status === 'FAILURE'"
                                    @click="handleError(scope.row)"
                                >查看错误
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                <el-tab-pane label="视频修复" name="apps.app_service.revive_video">
                    <el-table
                        :data="tableData"
                        style="width: 100%">
                        <el-table-column
                            prop="describe"
                            label="名称">
                        </el-table-column>
                        <el-table-column
                            prop="start_date"
                            label="开始时间"
                            width="220">
                        </el-table-column>
                        <el-table-column
                            prop="end_date"
                            width="220"
                            label="结束时间">
                        </el-table-column>
                        <el-table-column
                            prop="status"
                            label="状态">
                        </el-table-column>
                        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                            <template slot-scope="scope">
                                <el-button
                                    size="mini"
                                    v-if="scope.$index == 0"
                                    type="text"
                                    icon="el-icon-delete"
                                    @click="handleTaskDelete(scope.row)"
                                >删除当前任务
                                </el-button>
                                <el-button
                                    size="mini"
                                    type="text"
                                    v-if="scope.$index == 0 && taskStatus === 'FAILURE'"
                                    icon="el-icon-refresh"
                                    @click="handleRedo(scope.row)"
                                >重新提交
                                </el-button>
                                <el-button
                                    size="mini"
                                    type="text"
                                    v-if="scope.row.status === 'FAILURE'"
                                    @click="handleError(scope.row)"
                                >查看错误
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                <el-tab-pane label="动漫转图片" name="apps.app_service.anime_to_real">
                    <el-table
                        :data="tableData"
                        style="width: 100%">
                        <el-table-column
                            prop="describe"
                            label="名称">
                        </el-table-column>
                        <el-table-column
                            prop="start_date"
                            label="开始时间"
                            width="220">
                        </el-table-column>
                        <el-table-column
                            prop="end_date"
                            width="220"
                            label="结束时间">
                        </el-table-column>
                        <el-table-column
                            prop="status"
                            label="状态">
                        </el-table-column>
                        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                            <template slot-scope="scope">
                                <el-button
                                    size="mini"
                                    v-if="scope.$index == 0"
                                    type="text"
                                    icon="el-icon-delete"
                                    @click="handleTaskDelete(scope.row)"
                                    v-hasPermi="['api_hub:models:remove']"
                                >删除当前任务
                                </el-button>
                                <el-button
                                    size="mini"
                                    type="text"
                                    v-if="scope.$index == 0 && taskStatus === 'FAILURE'"
                                    icon="el-icon-refresh"
                                    @click="handleRedo(scope.row)"
                                    v-hasPermi="['api_hub:models:remove']"
                                >重新提交
                                </el-button>
                                <el-button
                                    size="mini"
                                    type="text"
                                    v-if="scope.row.status === 'FAILURE'"
                                    @click="handleError(scope.row)"
                                >查看错误
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                <el-tab-pane label="图片转动漫" name="apps.app_service.real_to_anime">
                    <el-table
                        :data="tableData"
                        style="width: 100%">
                        <el-table-column
                            prop="describe"
                            label="名称">
                        </el-table-column>
                        <el-table-column
                            prop="start_date"
                            label="开始时间"
                            width="220">
                        </el-table-column>
                        <el-table-column
                            prop="end_date"
                            width="220"
                            label="结束时间">
                        </el-table-column>
                        <el-table-column
                            prop="status"
                            label="状态">
                        </el-table-column>
                        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                            <template slot-scope="scope">
                                <el-button
                                    size="mini"
                                    v-if="scope.$index == 0"
                                    type="text"
                                    icon="el-icon-delete"
                                    @click="handleTaskDelete(scope.row)"
                                    v-hasPermi="['api_hub:models:remove']"
                                >删除当前任务
                                </el-button>
                                <el-button
                                    size="mini"
                                    type="text"
                                    v-if="scope.$index == 0"
                                    icon="el-icon-refresh"
                                    @click="handleRedo(scope.row)"
                                    v-hasPermi="['api_hub:models:remove']"
                                >重新提交
                                </el-button>
                                <el-button
                                    size="mini"
                                    type="text"
                                    v-if="scope.row.status === 'FAILURE'"
                                    @click="handleError(scope.row)"
                                >查看错误
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
            </el-tabs>
            <div slot="footer" class="dialog-footer">
                <el-button @click="openTask = false">关 闭</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {listModel2, getModel, delModel, addModel, updateModel} from "@/api/api_hub/userModel";
import {deleteTaskConfig, taskConfig} from "@/api/api_hub/ai-ajax";
import request from '@/utils/request'
import {taskRecord} from "@/api/invoke/record";

export default {
    name: "Model",
    dicts: ['user_model_type'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 用户模型表格数据
            modelList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                userId: null,
                type: null,
                modelName: null,
                status: null
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {},
            openTask: false,
            taskInfo: {},
            modelName: '',
            tabVal: 'apps.app_service.revive_image',
            tableData: [],
            taskStatus: '' // 当前任务的状态
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询用户模型列表 */
        getList() {
            this.loading = true;
            listModel2(this.queryParams).then(response => {
                this.modelList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                userId: null,
                type: null,
                modelName: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null,
                status: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加用户模型";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            getModel(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改用户模型";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        updateModel(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addModel(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal.confirm('是否确认删除用户模型编号为"' + ids + '"的数据项？').then(function () {
                return delModel(ids);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('api_hub/models/export', {
                ...this.queryParams
            }, `model_${new Date().getTime()}.xlsx`)
        },
        handleDeleteTask(row) {
            let modelName = this.modelName = row.modelName;
            // this.openTask = true;

            taskConfig(modelName).then(response => {
                // console.log('任务配置', response)
                this.taskInfo = response.data || {};
                this.tabVal = 'apps.app_service.revive_image';
                this.tableData = this.taskInfo[this.tabVal] ? this.taskInfo[this.tabVal]['steps'] : [];
                this.taskStatus = this.taskInfo[this.tabVal] ? this.taskInfo[this.tabVal]['status'] : '';
                this.openTask = true;
            })
        },
        tabClick(e) {
            this.tableData = this.taskInfo[this.tabVal] ? this.taskInfo[this.tabVal]['steps'] : [];
            this.taskStatus = this.taskInfo[this.tabVal] ? this.taskInfo[this.tabVal]['status'] : '';

            if (this.taskInfo && this.taskInfo[this.tabVal] && this.taskInfo[this.tabVal]['task_id'] && !this.taskInfo[this.tabVal]['steps'] && this.taskStatus === 'WAIT') {
                this.tableData = [{
                    describe: '等待中',
                    task_id: this.taskInfo[this.tabVal]['task_id'],
                    status: "WAIT"
                }]
            }
        },
        handleTaskDelete() {
            this.$modal.confirm('是否确认删除当前的任务项').then(() => {
                return deleteTaskConfig(this.modelName, {name: this.tabVal})
            }).then(() => {
                this.$modal.msgSuccess("删除成功");

                // 刷新数据
                taskConfig(this.modelName).then(response => {
                    // console.log('任务配置', response)
                    this.taskInfo = response.data || {};
                    this.tableData = this.taskInfo[this.tabVal] ? this.taskInfo[this.tabVal]['steps'] : [];
                    this.taskStatus = this.taskInfo[this.tabVal] ? this.taskInfo[this.tabVal]['status'] : '';
                })

            }).catch((e) => {
                // console.log(e)
            });
        },
        handleRedo() { // 重新制作
            let taskInfo = this.taskInfo[this.tabVal] || {};
            // console.log('taskInfo', taskInfo)
            let task_id = taskInfo.task_id;

            taskRecord(task_id).then(response => {
                // console.log('最后一次提交记录', response)
                let item = response.data;
                if (item) {
                    // 重新制作
                    const AiPath = '/interface/info/invoke';
                    // 制作失败以后重新提交
                    request({
                        url: AiPath + item.path,
                        method: 'post',
                        data: JSON.parse(item.invokeParameter),
                    }).then(response => {
                        // console.log('重新提交的结果', response)
                        if (response['code'] === 200) {
                            this.$message({message: '提交成功，请稍后', type: 'success'});

                            // 刷新数据
                            taskConfig(this.modelName).then(response => {
                                // console.log('任务配置', response)
                                this.taskInfo = response.data || {};
                                this.tableData = this.taskInfo[this.tabVal] ? this.taskInfo[this.tabVal]['steps'] : [];
                                this.taskStatus = this.taskInfo[this.tabVal] ? this.taskInfo[this.tabVal]['status'] : '';
                            })
                        }
                    })
                }
            })
        },
        handleError(item) {
            // 查看错误
            let taskInfo = this.taskInfo[this.tabVal] || {};
            // console.log('taskInfo', taskInfo)
            alert(taskInfo.error)
        },
        refreshConfig() { // 刷新配置
            taskConfig(this.modelName).then(response => {
                // console.log('任务配置', response)
                this.taskInfo = response.data || {};
                this.tabVal = 'apps.app_service.revive_image';
                this.tableData = this.taskInfo[this.tabVal] ? this.taskInfo[this.tabVal]['steps'] : [];
                this.taskStatus = this.taskInfo[this.tabVal] ? this.taskInfo[this.tabVal]['status'] : '';
                this.$message({message: '刷新成功', type: 'success'});
            })
        }
    }
};
</script>

<style>
.el-dialog__body {
    padding-top: 10px !important;
}

.sxpz {
    margin-top: -20px !important;
    margin-bottom: 10px !important;
}
</style>
