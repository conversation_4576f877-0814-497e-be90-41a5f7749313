<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="80px">
            <el-form-item label="用户名称" prop="userName">
                <el-input
                    v-model="queryParams.userName"
                    placeholder="请输入用户名称"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" style="width: 100%">
                    <el-option label="全部" :value="null"></el-option>
                    <el-option
                        v-for="dict in dict.type.user_verification_status"
                        :key="dict.value"
                        :label="dict.label"
                        :value="parseInt(dict.value)"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="手机号码" prop="phoneNum">
                <el-input
                    v-model="queryParams.phoneNum"
                    placeholder="请输入手机号码"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['dev:verification:remove']"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['dev:verification:export']"
                >导出
                </el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="verificationList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="主键" align="center" prop="id" width="200" show-overflow-tooltip/>
            <el-table-column label="用户名称" align="center" prop="userName" width="200" show-overflow-tooltip/>
            <el-table-column label="身份证姓名" align="center" prop="idName" width="200" show-overflow-tooltip/>
            <el-table-column label="身份证号码" align="center" prop="idNum" width="200" show-overflow-tooltip/>
            <el-table-column label="手机号码" align="center" prop="phoneNum" width="200" show-overflow-tooltip/>
            <el-table-column label="邮箱号" align="center" prop="email" width="200" show-overflow-tooltip/>
            <el-table-column label="证明材料" align="center" prop="credential" width="200" show-overflow-tooltip>
                <template slot-scope="scope">
                    <image-preview :src="scope.row.credential" :width="30" :height="30"/>
                </template>
            </el-table-column>
            <el-table-column label="银行名称" align="center" prop="bankName" width="200" show-overflow-tooltip/>
            <el-table-column label="银行卡号" align="center" prop="bankNum" width="200" show-overflow-tooltip/>
            <el-table-column label="状态" align="center" prop="status" width="200"
                             show-overflow-tooltip>
                <template #default="scope">
                    <dict-tag :options="dict.type.user_verification_status" :value="scope.row.status"/>
                </template>
            </el-table-column>
            <el-table-column label="支付状态" align="center" prop="payStatus" width="200" show-overflow-tooltip>
                <template #default="scope">
                    <dict-tag :options="dict.type.order_status" :value="scope.row.payStatus"/>
                </template>
            </el-table-column>
            <el-table-column label="支付时间" align="center" prop="payTime" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.payTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="申请时间" align="center" prop="createTime" width="180"/>
            <el-table-column label="更新时间" align="center" prop="updateTime" width="180"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right"
                             width="200">
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['dev:verification:edit']"
                    >详情
                    </el-button>
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['dev:verification:remove']"
                    >删除
                    </el-button>
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        v-if="scope.row.status === 0"
                        @click="handleAuth(scope.row)"
                    >审核
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改开发者认证对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px" disabled>
                <el-form-item label="用户名称" prop="userName">
                    <el-input v-model="form.userName" placeholder="请输入用户名称"/>
                </el-form-item>
                <el-form-item label="身份证姓名" prop="idName">
                    <el-input v-model="form.idName" placeholder="请输入身份证姓名"/>
                </el-form-item>
                <el-form-item label="身份证号码" prop="idNum">
                    <el-input v-model="form.idNum" placeholder="请输入身份证号码"/>
                </el-form-item>
                <el-form-item label="手机号码" prop="phoneNum">
                    <el-input v-model="form.phoneNum" placeholder="请输入手机号码"/>
                </el-form-item>
                <el-form-item label="邮箱号" prop="email">
                    <el-input v-model="form.email" placeholder="请输入邮箱号"/>
                </el-form-item>
                <el-form-item label="证明材料" prop="credential">
                    <el-input v-model="form.credential" type="textarea" placeholder="请输入内容"/>
                </el-form-item>
                <el-form-item label="银行名称" prop="bankName">
                    <el-input v-model="form.bankName" placeholder="请输入银行名称"/>
                </el-form-item>
                <el-form-item label="银行卡号" prop="bankNum">
                    <el-input v-model="form.bankNum" placeholder="请输入银行卡号"/>
                </el-form-item>
                <el-form-item label="支付时间" prop="payTime">
                    <el-date-picker clearable
                                    v-model="form.payTime"
                                    type="date"
                                    value-format="yyyy-MM-dd"
                                    placeholder="请选择支付时间">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="审核备注" prop="auditReason">
                    <el-input v-model="form.auditReason" placeholder="请输入审核备注"/>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <!--                <el-button type="primary" @click="submitForm">确 定</el-button>-->
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>

        <!--   审核弹窗     -->
        <el-dialog title="审核意见" :visible.sync="dialogVisible" width="50%" :close-on-click-modal="false"
                   :destroy-on-close="true">
            <el-form :model="authForm" label-width="100px">
                <el-form-item label="审核意见" prop="auditReason">
                    <el-input v-model="authForm.auditReason" autocomplete="off" type="textarea" :rows="5"></el-input>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="authForm.status">
                        <el-radio :label="1">通过</el-radio>
                        <el-radio :label="2">拒绝</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="handleAuthSubmit">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    listVerification,
    getVerification,
    delVerification,
    addVerification,
    updateVerification
} from "@/api/agent/dev-auth";
import {devExamine} from "../../../api/web";

export default {
    name: "Verification",
    dicts: ['user_verification_status', 'order_status'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 开发者认证表格数据
            verificationList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                userId: null,
                userName: null,
                idName: null,
                idNum: null,
                phoneNum: null,
                email: null,
                credential: null,
                bankName: null,
                bankNum: null,
                status: null,
                payStatus: null,
                payTime: null,
                auditReason: null,
                orderByColumn: 'updateTime',
                isAsc: 'desc'
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {},

            // 审核相关
            dialogVisible: false,
            authForm: {
                ids: [],
                auditReason: '',
                status: ""
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询开发者认证列表 */
        getList() {
            this.loading = true;
            listVerification(this.queryParams).then(response => {
                this.verificationList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                userId: null,
                userName: null,
                idName: null,
                idNum: null,
                phoneNum: null,
                email: null,
                credential: null,
                bankName: null,
                bankNum: null,
                status: null,
                createTime: null,
                updateTime: null,
                payStatus: null,
                payTime: null,
                auditReason: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加开发者认证";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            getVerification(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "开发者认证详情";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        updateVerification(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addVerification(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal.confirm('是否确认删除开发者认证编号为"' + ids + '"的数据项？').then(function () {
                return delVerification(ids);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('dev/verification/export', {
                ...this.queryParams
            }, `verification_${new Date().getTime()}.xlsx`)
        },
        handleAuth(item) {
            this.dialogVisible = true;
            this.authForm.auditReason = ''
            this.authForm.status = ''
            this.authForm.ids = [item.id];
        },
        handleAuthSubmit() {
            if (!this.authForm.status) {
                return this.$message.error('请填写审核结果')
            }
            if (this.authForm.status === 2 && !this.authForm.auditReason) {
                return this.$message.error('请填写审核意见')
            }

            devExamine(this.authForm).then(response => {
                this.$modal.msgSuccess("操作成功");
                this.dialogVisible = false;
                this.getList();
            });
        }
    }
};
</script>
