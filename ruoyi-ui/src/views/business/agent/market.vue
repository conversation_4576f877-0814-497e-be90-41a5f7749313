<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px">
            <el-form-item label="市场名称" prop="name">
                <el-input
                    v-model="queryParams.name"
                    placeholder="请输入市场名称"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="市场分类" prop="categoryId">
                <el-input
                    v-model="queryParams.categoryId"
                    placeholder="请输入市场分类"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="发布者" prop="userName">
                <el-input
                    v-model="queryParams.userName"
                    placeholder="请输入发布者"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" style="width: 100%">
                    <el-option label="全部" :value="null"></el-option>
                    <el-option
                        v-for="dict in dict.type.agent_status"
                        :key="dict.value"
                        :label="dict.label"
                        :value="parseInt(dict.value)"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAdd"
                    v-hasPermi="['agent:market:add']"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="el-icon-edit"
                    size="mini"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['agent:market:edit']"
                >修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['agent:market:remove']"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['agent:market:export']"
                >导出
                </el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="marketList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"/>
            <!--            <el-table-column label="应用市场id" align="center" prop="id"/>-->
            <el-table-column label="发布者" align="center" prop="userName" width="150" show-overflow-tooltip/>
            <el-table-column label="市场名称" align="center" prop="name" width="150" show-overflow-tooltip/>
            <el-table-column label="市场描述" align="center" prop="description" width="200" show-overflow-tooltip/>
            <el-table-column label="详细信息" align="center" prop="info" width="200" show-overflow-tooltip/>
            <el-table-column label="市场分类" align="center" prop="categoryName" width="200" show-overflow-tooltip/>
            <el-table-column label="发布地址" align="center" prop="platformBaseUrl" width="200" show-overflow-tooltip/>
            <!--            <el-table-column label="关联商品id" align="center" prop="productId" width="200" show-overflow-tooltip/>-->
            <el-table-column label="封面" align="center" prop="img" width="200" show-overflow-tooltip>
                <template #default="scope">
                    <el-image :src="scope.row.img" style="width: 20px; height: 20px"></el-image>
                </template>
            </el-table-column>
            <el-table-column label="是否推荐" align="center" prop="recommend" width="200" show-overflow-tooltip>
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.is_recommend" :value="scope.row.recommend"/>
                </template>
            </el-table-column>
            <el-table-column label="排序" align="center" prop="sort" width="200" show-overflow-tooltip/>
            <el-table-column label="浏览量" align="center" prop="viewNum" width="200" show-overflow-tooltip/>
            <el-table-column label="使用量" align="center" prop="useNum" width="200" show-overflow-tooltip/>
            <el-table-column label="收藏量" align="center" prop="collectionNum" width="200" show-overflow-tooltip/>
            <el-table-column label="状态" align="center" prop="status" width="200" show-overflow-tooltip>
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.agent_status" :value="scope.row.status"/>
                </template>
            </el-table-column>
            <el-table-column label="版本号" align="center" prop="version" width="200" show-overflow-tooltip/>
            <el-table-column label="是否在线运行" align="center" prop="onLine" width="200"
                             show-overflow-tooltip/>
            <el-table-column label="是否支持MCP" align="center" prop="mcp" width="200" show-overflow-tooltip/>
            <el-table-column label="是否支持API" align="center" prop="api" width="200" show-overflow-tooltip/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200"
                             fixed="right">
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['agent:market:edit']"
                    >修改
                    </el-button>
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['agent:market:remove']"
                    >删除
                    </el-button>
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        v-if="scope.row.status === 3"
                        @click="handleAuth(scope.row)"
                    >审核
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改应用市场对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body top="5vh"
                   :close-on-click-modal="false" destroy-on-close>
            <el-scrollbar class="hide-scrollbar hide-scrollbar2">
                <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                    <el-form-item label="市场名称" prop="name">
                        <el-input v-model="form.name" placeholder="请输入市场名称"/>
                    </el-form-item>
                    <el-form-item label="封面" prop="img">
                        <image-upload v-model="form.img" :limit="1"/>
                    </el-form-item>
                    <el-form-item label="市场描述" prop="description">
                        <el-input v-model="form.description" type="textarea" placeholder="请输入内容"/>
                    </el-form-item>
                    <el-form-item label="市场分类" prop="categoryId">
                        <el-input v-model="form.categoryId" placeholder="请输入市场分类"/>
                    </el-form-item>
                    <el-form-item label="发布地址" prop="platformBaseUrl">
                        <el-input v-model="form.platformBaseUrl" placeholder="请输入发布地址"/>
                    </el-form-item>
                    <el-form-item label="关联商品id" prop="productId">
                        <el-input v-model="form.productId" placeholder="请输入关联商品id"/>
                    </el-form-item>
                    <el-form-item label="是否推荐" prop="recommend">
                        <el-radio-group v-model="form.recommend">
                            <el-radio
                                v-for="dict in dict.type.is_recommend"
                                :key="dict.value"
                                :label="parseInt(dict.value)"
                            >{{ dict.label }}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="排序" prop="sort">
                        <el-input v-model="form.sort" placeholder="请输入排序"/>
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
                        <el-select v-model="form.status" style="width: 100%">
                            <el-option
                                v-for="dict in dict.type.agent_status"
                                :key="dict.value"
                                :label="dict.label"
                                :value="parseInt(dict.value)"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="审核备注" prop="auditReason">
                        <el-input v-model="form.auditReason" placeholder="请输入审核备注"/>
                    </el-form-item>
                    <el-form-item label="发布者" prop="userName">
                        <el-input v-model="form.userName" placeholder="请输入发布者"/>
                    </el-form-item>
                    <el-form-item label="智能体id" prop="platformAgentId">
                        <el-input v-model="form.platformAgentId" placeholder="请输入智能体id"/>
                    </el-form-item>
                    <el-form-item label="智能体秘钥" prop="platformToken">
                        <el-input v-model="form.platformToken" placeholder="请输入智能体秘钥"/>
                    </el-form-item>
                    <el-form-item label="版本号" prop="version">
                        <el-input v-model="form.version" placeholder="请输入版本号"/>
                    </el-form-item>
                    <el-form-item label="收费标准" prop="charge">
                        <el-input v-model="form.charge" type="textarea" placeholder="请输入内容"/>
                    </el-form-item>
                    <el-form-item label="是否在线运行" prop="onLine">
                        <el-input v-model="form.onLine" placeholder="请输入是否在线运行"/>
                    </el-form-item>
                    <el-form-item label="在线运行说明" prop="onLineInfo">
                        <el-input v-model="form.onLineInfo" type="textarea" placeholder="请输入内容"/>
                    </el-form-item>
                    <el-form-item label="是否支持MCP" prop="mcp">
                        <el-input v-model="form.mcp" placeholder="请输入是否支持MCP"/>
                    </el-form-item>
                    <el-form-item label="MCP说明" prop="mcpInfo">
                        <el-input v-model="form.mcpInfo" type="textarea" placeholder="请输入内容"/>
                    </el-form-item>
                    <el-form-item label="是否支持API" prop="api">
                        <el-input v-model="form.api" placeholder="请输入是否支持API"/>
                    </el-form-item>
                    <el-form-item label="API说明" prop="apiInfo">
                        <el-input v-model="form.apiInfo" type="textarea" placeholder="请输入内容"/>
                    </el-form-item>
                    <el-form-item label="智能体市场说明" prop="info">
                        <el-input v-model="form.info" type="textarea" placeholder="请输入内容" :rows="50"/>
                        <!--                        <editor v-model="form.info" :min-height="192"/>-->
                    </el-form-item>
                </el-form>
            </el-scrollbar>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>

        <!--   审核弹窗     -->
        <el-dialog title="审核意见" :visible.sync="dialogVisible" width="50%" :close-on-click-modal="false"
                   :destroy-on-close="true">
            <el-form :model="authForm" label-width="100px">
                <el-form-item label="审核意见" prop="auditReason">
                    <el-input v-model="authForm.auditReason" autocomplete="off" type="textarea" :rows="5"></el-input>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="authForm.status">
                        <el-radio :label="1">通过</el-radio>
                        <el-radio :label="4">拒绝</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="handleAuthSubmit">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {listAgent, getAgent, delAgent, addAgent, updateAgent} from "@/api/agent/market";
import {authExamine} from "../../../api/web";

export default {
    name: "Agent",
    dicts: ['agent_status', 'is_recommend'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 应用市场表格数据
            marketList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                name: null,
                description: null,
                info: null,
                categoryId: null,
                platformBaseUrl: null,
                productId: null,
                img: null,
                recommend: null,
                sort: null,
                viewNum: null,
                useNum: null,
                collectionNum: null,
                status: null,
                auditReason: null,
                userName: null,
                platformAgentId: null,
                platformToken: null,
                version: null,
                charge: null,
                onLine: null,
                onLineInfo: null,
                mcp: null,
                mcpInfo: null,
                api: null,
                apiInfo: null,
                orderByColumn:'updateTime',
                isAsc:'desc'
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {},

            // 审核相关
            dialogVisible: false,
            authForm: {
                ids: [],
                auditReason: '',
                status: ""
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询应用市场列表 */
        getList() {
            this.loading = true;
            listAgent(this.queryParams).then(response => {
                this.marketList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                name: null,
                description: null,
                info: null,
                categoryId: null,
                platformBaseUrl: null,
                productId: null,
                img: 'https://ai-cloud-1313466856.cos.ap-guangzhou.myqcloud.com/_upload/71221593-4693-4afd-a99f-5e5c82a7a68e.png',
                recommend: null,
                sort: null,
                viewNum: null,
                useNum: null,
                collectionNum: null,
                createTime: null,
                updateTime: null,
                status: null,
                auditReason: null,
                userName: null,
                platformAgentId: null,
                platformToken: null,
                version: null,
                charge: null,
                onLine: null,
                onLineInfo: null,
                mcp: null,
                mcpInfo: null,
                api: null,
                apiInfo: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加应用市场";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            getAgent(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改应用市场";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        updateAgent(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addAgent(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal.confirm('是否确认删除应用市场编号为"' + ids + '"的数据项？').then(function () {
                return delAgent(ids);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('agent/market/export', {
                ...this.queryParams
            }, `market_${new Date().getTime()}.xlsx`)
        },
        handleAuth(item) {
            this.dialogVisible = true;
            this.authForm.auditReason = ''
            this.authForm.status = ''
            this.authForm.ids = [item.id];
        },
        handleAuthSubmit() {
            if (!this.authForm.status) {
                return this.$message.error('请填写审核结果')
            }
            if (this.authForm.status === 4 && !this.authForm.auditReason) {
                return this.$message.error('请填写审核意见')
            }

            authExamine(this.authForm).then(response => {
                this.$modal.msgSuccess("操作成功");
                this.dialogVisible = false;
                this.getList();
            });
        }
    }
};
</script>
<style lang="scss">
.hide-scrollbar2 {
    width: 100%;
    height: calc(90vh - 200px);
    overflow-x: hidden !important;
}
</style>
