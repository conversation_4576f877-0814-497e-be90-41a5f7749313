<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px">
            <el-form-item label="订单号" prop="orderNum">
                <el-input
                    v-model="queryParams.orderNum"
                    placeholder="请输入订单号"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="手机号" prop="createByPhoneNumber">
                <el-input
                    v-model="queryParams.createByPhoneNumber"
                    placeholder="请输入手机号"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="支付状态" prop="status">
                <el-select v-model="queryParams.status">
                    <el-option label="全部" value=""></el-option>
                    <template v-for="(item,itemIndex) in dict.type.order_status">
                        <el-option :label="item.label" :value="item.value"></el-option>
                    </template>
                </el-select>
            </el-form-item>
            <el-form-item label="支付时间">
                <el-date-picker
                    v-model="daterangePaymentTime"
                    style="width: 240px"
                    value-format="yyyy-MM-dd"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                ></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAdd"
                    v-hasPermi="['product:order:add']"
                >新增
                </el-button>
            </el-col>
            <!--<el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="el-icon-edit"
                    size="mini"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['product:order:edit']"
                >修改</el-button>
            </el-col>-->
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['product:order:remove']"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['product:order:export']"
                >导出
                </el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange"
                  @sort-change="handleSortChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="订单id" align="center" prop="orderId"/>
            <el-table-column
                label="订单号"
                align="center"
                prop="orderNum"
                :show-overflow-tooltip="true"
                width="220"
            />
            <el-table-column label="订单总金额" align="center" prop="totalAmount" width="150"/>
            <el-table-column label="应付金额" align="center" prop="payAmount" width="150"/>
            <el-table-column label="支付类型" align="center" prop="payType" width="150">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.pay_type" :value="scope.row.payType"/>
                </template>
            </el-table-column>
            <el-table-column label="支付状态" align="center" prop="status" width="150">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.order_status" :value="scope.row.status"/>
                </template>
            </el-table-column>
            <el-table-column label="支付时间" align="center" prop="paymentTime" width="150">
                <template slot-scope="scope">
                    <span>{{ scope.row.paymentTime }}</span>
                </template>
            </el-table-column>
            <el-table-column
                label="支付凭证"
                align="center"
                prop="payOrder"
                :show-overflow-tooltip="true"
                width="150"
            />
            <el-table-column
                label="创建者"
                align="center"
                prop="createByName"
                :show-overflow-tooltip="true"
                width="150"
            />
            <el-table-column
                label="手机号"
                align="center"
                prop="createByPhoneNumber"
                :show-overflow-tooltip="true"
                width="150"
            />
            <el-table-column
                label="创建时间"
                align="center"
                prop="createTime"
                :show-overflow-tooltip="true"
                width="150"
                :sort-orders="['descending','ascending']" sortable="custom"
            />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right"
                             width="150">
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['product:order:edit']"
                    >查看详情
                    </el-button>
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['product:order:remove']"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改订单对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body :close-on-click-modal="false" destroy-on-close>
            <el-form ref="form" :model="form" :rules="rules" label-width="100px">
                <el-form-item label="订单号" prop="orderNum">
                    <el-input v-model="form.orderNum" placeholder="请输入订单号" readonly/>
                </el-form-item>
                <el-form-item label="订单总金额" prop="totalAmount">
                    <el-input v-model="form.totalAmount" placeholder="请输入订单总金额" readonly/>
                </el-form-item>
                <el-form-item label="应付金额" prop="payAmount">
                    <el-input v-model="form.payAmount" placeholder="请输入应付金额" readonly/>
                </el-form-item>
                <el-form-item label="支付时间" prop="paymentTime">
                    <el-date-picker clearable
                                    v-model="form.paymentTime"
                                    type="datetime"
                                    placeholder="请选择支付时间" readonly>
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="支付凭证" prop="payOrder">
                    <el-input v-model="form.payOrder" placeholder="请输入支付凭证" readonly/>
                </el-form-item>
                <!--                <el-form-item label="套餐id" prop="packageId">-->
                <!--                    <el-input v-model="form.packageId" placeholder="请输入套餐id" readonly/>-->
                <!--                </el-form-item>-->
                <el-form-item label="商品名称" prop="remark">
                    <el-input v-model="form.remark" placeholder="请输入备注" readonly/>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
import {listOrder, getOrder, delOrder, addOrder, updateOrder} from "@/api/product/order";

export default {
    name: "Order",
    dicts: ['pay_type', 'order_status'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 订单表格数据
            orderList: [],
            // 订单商品表格数据
            orderItemsList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 商品总价时间范围
            daterangePaymentTime: [],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                orderNum: null,
                totalAmount: null,
                payAmount: null,
                payType: null,
                status: null,
                paymentTime: null,
                count: null,
                payOrder: null,
                createByPhoneNumber: null,
                packageId: null,
                packageType: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {}
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询订单列表 */
        getList() {
            this.loading = true;
            this.queryParams.params = {};
            if (null != this.daterangePaymentTime && '' != this.daterangePaymentTime) {
                this.queryParams.params["beginPaymentTime"] = this.daterangePaymentTime[0];
                this.queryParams.params["endPaymentTime"] = this.daterangePaymentTime[1];
            }
            listOrder(this.queryParams).then(response => {
                this.orderList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                orderId: null,
                orderNum: null,
                totalAmount: null,
                payAmount: null,
                payType: null,
                type: null,
                status: null,
                createBy: null,
                createTime: null,
                paymentTime: null,
                count: null,
                payOrder: null,
                packageId: null,
                packageType: null,
                updateBy: null,
                updateTime: null,
                remark: null
            };
            this.orderItemsList = [];
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.daterangePaymentTime = [];
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.orderId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加订单";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const orderId = row.orderId || this.ids
            getOrder(orderId).then(response => {
                this.form = response.data;
                this.orderItemsList = response.data.orderItemsList;
                this.open = true;
                this.title = "查看详情";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    this.form.orderItemsList = this.orderItemsList;
                    if (this.form.orderId != null) {
                        updateOrder(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addOrder(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        handleSortChange(column) {
            this.queryParams.orderByColumn = column.prop;//查询字段是表格中字段名字
            this.queryParams.isAsc = column.order;//动态取值排序顺序
            console.log(this.queryParams);
            this.getList();
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const orderIds = row.orderId || this.ids;
            this.$modal.confirm('是否确认删除订单编号为"' + orderIds + '"的数据项？').then(function () {
                return delOrder(orderIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 订单商品序号 */
        rowOrderItemsIndex({row, rowIndex}) {
            row.index = rowIndex + 1;
        },
        /** 订单商品添加按钮操作 */
        handleAddOrderItems() {
            let obj = {};
            obj.productId = "";
            obj.quantity = "";
            obj.unitPrice = "";
            obj.subtotal = "";
            this.orderItemsList.push(obj);
        },
        /** 订单商品删除按钮操作 */
        handleDeleteOrderItems() {
            if (this.checkedOrderItems.length == 0) {
                this.$modal.msgError("请先选择要删除的订单商品数据");
            } else {
                const orderItemsList = this.orderItemsList;
                const checkedOrderItems = this.checkedOrderItems;
                this.orderItemsList = orderItemsList.filter(function (item) {
                    return checkedOrderItems.indexOf(item.index) == -1
                });
            }
        },
        /** 复选框选中数据 */
        handleOrderItemsSelectionChange(selection) {
            this.checkedOrderItems = selection.map(item => item.index)
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('product/order/export', {
                ...this.queryParams
            }, `order_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>
