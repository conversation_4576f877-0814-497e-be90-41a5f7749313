<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户id" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="认证类型【0:个人认证;1:企业认证】" prop="verificationType">
        <el-select v-model="queryParams.verificationType" placeholder="请选择认证类型【0:个人认证;1:企业认证】" clearable>
          <el-option
            v-for="dict in dict.type.verification_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核时间" prop="reviewedTime">
        <el-date-picker clearable
          v-model="queryParams.reviewedTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择审核时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="认证失败原因" prop="rejectionReason">
        <el-input
          v-model="queryParams.rejectionReason"
          placeholder="请输入认证失败原因"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['verification:verification:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['verification:verification:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['verification:verification:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['verification:verification:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="verificationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="用户认证主键" align="center" prop="verificationId" />
      <el-table-column label="用户id" align="center" prop="userId" />
      <el-table-column label="认证类型【0:个人认证;1:企业认证】" align="center" prop="verificationType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.verification_type" :value="scope.row.verificationType"/>
        </template>
      </el-table-column>
      <el-table-column label="认证状态" align="center" prop="verificationStatus" />
      <el-table-column label="审核时间" align="center" prop="reviewedTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.reviewedTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="认证失败原因" align="center" prop="rejectionReason" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['verification:verification:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['verification:verification:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户认证主对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户id" />
        </el-form-item>
        <el-form-item label="认证类型【0:个人认证;1:企业认证】" prop="verificationType">
          <el-select v-model="form.verificationType" placeholder="请选择认证类型【0:个人认证;1:企业认证】">
            <el-option
              v-for="dict in dict.type.verification_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核时间" prop="reviewedTime">
          <el-date-picker clearable
            v-model="form.reviewedTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择审核时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="认证失败原因" prop="rejectionReason">
          <el-input v-model="form.rejectionReason" placeholder="请输入认证失败原因" />
        </el-form-item>
        <el-divider content-position="center">个人认证信息信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddIndividualVerification">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteIndividualVerification">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="individualVerificationList" :row-class-name="rowIndividualVerificationIndex" @selection-change="handleIndividualVerificationSelectionChange" ref="individualVerification">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
          <el-table-column label="真实姓名" prop="realName" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.realName" placeholder="请输入真实姓名" />
            </template>
          </el-table-column>
          <el-table-column label="证件类型" prop="idType" width="150">
            <template slot-scope="scope">
              <el-select v-model="scope.row.idType" placeholder="请选择证件类型">
                <el-option label="请选择字典生成" value="" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="证件号码" prop="idNumber" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.idNumber" placeholder="请输入证件号码" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listVerification, getVerification, delVerification, addVerification, updateVerification } from "@/api/verification/userVerification";

export default {
  name: "Verification",
  dicts: ['verification_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedIndividualVerification: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户认证主表格数据
      verificationList: [],
      // 个人认证信息表格数据
      individualVerificationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        verificationType: null,
        verificationStatus: null,
        reviewedTime: null,
        rejectionReason: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户认证主列表 */
    getList() {
      this.loading = true;
      listVerification(this.queryParams).then(response => {
        this.verificationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        verificationId: null,
        userId: null,
        verificationType: null,
        verificationStatus: null,
        createTime: null,
        reviewedTime: null,
        rejectionReason: null
      };
      this.individualVerificationList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.verificationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户认证主";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const verificationId = row.verificationId || this.ids
      getVerification(verificationId).then(response => {
        this.form = response.data;
        this.individualVerificationList = response.data.individualVerificationList;
        this.open = true;
        this.title = "修改用户认证主";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.individualVerificationList = this.individualVerificationList;
          if (this.form.verificationId != null) {
            updateVerification(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addVerification(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const verificationIds = row.verificationId || this.ids;
      this.$modal.confirm('是否确认删除用户认证主编号为"' + verificationIds + '"的数据项？').then(function() {
        return delVerification(verificationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
	/** 个人认证信息序号 */
    rowIndividualVerificationIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 个人认证信息添加按钮操作 */
    handleAddIndividualVerification() {
      let obj = {};
      obj.realName = "";
      obj.idType = "";
      obj.idNumber = "";
      this.individualVerificationList.push(obj);
    },
    /** 个人认证信息删除按钮操作 */
    handleDeleteIndividualVerification() {
      if (this.checkedIndividualVerification.length == 0) {
        this.$modal.msgError("请先选择要删除的个人认证信息数据");
      } else {
        const individualVerificationList = this.individualVerificationList;
        const checkedIndividualVerification = this.checkedIndividualVerification;
        this.individualVerificationList = individualVerificationList.filter(function(item) {
          return checkedIndividualVerification.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleIndividualVerificationSelectionChange(selection) {
      this.checkedIndividualVerification = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('user/verification/export', {
        ...this.queryParams
      }, `verification_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
