<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px">
            <el-form-item label="资源名称" prop="sourceName">
                <el-input
                    v-model="queryParams.sourceName"
                    placeholder="请输入资源名称"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="资源类型" prop="type">
                <el-select v-model="queryParams.type" placeholder="请选择资源类型" clearable>
                    <el-option
                        v-for="dict in dict.type.user_resource_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="修复类型" prop="repairType">
                <el-select v-model="queryParams.repairType" placeholder="请选择修复类型" clearable>
                    <el-option
                        v-for="dict in dict.type.repair_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="路径" prop="url">
                <el-input
                    v-model="queryParams.url"
                    placeholder="请输入路径"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="视频封面路径" prop="videoCover">
                <el-input
                    v-model="queryParams.videoCover"
                    placeholder="请输入视频封面路径"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="是否公开" prop="isPublic">
                <el-input
                    v-model="queryParams.isPublic"
                    placeholder="请输入是否公开"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="置顶" prop="isTop">
                <el-input
                    v-model="queryParams.isTop"
                    placeholder="请输入置顶"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="所属用户" prop="userId">
                <el-input
                    v-model="queryParams.userId"
                    placeholder="请输入所属用户"
                    clearable
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAdd"
                    v-hasPermi="['api_hub:resource:add']"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="el-icon-edit"
                    size="mini"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['api_hub:resource:edit']"
                >修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['api_hub:resource:remove']"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['api_hub:resource:export']"
                >导出
                </el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="resourceList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="主键" align="center" prop="id"/>
            <el-table-column label="资源名称" align="center" prop="sourceName"/>
            <el-table-column label="资源类型" align="center" prop="type">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.user_resource_type" :value="scope.row.type"/>
                </template>
            </el-table-column>
            <el-table-column label="修复类型" align="center" prop="repairType">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.repair_type" :value="scope.row.type"/>
                </template>
            </el-table-column>
            <el-table-column label="路径" align="center" prop="url"/>
            <el-table-column label="视频封面路径" align="center" prop="videoCover"/>
            <el-table-column label="是否公开" align="center" prop="isPublic"/>
            <el-table-column label="置顶" align="center" prop="isTop"/>
            <el-table-column label="所属用户" align="center" prop="userId"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['api_hub:resource:edit']"
                    >修改
                    </el-button>
                    <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['api_hub:resource:remove']"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改用户资源对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false" destroy-on-close>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="资源名称" prop="sourceName">
                    <el-input v-model="form.sourceName" placeholder="请输入资源名称"/>
                </el-form-item>
                <el-form-item label="资源类型" prop="type">
                    <el-select v-model="form.type" placeholder="请选择资源类型">
                        <el-option
                            v-for="dict in dict.type.user_resource_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="parseInt(dict.value)"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="修复类型" prop="repairType">
                    <el-select v-model="form.repairType" placeholder="请选择修复类型">
                        <el-option
                            v-for="dict in dict.type.repair_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="parseInt(dict.value)"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="路径" prop="url">
                    <el-input v-model="form.url" placeholder="请输入路径"/>
                </el-form-item>
                <el-form-item label="视频封面路径" prop="videoCover">
                    <el-input v-model="form.videoCover" placeholder="请输入视频封面路径"/>
                </el-form-item>
                <el-form-item label="是否公开" prop="isPublic">
                    <el-input v-model="form.isPublic" placeholder="请输入是否公开"/>
                </el-form-item>
                <el-form-item label="置顶" prop="isTop">
                    <el-input v-model="form.isTop" placeholder="请输入置顶"/>
                </el-form-item>
                <el-form-item label="所属用户" prop="userId">
                    <el-input v-model="form.userId" placeholder="请输入所属用户"/>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {listResource, getResource, delResource, addResource, updateResource} from "@/api/user_resource/userResource";

export default {
    name: "Resource",
    dicts: ['user_resource_type', 'repair_type'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 用户资源表格数据
            resourceList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                sourceName: null,
                type: null,
                url: null,
                repairType: null,
                videoCover: null,
                isPublic: null,
                isTop: null,
                userId: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {}
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询用户资源列表 */
        getList() {
            this.loading = true;
            listResource(this.queryParams).then(response => {
                this.resourceList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                sourceName: null,
                type: null,
                repairType: null,
                url: null,
                videoCover: null,
                isPublic: null,
                isTop: null,
                userId: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加用户资源";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            getResource(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改用户资源";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        updateResource(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addResource(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal.confirm('是否确认删除用户资源编号为"' + ids + '"的数据项？').then(function () {
                return delResource(ids);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('resource/export', {
                ...this.queryParams
            }, `resource_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>
