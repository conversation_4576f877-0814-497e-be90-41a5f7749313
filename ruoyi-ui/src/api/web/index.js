import request from '@/utils/request'
import store from "@/store/index";

// 查询用户模型列表
export function getModelList(query) {
    return request({
        url: '/ai/models/getModelList',
        method: 'get',
        params: query
    })
}

export function getModelDetail(query) {
    return request({
        url: '/ai/models/getByModelId',
        method: 'get',
        params: query
    })
}

export function getTagList(query) {
    return request({
        url: '/ai/tags/list',
        method: 'get',
        params: query
    })
}

export function getCategoriesList(query) {
    return request({
        url: '/ai/categories/list',
        method: 'get',
        params: query
    })
}

export function getSupplierList(query) {
    return request({
        url: '/ai/supplier/list',
        method: 'get',
        params: query
    })
}


// AI 规格
export function getSpecType(query) {
    return request({
        url: '/system/dict/data/type/ai_model_spec_type',
        method: 'get',
        params: query
    })
}

// 创建密钥
export function generateKey(params) {
    return request({
        url: '/apikey/generateKey',
        method: 'post',
        params: params
    })
}

// 删除密钥
export function deleteApiKey(keyId) {
    return request({
        url: '/apikey/remove/' + keyId,
        method: 'delete'
    })
}

// 获取我的密钥
export function getMyKey(query) {
    return request({
        url: '/apikey/getMyKey',
        method: 'get',
        params: query
    })
}

// 获取我的密钥
export function redirectKey(params) {
    return request({
        url: '/apikey/redirect',
        method: 'post',
        data: params
    })
}

// 获取我的密钥
export function getMyAccount(params) {
    return request({
        url: '/account/getMyAccount',
        method: 'get',
        params: params
    })
}

// 获取我的密钥
export function getMyTokenAccount(params) {
    return request({
        url: '/product/token/getMyTokenAccount',
        method: 'get',
        params: params
    })
}

export function getMonthlyStatistics(params) {
    return request({
        url: '/bill/record/getMonthlyStatistics',
        method: 'get',
        params: params
    })
}

export function getDayStatistics(params) {
    return request({
        url: '/bill/record/getDayStatistics',
        method: 'get',
        params: params
    })
}

export function getMyBillList(params) {
    return request({
        url: '/bill/record/getMyBillList',
        method: 'get',
        params: params
    })
}

export function getMyAgentBillList(params) {
    return request({
        url: '/bill/record/getMyAgentBillList',
        method: 'get',
        params: params
    })
}

export function getBillDetailList(params) {
    return request({
        url: '/bill/detail/getBillDetailList',
        method: 'get',
        params: params
    })
}

export function getModelUsageStatistics(params) {
    return request({
        url: '/bill/record/getModelUsageStatistics',
        method: 'get',
        params: params
    })
}

export function getApiKeyUsageStatistics(params) {
    return request({
        url: '/bill/record/getApiKeyUsageStatistics',
        method: 'get',
        params: params
    })
}

export function getAgentUsageStatistics(params) {
    return request({
        url: '/bill/record/getAgentUsageStatistics',
        method: 'get',
        params: params
    })
}

// 用户认证提交审核
export function certification(params) {
    return request({
        url: '/user/verification/review',
        method: 'post',
        data: params
    })
}

// 获取赠送额度
export function getGiftBalance(params) {
    return request({
        url: '/user/verification/getGiftBalance',
        method: 'get',
        params: params
    })
}

// 获取用户认证信息
export function getVerificationInfo(params) {
    return request({
        url: '/user/verification/getInfo',
        method: 'get',
        params: params
    })
}

// 调用AI对话
export function chatCompletions(params) {

    return new Promise((resolve, reject) => {
        return store.getters.apiKey ? resolve(store.getters.apiKey) : resolve(store.dispatch("GetApiKey"))
    }).then((apiKey) => {
        return fetch(process.env.VUE_APP_BASE_API + "/agent/v1/chat/completions", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify(params),
        })
    })

}


// 智能体广场分类
export function agentCateList(params) {
    return request({
        url: '/agent/category/list',
        method: 'get',
        params: params
    })
}

// 智能体广场模型列表
export function agentMarketList(params) {
    return request({
        url: '/agent/market/queryPage',
        method: 'get',
        params: params
    })
}


// 我的智能体新增
export function markeTaddOrUpdate(params) {
    return request({
        url: '/agent/market/addOrUpdate',
        method: 'post',
        data: params
    })
}

// 查询智能体分类列表
export function agentCategoryList(params) {
    return request({
        url: '/agent/category/list',
        method: 'get',
        params: params
    })
}

// 根据id查询
export function marketQueryById(params) {
    return request({
        url: '/agent/market/queryById',
        method: 'get',
        params: params
    })
}

// 批量提交审核 /agent/market/submitExamine
export function submitExamine(params) {
    return request({
        url: '/agent/market/submitExamine',
        method: 'post',
        data: params
    })
}

// 批量上架 /agent/market/submitExamine
export function submitUp(params) {
    return request({
        url: '/agent/market/up',
        method: 'post',
        data: params
    })
}

// 批量下架 /agent/market/submitExamine
export function submitDown(params) {
    return request({
        url: '/agent/market/down',
        method: 'post',
        data: params
    })
}

// 审核
export function authExamine(params) {
    return request({
        url: '/agent/market/examine',
        method: 'post',
        data: params
    })
}

// 授权
export function agentAuthorization(params) {
    return request({
        url: '/agent/market/auth',
        method: 'get',
        params: params
    })
}

// 开发者审核
export function devExamine(params) {
    return request({
        url: '/dev/verification/examine',
        method: 'post',
        data: params
    })
}

// 获取可开票金额
export function getInvoiceAmount(params) {
    return request({
        url: '/user/invoice/prepare',
        method: 'get',
        params: params
    })
}

// 提交开票申请
export function invoiceSubmit(data) {
    return request({
        url: '/user/invoice/submit',
        method: 'post',
        data: data
    })
}

// 获取我的开票记录
export function getMyInvoiceList(params) {
    return request({
        url: '/user/invoice/getList',
        method: 'get',
        params: params
    })
}


// 发票审核
export function invoiceExamine(params) {
    return request({
        url: '/user/invoice/examine',
        method: 'post',
        data: params
    })
}
