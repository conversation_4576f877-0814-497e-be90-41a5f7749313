import request from '@/utils/request'

// 查询个人认证信息列表
export function listVerification(query) {
  return request({
    url: '/verification/individual/list',
    method: 'get',
    params: query
  })
}

// 查询个人认证信息详细
export function getVerification(individualId) {
  return request({
    url: '/verification/individual/' + individualId,
    method: 'get'
  })
}

// 新增个人认证信息
export function addVerification(data) {
  return request({
    url: '/verification/individual',
    method: 'post',
    data: data
  })
}

// 修改个人认证信息
export function updateVerification(data) {
  return request({
    url: '/verification/individual',
    method: 'put',
    data: data
  })
}

// 删除个人认证信息
export function delVerification(individualId) {
  return request({
    url: '/verification/individual/' + individualId,
    method: 'delete'
  })
}
