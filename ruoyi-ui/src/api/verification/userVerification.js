import request from '@/utils/request'

// 查询用户认证主列表
export function listVerification(query) {
  return request({
    url: '/user/verification/list',
    method: 'get',
    params: query
  })
}

// 查询用户认证主详细
export function getVerification(verificationId) {
  return request({
    url: '/user/verification/' + verificationId,
    method: 'get'
  })
}

// 新增用户认证主
export function addVerification(data) {
  return request({
    url: '/user/verification',
    method: 'post',
    data: data
  })
}

// 修改用户认证主
export function updateVerification(data) {
  return request({
    url: '/user/verification',
    method: 'put',
    data: data
  })
}

// 删除用户认证主
export function delVerification(verificationId) {
  return request({
    url: '/user/verification/' + verificationId,
    method: 'delete'
  })
}
