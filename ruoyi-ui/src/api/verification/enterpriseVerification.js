import request from '@/utils/request'

// 查询企业认证信息列表
export function listVerification(query) {
  return request({
    url: '/verification/enterprise/list',
    method: 'get',
    params: query
  })
}

// 查询企业认证信息详细
export function getVerification(enterpriseId) {
  return request({
    url: '/verification/enterprise/' + enterpriseId,
    method: 'get'
  })
}

// 新增企业认证信息
export function addVerification(data) {
  return request({
    url: '/verification/enterprise',
    method: 'post',
    data: data
  })
}

// 修改企业认证信息
export function updateVerification(data) {
  return request({
    url: '/verification/enterprise',
    method: 'put',
    data: data
  })
}

// 删除企业认证信息
export function delVerification(enterpriseId) {
  return request({
    url: '/verification/enterprise/' + enterpriseId,
    method: 'delete'
  })
}
