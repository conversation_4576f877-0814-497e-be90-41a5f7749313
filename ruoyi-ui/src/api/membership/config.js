import request from '@/utils/request'

// 查询商品配置列表
export function listSetting(query) {
    return request({
        url: '/product/setting/list',
        method: 'get',
        params: query
    })
}

// 查询商品配置详细
export function getSetting(id) {
    return request({
        url: '/product/setting/' + id,
        method: 'get'
    })
}

// 新增商品配置
export function addSetting(data) {
    return request({
        url: '/product/setting',
        method: 'post',
        data: data
    })
}

// 修改商品配置
export function updateSetting(data) {
    return request({
        url: '/product/setting',
        method: 'put',
        data: data
    })
}

// 删除商品配置
export function delSetting(id) {
    return request({
        url: '/product/setting/' + id,
        method: 'delete'
    })
}
