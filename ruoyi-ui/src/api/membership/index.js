import request from '@/utils/request'

// 查询会员列表
export function listMembership(query) {
    return request({
        url: '/product/membership/list',
        method: 'get',
        params: query
    })
}

// 查询会员详细
export function getMembership(id) {
    return request({
        url: '/product/membership/' + id,
        method: 'get'
    })
}

// 新增会员
export function addMembership(data) {
    return request({
        url: '/product/membership',
        method: 'post',
        data: data
    })
}

// 修改会员
export function updateMembership(data) {
    return request({
        url: '/product/membership',
        method: 'put',
        data: data
    })
}

// 删除会员
export function delMembership(id) {
    return request({
        url: '/product/membership/' + id,
        method: 'delete'
    })
}
