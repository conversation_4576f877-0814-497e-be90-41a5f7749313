import request from '@/utils/request'

// 查询用户商品列表
export function listProduct(query) {
  return request({
    url: '/user/product/list',
    method: 'get',
    params: query
  })
}

// 查询用户商品详细
export function getProduct(id) {
  return request({
    url: '/user/product/' + id,
    method: 'get'
  })
}

// 新增用户商品
export function addProduct(data) {
  return request({
    url: '/user/product',
    method: 'post',
    data: data
  })
}

// 修改用户商品
export function updateProduct(data) {
  return request({
    url: '/user/product',
    method: 'put',
    data: data
  })
}

// 删除用户商品
export function delProduct(id) {
  return request({
    url: '/user/product/' + id,
    method: 'delete'
  })
}
