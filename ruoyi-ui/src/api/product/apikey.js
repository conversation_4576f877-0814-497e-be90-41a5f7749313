import request from '@/utils/request'

// 查询用户密匙列表
export function listKey(query) {
  return request({
    url: '/apikey/list',
    method: 'get',
    params: query
  })
}

// 查询用户密匙详细
export function getKey(keyId) {
  return request({
    url: '/apikey/' + keyId,
    method: 'get'
  })
}

// 新增用户密匙
export function addKey(data) {
  return request({
    url: '/apikey',
    method: 'post',
    data: data
  })
}

// 修改用户密匙
export function updateKey(data) {
  return request({
    url: '/apikey',
    method: 'put',
    data: data
  })
}

// 删除用户密匙
export function delKey(keyId) {
  return request({
    url: '/apikey/' + keyId,
    method: 'delete'
  })
}
