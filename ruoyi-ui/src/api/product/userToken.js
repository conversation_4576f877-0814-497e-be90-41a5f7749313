import request from '@/utils/request'

// 查询用户token数量列表
export function listToken(query) {
  return request({
    url: '/product/token/list',
    method: 'get',
    params: query
  })
}

// 查询用户token数量详细
export function getToken(tokenId) {
  return request({
    url: '/product/token/' + tokenId,
    method: 'get'
  })
}

// 新增用户token数量
export function addToken(data) {
  return request({
    url: '/product/token',
    method: 'post',
    data: data
  })
}

// 修改用户token数量
export function updateToken(data) {
  return request({
    url: '/product/token',
    method: 'put',
    data: data
  })
}

// 删除用户token数量
export function delToken(tokenId) {
  return request({
    url: '/product/token/' + tokenId,
    method: 'delete'
  })
}
