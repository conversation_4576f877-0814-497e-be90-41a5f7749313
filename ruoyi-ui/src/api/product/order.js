import request from '@/utils/request'

// 查询订单列表
export function listOrder(query) {
    return request({
        url: '/order/list',
        method: 'get',
        params: query
    })
}

// 查询订单详细
export function getOrder(orderId) {
    return request({
        url: '/order/' + orderId,
        method: 'get'
    })
}

// 新增订单
export function addOrder(data) {
    return request({
        url: '/order',
        method: 'post',
        data: data
    })
}

// 修改订单
export function updateOrder(data) {
    return request({
        url: '/order',
        method: 'put',
        data: data
    })
}

// 删除订单
export function delOrder(orderId) {
    return request({
        url: '/order/' + orderId,
        method: 'delete'
    })
}


// 下单---支付---二维码
export function placeOrder(data) {
    /*{
        "count": 0,
        "isPlace": true,
        "packageId": 0,
        "payOrder": "string",
        "payTime": "2024-09-02T07:10:47.807Z",
        "payType": 0,
        "productId": 0
    }*/
    return request({
        url: '/order/placeOrder',
        method: 'post',
        data: data
    })
}

export function paying(query) {
    /*{
        orderNumber ：''，
        payType ：1, // 扫码是1
    }*/
    return request({
        url: '/pay/paying',
        method: 'get',
        params: query
    })
}


export function createScanPayQrcode(data) {
    /*{
        codeUrl  ：''，
        sideLength ：'', // 扫码是1
    }*/
    return request({
        url: '/pay/createScanPayQrcode?codeUrl=' + data.codeUrl,
        method: 'post',
        data: {}
    })
}


export function getMyOrderList(query) {
    /*{
        orderNumber ：''，
        payType ：1, // 扫码是1
    }*/
    return request({
        url: '/order/getMyOrderList',
        method: 'get',
        params: query
    })
}

export function queryOrder(query) {
    /*{
        orderNumber ：''，
        payType ：1, // 扫码是1
    }*/
    return request({
        url: '/pay/queryOrder',
        method: 'get',
        params: query
    })
}
