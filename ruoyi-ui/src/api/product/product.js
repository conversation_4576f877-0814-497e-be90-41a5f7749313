import request from '@/utils/request'

// 查询AI商品列表
export function listProduct(query) {
    return request({
        url: '/product/list',
        method: 'get',
        params: query
    })
}

// 查询AI商品详细
export function getProduct(productId) {
    return request({
        url: '/product/' + productId,
        method: 'get'
    })
}

export function  getProductByProductIds(ids){
    return request({
        url: '/product/getProductByProductIds/' + ids,
        method: 'get'
    })
}

// 新增AI商品
export function addProduct(data) {
    return request({
        url: '/product',
        method: 'post',
        data: data
    })
}

// 修改AI商品
export function updateProduct(data) {
    return request({
        url: '/product',
        method: 'put',
        data: data
    })
}

// 删除AI商品
export function delProduct(productId) {
    return request({
        url: '/product/' + productId,
        method: 'delete'
    })
}
