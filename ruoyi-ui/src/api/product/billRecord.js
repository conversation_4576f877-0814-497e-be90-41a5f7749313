import request from '@/utils/request'

// 查询用户下消费记录列表
export function listBillRecord(query) {
  return request({
    url: '/bill/record/list',
    method: 'get',
    params: query
  })
}

// 查询用户下消费记录详细
export function getBillRecord(billId) {
  return request({
    url: '/bill/record/' + billId,
    method: 'get'
  })
}

// 新增用户下消费记录
export function addBillRecord(data) {
  return request({
    url: '/bill/record',
    method: 'post',
    data: data
  })
}

// 修改用户下消费记录
export function updateBillRecord(data) {
  return request({
    url: '/bill/record',
    method: 'put',
    data: data
  })
}

// 删除用户下消费记录
export function delBillRecord(billId) {
  return request({
    url: '/bill/record/' + billId,
    method: 'delete'
  })
}
