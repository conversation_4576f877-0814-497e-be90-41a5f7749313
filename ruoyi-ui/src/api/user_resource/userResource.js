import request from '@/utils/request'

// 查询用户资源列表
export function listResource(query) {
  return request({
    url: '/resource/list',
    method: 'get',
    params: query
  })
}

// 查询用户资源详细
export function getResource(id) {
  return request({
    url: '/resource/' + id,
    method: 'get'
  })
}

// 新增用户资源
export function addResource(data) {
  return request({
    url: '/resource',
    method: 'post',
    data: data
  })
}

// 修改用户资源
export function updateResource(data) {
  return request({
    url: '/resource',
    method: 'put',
    data: data
  })
}

// 删除用户资源
export function delResource(id) {
  return request({
    url: '/resource/' + id,
    method: 'delete'
  })
}
