import request from "@/utils/request";

// 查询项目合作孵化申请列表
export function listIncubation(query) {
    return request({
        url: "/project/incubation/list",
        method: "get",
        params: query,
    });
}

// 查询项目合作孵化申请详细
export function getIncubation(id) {
    return request({
        url: "/project/incubation/" + id,
        method: "get",
    });
}

// 新增项目合作孵化申请
export function addIncubation(data) {
    return request({
        url: "/project/incubation",
        method: "post",
        data: data,
    });
}

// 修改项目合作孵化申请
export function updateIncubation(data) {
    return request({
        url: "/project/incubation",
        method: "put",
        data: data,
    });
}

// 删除项目合作孵化申请
export function delIncubation(id) {
    return request({
        url: "/project/incubation/" + id,
        method: "delete",
    });
}
// 查询项目类型
export function listProjectType() {
    return request({
        url: "/project/projectType/list",
        method: "get",
    });
}
