import request from '@/utils/request'

// 查询开发者认证列表
export function listVerification(query) {
    return request({
        url: '/dev/verification/list',
        method: 'get',
        params: query
    })
}

// 查询开发者认证详细
export function getVerification(id) {
    return request({
        url: '/dev/verification/' + id,
        method: 'get'
    })
}

// 新增开发者认证
export function addVerification(data) {
    return request({
        url: '/dev/verification',
        method: 'post',
        data: data
    })
}

// 修改开发者认证
export function updateVerification(data) {
    return request({
        url: '/dev/verification',
        method: 'put',
        data: data
    })
}

// 删除开发者认证
export function delVerification(id) {
    return request({
        url: '/dev/verification/' + id,
        method: 'delete'
    })
}
