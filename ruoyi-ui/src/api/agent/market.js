import request from '@/utils/request'

// 查询应用市场列表
export function listAgent(query) {
  return request({
    url: '/agent/market/list',
    method: 'get',
    params: query
  })
}

// 查询应用市场详细
export function getAgent(id) {
  return request({
    url: '/agent/market/' + id,
    method: 'get'
  })
}

// 新增应用市场
export function addAgent(data) {
  return request({
    url: '/agent/market',
    method: 'post',
    data: data
  })
}

// 修改应用市场
export function updateAgent(data) {
  return request({
    url: '/agent/market',
    method: 'put',
    data: data
  })
}

// 删除应用市场
export function delAgent(id) {
  return request({
    url: '/agent/market/' + id,
    method: 'delete'
  })
}

// 前台应用市场
export function editAgent(data) {
    return request({
        url: '/agent/market/addOrUpdate',
        method: 'post',
        data: data
    })
}
