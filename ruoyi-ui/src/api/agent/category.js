import request from '@/utils/request'

// 查询智能体分类列表
export function listCategory(query) {
  return request({
    url: '/agent/category/list',
    method: 'get',
    params: query
  })
}

// 查询智能体分类详细
export function getCategory(id) {
  return request({
    url: '/agent/category/' + id,
    method: 'get'
  })
}

// 新增智能体分类
export function addCategory(data) {
  return request({
    url: '/agent/category',
    method: 'post',
    data: data
  })
}

// 修改智能体分类
export function updateCategory(data) {
  return request({
    url: '/agent/category',
    method: 'put',
    data: data
  })
}

// 删除智能体分类
export function delCategory(id) {
  return request({
    url: '/agent/category/' + id,
    method: 'delete'
  })
}
