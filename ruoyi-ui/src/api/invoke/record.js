import request from '@/utils/request'

// 查询接口调用记录列表
export function listRecord(query) {
    return request({
        url: '/invoke/record/list',
        method: 'get',
        params: query
    })
}

// 查询接口调用记录列表
export function taskRecord(id) {
    return request({
        url: 'invoke/record/getRecordByTaskId?taskId=' + id,
        method: 'get'
    })
}

// 查询接口调用记录详细
export function getRecord(id) {
    return request({
        url: '/invoke/record/' + id,
        method: 'get'
    })
}

// 新增接口调用记录
export function addRecord(data) {
    return request({
        url: '/invoke/record',
        method: 'post',
        data: data
    })
}

// 修改接口调用记录
export function updateRecord(data) {
    return request({
        url: '/invoke/record',
        method: 'put',
        data: data
    })
}

// 删除接口调用记录
export function delRecord(id) {
    return request({
        url: '/invoke/record/' + id,
        method: 'delete'
    })
}
