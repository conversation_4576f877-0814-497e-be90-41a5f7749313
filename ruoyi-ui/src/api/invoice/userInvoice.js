import request from '@/utils/request'

// 查询用户开票申请列表
export function listInvoice(query) {
  return request({
    url: '/user/invoice/list',
    method: 'get',
    params: query
  })
}

// 查询用户开票申请详细
export function getInvoice(invoiceId) {
  return request({
    url: '/user/invoice/' + invoiceId,
    method: 'get'
  })
}

// 新增用户开票申请
export function addInvoice(data) {
  return request({
    url: '/user/invoice',
    method: 'post',
    data: data
  })
}

// 修改用户开票申请
export function updateInvoice(data) {
  return request({
    url: '/user/invoice',
    method: 'put',
    data: data
  })
}

// 删除用户开票申请
export function delInvoice(invoiceId) {
  return request({
    url: '/user/invoice/' + invoiceId,
    method: 'delete'
  })
}
