import request from '@/utils/request'

// 查询Ip地址转发列表
export function listMapping(query) {
  return request({
    url: '/host/mapping/list',
    method: 'get',
    params: query
  })
}

// 查询Ip地址转发详细
export function getMapping(id) {
  return request({
    url: '/host/mapping/' + id,
    method: 'get'
  })
}

// 新增Ip地址转发
export function addMapping(data) {
  return request({
    url: '/host/mapping',
    method: 'post',
    data: data
  })
}

// 修改Ip地址转发
export function updateMapping(data) {
  return request({
    url: '/host/mapping',
    method: 'put',
    data: data
  })
}

// 删除Ip地址转发
export function delMapping(id) {
  return request({
    url: '/host/mapping/' + id,
    method: 'delete'
  })
}
