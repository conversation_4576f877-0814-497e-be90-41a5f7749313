import request from '@/utils/request'

// 查询核销记录列表
export function listRecord(query) {
    return request({
        url: '/invoke/record/list',
        method: 'get',
        params: query
    })
}

// 查询核销记录详细
export function getRecord(id) {
    return request({
        url: '/invoke/record/' + id,
        method: 'get'
    })
}

// 新增核销记录
export function addRecord(data) {
    return request({
        url: '/invoke/record',
        method: 'post',
        data: data
    })
}

// 修改核销记录
export function updateRecord(data) {
    return request({
        url: '/invoke/record',
        method: 'put',
        data: data
    })
}

// 删除核销记录
export function delRecord(id) {
    return request({
        url: '/invoke/record/' + id,
        method: 'delete'
    })
}
