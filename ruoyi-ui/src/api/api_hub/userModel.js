import request from '@/utils/request'

// 查询用户模型列表
export function listModel(query) {
    return request({
        // url: '/user/models/list',
        url: '/user/model/getMyModel',
        method: 'get',
        params: query
    })
}

export function listModel2(query) {
    return request({
        url: '/user/model/list',
        // url: '/user/models/getMyModel',
        method: 'get',
        params: query
    })
}

// 创建用户模型
export function createModel(data) {
    return request({
        url: '/user/model/create',
        method: 'post',
        data: data
    })
}

// 查询用户模型详细
export function getModel(id) {
    return request({
        url: '/user/model/' + id,
        method: 'get'
    })
}

// 新增用户模型
export function addModel(data) {
    return request({
        url: '/user/model',
        method: 'post',
        data: data
    })
}

// 修改用户模型
export function updateModel(data) {
    return request({
        url: '/user/model',
        method: 'put',
        data: data
    })
}

// 删除用户模型
export function delModel(id) {
    return request({
        url: '/user/model/' + id,
        method: 'delete'
    })
}
