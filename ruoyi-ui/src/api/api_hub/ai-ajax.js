import request from '@/utils/request'

const AiPath = '/interface/info/invoke';
// const AiPath2 = 'http://localhost/dev-api/interface/info/invoke';
const AiPath2 = process.env.VUE_APP_BASE_API + '/interface/info/invoke';

// 上传修复资源
export function uploadResources(options) {
    options.urlDir = options.urlDir || '/revive/'; // 默认是修复照片的目录
    const url = AiPath + '/resources/models/' + options.model + '/' + options.urlDir + '/' + options.dir + '/' + options.fileName;
    let data = new FormData();
    const config = {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    }
    data.append('raw', options.file);
    request.post(url, data, config).then(res => {
        options.success && options.success(res);
    }).catch(e => {
        options.fail && options.fail(e);
    })
}

// 修复图片
export function reviveImage(model, data) {
    const url = AiPath + '/models/revive/image/' + model;
    return request({
        url: url,
        method: 'post',
        data: data
    })
}


// 修复视频
export function reviveVideo(model, data) {
    const url = AiPath + '/models/revive/video/' + model;
    return request({
        url: url,
        method: 'post',
        data: data
    })
}

// 获取修复配置
export function reviveConfig() {
    return request({
        url: AiPath + '/model/config/revive',
        method: 'get'
    })
}

export function animeConfig() {
    return request({
        url: AiPath + '/model/config/anime',
        method: 'get'
    })
}

// 删除修复驱动示例
export function DelDrivingExamples(file) {
    return request({
        url: AiPath + '/resources/revive/driving/examples/' + file,
        method: 'delete'
    })
}

// 获取修复驱动示例
export function GetDrivingExamples(file) {
    return request({
        url: AiPath + '/resources/revive/driving/examples/' + file,
        method: 'get'
    })
}

// 上传修复驱动示例
export function PostDrivingExamples(options) {
    const url = AiPath + '/resources/revive/driving/examples/' + options.fileName
    let data = new FormData();
    const config = {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    }
    data.append('example', options.file);
    request.post(url, data, config).then(res => {
        options.success && options.success(res);
    }).catch(e => {
        options.fail && options.fail(e);
    })
}

// 获取指定的任务进度
export function taskConfig(model) {
    return request({
        url: AiPath + '/model/config/' + model,
        method: 'get'
    })
}

// 获取指定的任务进度
export function deleteTaskConfig(model, data) {
    return request({
        url: AiPath + '/model/config/' + model + '?name=' + data.name,
        method: 'delete',
        data: {}
    })
}

export function RawImagePath(model, name) {
    // 获取上传的图片的地址
    return AiPath2 + '/resources/models/' + model + '/revive/old/' + name;
}

export function AnimeImagePath(model, name) {
    // 获取上传的图片的地址
    return AiPath2 + '/resources/models/' + model + '/anime/old/' + name;
}

export function UsedVideoPath(model, name) {
    // 获取生成的视频的地址
    let AiPath3 = 'http://************:5099';
    return AiPath2 + '/resources/models/' + model + '/used/video/' + name;
}

export function UsedImagePath(model, name) {
    // 获取生成的图片的地址
    return AiPath2 + '/resources/models/' + model + '/used/image/' + name;
}

export function UsedImageList(model) {
    // 获取生成的图片的地址
    return request({
        url: AiPath + '/resources/models/' + model + '/used/image',
        method: 'get'
    })
}

export function UsedVideoList(model) {
    // 获取生成的视频的地址
    return request({
        url: AiPath + '/resources/models/' + model + '/used/video',
        method: 'get'
    })
}

export function UsedAudioPath(model, name) {
    return AiPath2 + '/resources/models/' + model + '/used/audio/' + name;
}

export function ExamplePath(model) {
    return AiPath2 + '/models/example/facechain/' + model;
}

// 查询用户资源列表
export function listResource(query) {
    return request({
        url: '/resource/list',
        method: 'get',
        params: query
    })
}

// 查询用户资源详细
export function getResource(id) {
    return request({
        url: '/resource/' + id,
        method: 'get'
    })
}

// 新增用户资源
export function addResource(data) {
    return request({
        url: '/resource',
        method: 'post',
        data: data
    })
}

// 修改用户资源
export function updateResource(data) {
    return request({
        url: '/resource',
        method: 'put',
        data: data
    })
}

// 删除用户资源
export function delResource(id) {
    return request({
        url: '/resource/' + id,
        method: 'delete'
    })
}


export function testFace(model, data) {
    const url = AiPath + '/models/test/face/' + model;
    return request({
        url: url,
        method: 'post',
        data: data
    })
}

// 图片转动漫
export function Real2anime(model, data) {
    const url = AiPath + '/models/anime/real2anime/' + model;
    return request({
        url: url,
        method: 'post',
        data: data
    })
}


// 动漫转图片
export function Anime2real(model, data) {
    const url = AiPath + '/models/anime/anime2real/' + model;
    return request({
        url: url,
        method: 'post',
        data: data
    })
}
