import request from '@/utils/request'

const AiPath = '/interface/info/invoke';
// const AiPath2 = 'http://localhost/dev-api/interface/info/invoke';
// const AiPath2 = process.env.VUE_APP_BASE_API + '/interface/info/invoke';

// 上传修复资源
export function uploadResources(options) {
    const url = AiPath + '/resources/models/' + options.model + '/revive/' + options.dir + '/' + options.fileName;
    let data = new FormData();
    const config = {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    }
    data.append('raw', options.file);
    request.post(url, data, config).then(res => {
        options.success && options.success(res);
    }).catch(e => {
        options.fail && options.fail(e);
    })
}

// 修复图片
export function reviveImage(model, data) {
    const url = AiPath + '/models/revive/image/' + model;
    return request({
        url: url,
        method: 'post',
        data: data
    })
}


// 修复视频
export function reviveVideo(model, data) {
    const url = AiPath + '/models/revive/video/' + model;
    return request({
        url: url,
        method: 'post',
        data: data
    })
}

// 获取修复配置
export function reviveConfig() {
    return request({
        url: AiPath + '/model/config/revive',
        method: 'get'
    })
}

// 获取指定的任务进度
export function taskConfig(model) {
    return request({
        url: AiPath + '/model/config/' + model,
        method: 'get'
    })
}

export function RawImagePath(model, name) {
    // 获取上传的图片的地址
    return AiPath + '/resources/models/' + model + '/revive/old/' + name;
}

export function UsedVideoPath(model, name) {
    // 获取生成的视频的地址
    return AiPath + '/resources/models/' + model + '/used/video/' + name;
}

export function UsedImagePath(model, name) {
    // 获取生成的图片的地址
    return AiPath + '/resources/models/' + model + '/used/image/' + name;
}

export function UsedImageList(model) {
    // 获取生成的图片的地址
    return request({
        url: AiPath + '/resources/models/' + model + '/used/image',
        method: 'get'
    })
}

export function UsedVideoList(model) {
    // 获取生成的视频的地址
    return request({
        url: AiPath + '/resources/models/' + model + '/used/video',
        method: 'get'
    })
}

export function UsedAudioPath(model, name) {
    return AiPath + '/resources/models/' + model + '/used/audio/' + name;
}

export function ExamplePath(model) {
    return AiPath + '/models/example/facechain/' + model;
}

// 查询用户资源列表
export function listResource(query) {
    return request({
        url: '/resource/list',
        method: 'get',
        params: query
    })
}

// 查询用户资源详细
export function getResource(id) {
    return request({
        url: '/resource/' + id,
        method: 'get'
    })
}

// 新增用户资源
export function addResource(data) {
    return request({
        url: '/resource',
        method: 'post',
        data: data
    })
}

// 修改用户资源
export function updateResource(data) {
    return request({
        url: '/resource',
        method: 'put',
        data: data
    })
}

// 删除用户资源
export function delResource(id) {
    return request({
        url: '/resource/' + id,
        method: 'delete'
    })
}


export function testFace(model, data) {
    const url = AiPath + '/models/test/face/' + model;
    return request({
        url: url,
        method: 'post',
        data: data
    })
}
