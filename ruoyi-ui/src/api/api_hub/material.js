import request from '@/utils/request'

// 查询素材管理列表
export function listLib(query) {
    return request({
        url: '/material/lib/list',
        method: 'get',
        params: query
    })
}

// 查询素材管理详细
export function getLib(id) {
    return request({
        url: '/material/lib/' + id,
        method: 'get'
    })
}

// 新增素材管理
export function addLib(data) {
    return request({
        url: '/material/lib',
        method: 'post',
        data: data
    })
}

// 修改素材管理
export function updateLib(data) {
    return request({
        url: '/material/lib',
        method: 'put',
        data: data
    })
}

// 删除素材管理
export function delLib(id) {
    return request({
        url: '/material/lib/' + id,
        method: 'delete'
    })
}
