import COS from 'cos-js-sdk-v5';

// UUID
export function NewGuid() {
    return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c =>
        (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
    );
}

// const Bucket = 'shuziyunxie-1313466856';
const Bucket = 'ai-cloud-1313466856';
const Region = 'ap-guangzhou';

const cos = new COS({
    // SecretId: 'AKIDVwFLC0750reOBqbOD0siZRNC3se2TtFd',
    // SecretKey: 'U0X4nV1OFtKXWHF1av53ndHl3B7tV2RO',
    SecretId: 'AKIDiJm32sX2KPfqrgIi0MfnjJuOgljYAZki',
    SecretKey: 'oPqHYcVCk5C6pLdiZFixnX6h7MRKzRio'
});

// 上传到腾讯云从方法，只能单个上传，没有处理多个上传，如果有需要在处理吧 2024-08-28
export const uploadChange = (option) => {
    const files = option.files;
    option.folder = option.folder || 'otherfile';
    const uploadFileList = [...files].map((file) => {
        return {
            Bucket,
            Region,
            Key: option.folder + '/' + option.fileName,
            Body: file,
        }
    });
    return cos.uploadFiles({
        files: uploadFileList,
        SliceSize: 1024 * 1024 * 5,    /* 设置大于10MB采用分块上传 */
    }).then(response => {
        return Promise.resolve(response.files[0])
    }, err => {
        console.log('eer', err)
    })
}
