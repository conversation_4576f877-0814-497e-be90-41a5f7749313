import request from "@/utils/request";

// 查询AZX轮播图保存类列表
export function listCarousel(data) {
    return request({
        url: "/ai/carousel/list",
        method: "get",
        params: data,
    });
}

// 查询AZX轮播图保存类详细
export function getCarousel(id) {
    return request({
        url: "/ai/carousel/" + id,
        method: "get",
    });
}

// 新增AZX轮播图保存类
export function addCarousel(data) {
    return request({
        url: "/ai/carousel/save",
        method: "post",
        data: data,
    });
}

// // 修改AZX轮播图保存类
// export function updateCarousel(data) {
//     return request({
//         url: "/ai/carousel",
//         method: "put",
//         data: data,
//     });
// }

// 删除AZX轮播图保存类
export function delCarousel(data) {
    return request({
        url: "/ai/carousel/delete",
        method: "post",
        data: data,
    });
}
