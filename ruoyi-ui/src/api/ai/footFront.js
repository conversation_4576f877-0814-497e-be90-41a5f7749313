import request from '@/utils/request'

// 查询首页跳转配置列表
export function listFront(query) {
  return request({
    url: '/ai/front/list',
    method: 'get',
    params: query
  })
}

// 查询首页跳转配置详细
export function getFront(id) {
  return request({
    url: '/ai/front/' + id,
    method: 'get'
  })
}

// 新增首页跳转配置
export function addFront(data) {
  return request({
    url: '/ai/front',
    method: 'post',
    data: data
  })
}

// 修改首页跳转配置
export function updateFront(data) {
  return request({
    url: '/ai/front',
    method: 'put',
    data: data
  })
}

// 删除首页跳转配置
export function delFront(id) {
  return request({
    url: '/ai/front/' + id,
    method: 'delete'
  })
}
