import request from '@/utils/request'

// 查询AI分类列表
export function listCategories(query) {
  return request({
    url: '/ai/categories/list',
    method: 'get',
    params: query
  })
}

// 查询AI分类详细
export function getCategories(categoryId) {
  return request({
    url: '/ai/categories/' + categoryId,
    method: 'get'
  })
}

// 新增AI分类
export function addCategories(data) {
  return request({
    url: '/ai/categories',
    method: 'post',
    data: data
  })
}

// 修改AI分类
export function updateCategories(data) {
  return request({
    url: '/ai/categories',
    method: 'put',
    data: data
  })
}

// 删除AI分类
export function delCategories(categoryId) {
  return request({
    url: '/ai/categories/' + categoryId,
    method: 'delete'
  })
}
