import request from '@/utils/request'

// 查询模型标签关联列表
export function listTags(query) {
  return request({
    url: '/ai/models/tags/list',
    method: 'get',
    params: query
  })
}

// 查询模型标签关联详细
export function getTags(id) {
  return request({
    url: '/ai/models/tags/' + id,
    method: 'get'
  })
}

// 新增模型标签关联
export function addTags(data) {
  return request({
    url: '/ai/models/tags',
    method: 'post',
    data: data
  })
}

// 修改模型标签关联
export function updateTags(data) {
  return request({
    url: '/ai/models/tags',
    method: 'put',
    data: data
  })
}

// 删除模型标签关联
export function delTags(id) {
  return request({
    url: '/ai/models/tags/' + id,
    method: 'delete'
  })
}

// 根据modelsId查询模型标签关联列表
export function listTagsByModelsId(modelsId) {
  return request({
    url: '/ai/models/tags/getTagsByModelsId/' + modelsId,
    method: 'get'
  })
}