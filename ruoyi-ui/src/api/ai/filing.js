import request from '@/utils/request'

// 查询模型备案列表
export function listFiling(query) {
  return request({
    url: '/ai/filing/list',
    method: 'get',
    params: query
  })
}

// 查询模型备案详细
export function getFiling(filingId) {
  return request({
    url: '/ai/filing/' + filingId,
    method: 'get'
  })
}

// 新增模型备案
export function addFiling(data) {
  return request({
    url: '/ai/filing',
    method: 'post',
    data: data
  })
}

// 修改模型备案
export function updateFiling(data) {
  return request({
    url: '/ai/filing',
    method: 'put',
    data: data
  })
}

// 删除模型备案
export function delFiling(filingId) {
  return request({
    url: '/ai/filing/' + filingId,
    method: 'delete'
  })
}
