import request from '@/utils/request'

// 查询模型标签列表
export function listTags(query) {
  return request({
    url: '/ai/tags/list',
    method: 'get',
    params: query
  })
}

// 查询模型标签详细
export function getTags(tagId) {
  return request({
    url: '/ai/tags/' + tagId,
    method: 'get'
  })
}

// 新增模型标签
export function addTags(data) {
  return request({
    url: '/ai/tags',
    method: 'post',
    data: data
  })
}

// 修改模型标签
export function updateTags(data) {
  return request({
    url: '/ai/tags',
    method: 'put',
    data: data
  })
}

// 删除模型标签
export function delTags(tagId) {
  return request({
    url: '/ai/tags/' + tagId,
    method: 'delete'
  })
}
