import request from "@/utils/request";

// 查询Ai模型列表
export function listModels(query) {
    return request({
        url: "/ai/models/list",
        method: "get",
        params: query,
    });
}

// 查询Ai模型详细
export function getModels(id) {
    return request({
        url: "/ai/models/" + id,
        method: "get",
    });
}

// 新增Ai模型
export function addModels(data) {
    return request({
        url: "/ai/models",
        method: "post",
        data: data,
    });
}

// 修改Ai模型
export function updateModels(data) {
    return request({
        url: "/ai/models",
        method: "put",
        data: data,
    });
}

// 删除Ai模型
export function delModels(id) {
    return request({
        url: "/ai/models/" + id,
        method: "delete",
    });
}

// 增加浏览量
export function incrViewnum(modelId) {
    return request({
        url: "/ai/models/" + modelId + "/view",
        method: "get",
    });
}
// AI模型查重
export function checkModelNameAPI(aiModelName) {
    return request({
        url: "/ai/models/checkModelName/" + aiModelName,
        method: "get",
    });
}
// AI模型模糊搜索
export function fuzzyParamModelsAPI(query) {
    return request({
        url: "/ai/models/fuzzyParam",
        method: "get",
        params: query,
    });
}
