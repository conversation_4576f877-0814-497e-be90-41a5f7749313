import request from '@/utils/request'

// 查询计费策略列表
export function listPrice(query) {
  return request({
    url: '/ai/price/list',
    method: 'get',
    params: query
  })
}

// 查询计费策略详细
export function getPrice(priceId) {
  return request({
    url: '/ai/price/' + priceId,
    method: 'get'
  })
}

// 新增计费策略
export function addPrice(data) {
  return request({
    url: '/ai/price',
    method: 'post',
    data: data
  })
}

// 修改计费策略
export function updatePrice(data) {
  return request({
    url: '/ai/price',
    method: 'put',
    data: data
  })
}

// 删除计费策略
export function delPrice(priceId) {
  return request({
    url: '/ai/price/' + priceId,
    method: 'delete'
  })
}
