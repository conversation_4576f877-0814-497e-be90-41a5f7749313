import Cookies from "js-cookie";

const TokenKey = "Admin-Token";
const ApiKey = "Api-Key";
const ModelNameKey = "Model-Name";
const UserInfoNameKey = "User-Name";

export function getToken() {
    return Cookies.get(TokenKey) || localStorage.getItem("token");
}

export function setToken(token) {
    return Cookies.set(Token<PERSON>ey, token);
}

export function removeToken() {
    return Cookies.remove(TokenKey);
}

export function getModelName() {
    return Cookies.get(ModelNameKey);
}

export function setModelName(name) {
    return Cookies.set(ModelNameKey, name);
}

export function removeModelName() {
    return Cookies.remove(ModelNameKey);
}

export function getUserInfo() {
    return Cookies.get(UserInfoNameKey);
}

export function setUserInfo(userInfo) {
    return Cookies.set(UserInfoNameKey, userInfo);
}

export function removeUserInfo() {
    return Cookies.remove(UserInfoNameKey);
}

export function getApiKey() {
    return Cookies.get(Api<PERSON><PERSON>);
}

export function setApi<PERSON>ey(key) {
    return Cookies.set(<PERSON><PERSON><PERSON><PERSON>, key);
}

export function removeApiKey() {
    return Cookies.remove(ApiKey);
}
